import React, {
  createContext,
  useContext,
  useCallback,
  useState,
  useRef,
} from "react";

// eslint-disable-next-line import/no-restricted-paths
import type { HospitalInfoPageType } from "@/features/start/types";
import type { RefObject } from "react";

export type PreviewDataUpdate = {
  type: "HOSPITAL_INFO" | "STAFF_CREATE" | "STAFF_EDIT";
  data: unknown;
  pageType?: HospitalInfoPageType;
};

type PreviewDataContextType = {
  triggerPreviewUpdate: (update: PreviewDataUpdate) => void;
  isPreviewUpdatePending: boolean;
};

const PreviewDataContext = createContext<PreviewDataContextType | undefined>(
  undefined,
);

type Props = {
  children: React.ReactNode;
  iframeRef: RefObject<HTMLIFrameElement | null>;
  previewKey: number;
};

export const PreviewDataProvider: React.FC<Props> = ({
  children,
  iframeRef,
  previewKey,
}) => {
  const [isPreviewUpdatePending, setIsPreviewUpdatePending] = useState(false);
  const pendingUpdatesRef = useRef<PreviewDataUpdate[]>([]);

  const triggerPreviewUpdate = useCallback((update: PreviewDataUpdate) => {
    // Thêm update vào queue
    pendingUpdatesRef.current.push(update);
    setIsPreviewUpdatePending(true);
  }, []);

  const value: PreviewDataContextType = {
    triggerPreviewUpdate,
    isPreviewUpdatePending,
  };

  // Expose pending updates và clear function để MobilePreview có thể sử dụng
  React.useEffect(() => {
    if (iframeRef.current) {
      (iframeRef.current as any).__pendingUpdates = pendingUpdatesRef.current;
      (iframeRef.current as any).__clearPendingUpdates = () => {
        pendingUpdatesRef.current = [];
        setIsPreviewUpdatePending(false);
      };
    }
  }, [iframeRef, previewKey]);

  return (
    <PreviewDataContext.Provider value={value}>
      {children}
    </PreviewDataContext.Provider>
  );
};

export const usePreviewData = () => {
  const context = useContext(PreviewDataContext);
  if (context === undefined) {
    throw new Error("usePreviewData must be used within a PreviewDataProvider");
  }
  return context;
};

// Hook để lấy pending updates từ MobilePreview
export const usePendingPreviewUpdates = (
  iframeRef: RefObject<HTMLIFrameElement | null>,
) => {
  const getPendingUpdates = useCallback((): PreviewDataUpdate[] => {
    if (iframeRef.current && (iframeRef.current as any).__pendingUpdates) {
      return (iframeRef.current as any).__pendingUpdates;
    }
    return [];
  }, [iframeRef]);

  const clearPendingUpdates = useCallback(() => {
    if (iframeRef.current && (iframeRef.current as any).__clearPendingUpdates) {
      (iframeRef.current as any).__clearPendingUpdates();
    }
  }, [iframeRef]);

  return { getPendingUpdates, clearPendingUpdates };
};
