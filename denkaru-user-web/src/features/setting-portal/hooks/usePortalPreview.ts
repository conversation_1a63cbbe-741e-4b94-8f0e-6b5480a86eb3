import { useCallback, useEffect, useState } from "react";

import type { RefObject } from "react";

export const usePortalPreview = (
  iframeRef: RefObject<HTMLIFrameElement | null>,
  previewKey?: number,
) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const iframeCurrent = iframeRef.current;
    if (!iframeCurrent) {
      return;
    }

    const childOrigin = process.env.NEXT_PUBLIC_BOOKING_CLIENT_URL ?? "*";

    const handleMessage = (event: MessageEvent) => {
      if (childOrigin !== "*" && event.origin !== childOrigin) {
        return;
      }
      if (event.source !== iframeCurrent.contentWindow) {
        return;
      }

      if (event.data?.type === "IFRAME_READY") {
        if (isConnected) return;
        setIsConnected(true);
        setIsLoading(false);
        iframeCurrent.contentWindow?.postMessage(
          { type: "PARENT_READY_ACK" },
          childOrigin,
        );
      }
    };

    window.addEventListener("message", handleMessage);

    const timeoutId = setTimeout(() => {
      if (!isConnected) {
        setIsLoading(false);
      }
    }, 1000);

    return () => {
      window.removeEventListener("message", handleMessage);
      clearTimeout(timeoutId);
      setIsConnected(false);
      setIsLoading(true);
    };
  }, [iframeRef, previewKey, isConnected]);

  const postData = useCallback(
    (data: unknown) => {
      const iframeCurrent = iframeRef.current;
      const childOrigin = process.env.NEXT_PUBLIC_BOOKING_CLIENT_URL ?? "*";
      if (isConnected && iframeCurrent?.contentWindow) {
        iframeCurrent.contentWindow.postMessage(
          {
            type: "SET_DATA",
            payload: JSON.stringify(data),
          },
          childOrigin,
        );
      }
    },
    [isConnected, iframeRef],
  );

  return { postData, isLoading, isConnected };
};

// Hook đơn giản chỉ để kiểm tra connection status
export const usePortalPreviewConnection = (
  iframeRef: RefObject<HTMLIFrameElement | null>,
  previewKey?: number,
) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const iframeCurrent = iframeRef.current;
    if (!iframeCurrent) {
      return;
    }

    const childOrigin = process.env.NEXT_PUBLIC_BOOKING_CLIENT_URL ?? "*";

    const handleMessage = (event: MessageEvent) => {
      if (childOrigin !== "*" && event.origin !== childOrigin) {
        return;
      }
      if (event.source !== iframeCurrent.contentWindow) {
        return;
      }

      if (event.data?.type === "IFRAME_READY") {
        if (isConnected) return;
        setIsConnected(true);
        setIsLoading(false);
        iframeCurrent.contentWindow?.postMessage(
          { type: "PARENT_READY_ACK" },
          childOrigin,
        );
      }
    };

    window.addEventListener("message", handleMessage);

    const timeoutId = setTimeout(() => {
      if (!isConnected) {
        setIsLoading(false);
      }
    }, 1000);

    return () => {
      window.removeEventListener("message", handleMessage);
      clearTimeout(timeoutId);
      setIsConnected(false);
      setIsLoading(true);
    };
  }, [iframeRef, previewKey, isConnected]);

  return { isLoading, isConnected };
};
