import React, { useState, useEffect, useRef, type RefObject } from "react";

import { Flex } from "antd";
import { useRouter } from "next/router";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Checkbox } from "@/components/ui/Checkbox";
import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { Loading } from "@/components/ui/Loading";
import { Button } from "@/components/ui/NewButton";
import { TextAreaInput } from "@/components/ui/TextAreaInput";
import { TextInput } from "@/components/ui/TextInput";
import { Form } from "@/components/functional/Form";
// eslint-disable-next-line import/no-restricted-paths
import { HospitalInfoPageType } from "@/features/start/types";
import { usePortal } from "@/providers/PortalProvider";
import { useGetApiKaGetListMstQuery } from "@/apis/gql/operations/__generated__/ka";

import { usePortalStaffEditForm } from "../../hooks/usePortalStaffEditForm";
import { usePortalPreview } from "../../hooks/usePortalPreview";
import { PortalConfirmModal } from "../PortalConfirmModal";
import { useGetPortalStaff } from "../../hooks/useGetPortalStaff";

import { PortalStaffDeleteModal } from "./PortalStaffDeleteModal";
import { StaffImageUploader } from "./StaffImageUploader";

import type { GetPortalHospitalStaffQuery } from "@/apis/gql/operations/__generated__/portal-staff";
import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";

const Wrapper = styled.div`
  font-size: 14px;
  position: relative;
`;

const Title = styled.p`
  font-size: 16px;
  padding: 20px;
  line-height: 1;
  border-bottom: 1px solid #e0e6ec;
  font-weight: bold;
`;

const Heading = styled.p`
  font-size: 16px;
  line-height: 1;
  font-weight: bold;
`;

const Section = styled.div`
  margin: 20px 0px;

  &:first-child {
    margin-top: 0px;
  }

  &:last-child {
    margin-bottom: 80px;
  }
`;

const FormWrapper = styled(Form)`
  padding: 20px;
  max-height: calc(100vh - 145px);
  overflow-y: auto;
`;

const HorizontalDivider = styled.div`
  border: 1px solid #e0e6ec;
`;

const InputWrapper = styled.div`
  margin: 20px 0;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const ErrorText = styled(CommonErrorText)`
  margin-top: 4px;
`;

const StyledTextAreaInput = styled(TextAreaInput)`
  width: 520px;
  height: 120px !important;
  resize: none !important;
`;

const ButtonWrapper = styled.div`
  border-top: 1px solid #e0e6ec;
  display: flex;
  gap: 20px;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #ffffff;
  width: 100%;
  padding: 20px;
  border-radius: 0 0 12px 12px;

  button:last-child {
    margin-left: 8px;
  }
`;

const DeleteButton = styled(Button)`
  font-weight: normal;
  margin-left: 8px;

  span {
    color: #e74c3c;
  }
`;

const CheckboxWrapper = styled.div`
  margin-top: 8px;
`;

const StyledTextInput = styled(TextInput)<{ $width: number }>`
  width: ${({ $width }) => `${$width}px`};
`;

type Props = {
  hospital:
    | NonNullable<
        GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
      >
    | null
    | undefined;
  staffInfo: NonNullable<
    GetPortalHospitalStaffQuery["getPortalHospitalStaff"]["portalHospitalStaff"]
  >;
  isActive: boolean;
  iframeRef?: RefObject<HTMLIFrameElement | null>;
  onTogglePreview?: () => void;
  previewKey?: number;
};

export const PortalStaffEditForm: React.FC<Props> = ({
  hospital,
  staffInfo,
  isActive,
  iframeRef,
  onTogglePreview,
  previewKey,
}) => {
  const {
    onSubmit,
    errors,
    control,
    isSubmitting,
    isAgreementOpen,
    handleAgreementClose,
    handleAgreementOpen,
    isDirty,
    getValues,
    isValid,
  } = usePortalStaffEditForm(staffInfo);

  const { push } = useRouter();
  const { staffs } = useGetPortalStaff();
  const { tags } = usePortal();
  const { data: treatmentCategories } = useGetApiKaGetListMstQuery({
    variables: {
      isDeleted: 0,
    },
    fetchPolicy: "no-cache",
  });

  const dummyRef = useRef<HTMLIFrameElement>(null);
  const { postData, isLoading, isConnected } = usePortalPreview(
    iframeRef ?? dummyRef,
    previewKey,
  );

  const [shouldPostData, setShouldPostData] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const formId = "edit-staff";

  const handlePreview = () => {
    onTogglePreview?.();
    setShouldPostData(true);
  };

  useEffect(() => {
    if (shouldPostData && isConnected && hospital) {
      const formDatas = getValues();

      // Create updated staff data for preview
      const updatedStaff = {
        hospitalStaffId: staffInfo.hospitalStaffId,
        name: formDatas?.name ?? "",
        description: formDatas?.description ?? "",
        specialistDetail: formDatas?.specialistDetail ?? "",
        experienceDetail: formDatas?.experienceDetail ?? "",
        isDirector: formDatas?.isDirector ?? false,
        order: staffInfo.order,
        pictures:
          formDatas?.files?.map((file, index) => ({
            pictId: file.uid ? parseInt(file.uid) : index,
            hospitalStaffId: staffInfo.hospitalStaffId,
            pictureDetail: {
              fileName: file.name,
              filepath: file.url || file.thumbUrl || "",
            },
          })) ?? [],
      };

      const currentStaffs =
        staffs?.map((staff) => ({
          ...staff,
          pictures: staff.files?.map((file, index) => ({
            fileId: index,
            pictureDetail: {
              fileName: file.originalFileName,
              filepath: file.s3Key,
            },
          })),
        })) ?? [];
      const hospitalStaffsWithUpdate = currentStaffs.map((staff) =>
        staff.hospitalStaffId === staffInfo.hospitalStaffId
          ? updatedStaff
          : staff,
      );

      // Send the updated staff data for preview
      postData({
        pageType: HospitalInfoPageType.STAFFS,
        ...hospital,
        tags: (hospital.tags ?? []).map(({ tagId }) => {
          const tag = tags.find((tag) => tag.tagId === tagId);
          return {
            tagId,
            name: tag?.name ?? "",
          };
        }),
        hospitalStaffs: hospitalStaffsWithUpdate,
        treatmentCategories: (
          treatmentCategories?.getApiKaGetListMst?.data?.departments ?? []
        ).map(({ kaName, id }) => ({
          name: kaName,
          treatmentCategoryId: id,
        })),
      });

      setShouldPostData(false);
    }
  }, [
    shouldPostData,
    isConnected,
    getValues,
    postData,
    staffInfo.hospitalStaffId,
    staffInfo.order,
    hospital,
    staffs,
    tags,
    treatmentCategories,
  ]);

  return (
    <>
      {isSubmitting && <Loading isLoading />}
      <Wrapper>
        <Title>医師情報の編集</Title>
        <FormWrapper id={formId} onSubmit={onSubmit}>
          <Section>
            <Heading>基本情報</Heading>
            <InputWrapper>
              <StyledLabel label="医師名" required />
              <Flex gap={8} align="center">
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <StyledTextInput
                      {...field}
                      hasError={!!errors.name}
                      placeholder="医師名を入力してください。"
                      $width={240}
                      shouldTrim
                    />
                  )}
                  rules={{
                    required: "医師名を入力してください",
                    maxLength: {
                      value: 30,
                      message: "30文字以内で入力してください。",
                    },
                  }}
                />
                <DeleteButton
                  varient="inline"
                  onClick={() => setIsDeleteModalOpen(true)}
                >
                  医師情報を削除
                </DeleteButton>
              </Flex>
              {errors.name && <ErrorText>{errors.name.message}</ErrorText>}

              <Controller
                name="isDirector"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <CheckboxWrapper>
                    <Checkbox
                      value={value}
                      onChange={onChange}
                      checked={value === true}
                    >
                      院長
                    </Checkbox>
                  </CheckboxWrapper>
                )}
              />
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="紹介用画像" />

              <Controller
                name="files"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <StaffImageUploader
                    files={value}
                    description="推奨画像サイズ640x640ピクセル"
                    maxCount={1}
                    handleChangeEvent={onChange}
                  />
                )}
              />
            </InputWrapper>

            <InputWrapper>
              <StyledLabel label="経歴" />

              <Controller
                name="experienceDetail"
                control={control}
                render={({ field }) => (
                  <StyledTextAreaInput
                    {...field}
                    placeholder="経歴を入力してください。"
                    hasError={!!errors.experienceDetail}
                  />
                )}
                rules={{
                  maxLength: {
                    value: 1000,
                    message: "1000文字以内で入力してください。",
                  },
                }}
              />
              {errors.experienceDetail && (
                <ErrorText>{errors.experienceDetail.message}</ErrorText>
              )}
            </InputWrapper>
          </Section>

          <HorizontalDivider />

          <Section>
            <StyledLabel label="資格・所属学会情報" />
            <Controller
              name="specialistDetail"
              control={control}
              render={({ field }) => (
                <StyledTextAreaInput
                  {...field}
                  placeholder="資格・所属学会を入力してください。"
                  hasError={!!errors.specialistDetail}
                />
              )}
              rules={{
                maxLength: {
                  value: 1000,
                  message: "1000文字以内で入力してください。",
                },
              }}
            />
            {errors.specialistDetail && (
              <ErrorText>{errors.specialistDetail.message}</ErrorText>
            )}
          </Section>

          <HorizontalDivider />

          <Section>
            <StyledLabel label="紹介文" />
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <StyledTextAreaInput
                  {...field}
                  placeholder="医師の紹介や患者へのメッセージなどを入力してください。"
                  hasError={!!errors.description}
                />
              )}
              rules={{
                maxLength: {
                  value: 1000,
                  message: "1000文字以内で入力してください。",
                },
              }}
            />
            {errors.description && (
              <ErrorText>{errors.description.message}</ErrorText>
            )}
          </Section>
        </FormWrapper>
        <ButtonWrapper>
          <Button
            varient="tertiary"
            onClick={() => push("/setting/portal/staff")}
          >
            キャンセル
          </Button>
          <div>
            <Button
              varient="secondary"
              disabled={!isDirty || !isValid || isLoading || isSubmitting}
              onClick={handlePreview}
            >
              プレビュー更新
            </Button>
            <Button
              varient="primary"
              htmlType={isActive ? "button" : "submit"}
              form={formId}
              disabled={isSubmitting}
              onClick={() => {
                if (!isActive) {
                  return;
                }
                handleAgreementOpen();
              }}
            >
              保存
            </Button>
          </div>
        </ButtonWrapper>
      </Wrapper>
      <PortalStaffDeleteModal
        hospitalStaffId={staffInfo.hospitalStaffId}
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
      />

      <PortalConfirmModal
        formId={formId}
        title="GMOクリニック・マップ連携情報の編集"
        onClose={handleAgreementClose}
        isOpen={isAgreementOpen}
        isSubmitting={isSubmitting}
      />
    </>
  );
};
