/* eslint-disable import/no-restricted-paths */
import React from "react";

import styled from "styled-components";
import Link from "next/link";

import { TaskTable } from "@/features/task/ui/list/TaskListTable";
import { TaskSortType } from "@/apis/gql/generated/types";
import { useTaskListTemplate } from "@/features/task/hooks/useTaskListTemplate";
import { TASK_HOME_PAGE_SIZE } from "@/features/task/constant";

const TaskSectionContainer = styled.div`
  min-height: 400px;
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const Title = styled.span`
  font-size: 16px;
  color: #243544;
  font-weight: bold;
  cursor: default;
`;

const ViewAllLink = styled(Link)`
  color: #007aff;
  font-size: 14px;
  text-decoration: none;
  font-family: "NotoSansJP";
`;

const TaskTableWrapper = styled.div`
  height: 100%;
`;

export const TaskSection = () => {
  const { taskList, isLoadingTasks } = useTaskListTemplate(
    TASK_HOME_PAGE_SIZE,
    true,
  );

  return (
    <TaskSectionContainer>
      <Header>
        <Title>タスク</Title>
        <ViewAllLink href="/task">すべて見る</ViewAllLink>
      </Header>
      <TaskTableWrapper>
        <TaskTable
          tasks={taskList}
          loading={isLoadingTasks}
          isLoadingMore={false}
          sortOrder={undefined}
          sortType={TaskSortType.CreatedDate}
          onSortTasks={() => {}}
          onLoadMore={() => {}}
        />
      </TaskTableWrapper>
    </TaskSectionContainer>
  );
};
