import React, { useEffect, useState } from "react";

import styled from "styled-components";
import dayjs from "dayjs";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { Checkbox } from "@/components/ui/Checkbox";
import { ResizableHorizontalDivider } from "@/components/ui/ResizableHorizontalDivider";
import { useEditReadSystemNotificationsMutation } from "@/apis/gql/operations/__generated__/system-notification";

import { NotifyBadge } from "./NotifyBadge";

import type { SystemNotification } from "@/apis/gql/generated/types";

type FormattedNotification = SystemNotification & { isRead: boolean };

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: FormattedNotification[];
  refetch: () => void;
}

const ModalContent = styled.div`
  display: flex;
  height: 600px;
  background-color: #fff;
`;

const SidebarHeader = styled.div`
  height: 44px;
  padding: 8px;
  border-bottom: 1px solid #e2e3e5;
  background-color: #f8f9fa;
`;

const SelectAllCheckbox = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 14px;
  color: #243544;
  font-family: "NotoSansJP";
`;

const MarkAsReadButton = styled(Button)`
  width: 220px;
  height: 28px;
  padding: 7px 12px;
  margin-right: 8px;
`;

const NotificationList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0;
`;

const NotificationItem = styled.div<{ $isSelected: boolean }>`
  display: flex;
  align-items: center;
  height: 80px;
  padding: 8px;
  border-bottom: 1px solid #e2e3e5;
  cursor: pointer;
  background-color: ${({ $isSelected }) => ($isSelected ? "#eaf0f5" : "#fff")};

  &:hover {
    background-color: #eaf0f5;
  }
`;

const CheckboxWrapper = styled(Checkbox)`
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 8px;
  width: 24px;
`;

const NotificationItemContent = styled.div`
  flex: 1;
`;

const NotificationItemHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DateText = styled.span`
  color: #6a757d;
  font-size: 16px;
  font-family: "Roboto";
  line-height: 1;
`;

const NotificationTitle = styled.div<{ $isRead?: boolean }>`
  color: #243544;
  font-size: 14px;
  font-weight: ${({ $isRead }) => ($isRead ? "normal" : "bold")};
`;

const DetailArea = styled.div`
  flex: 1;
  padding: 28px 20px;
  overflow-y: auto;
`;

const DetailTitle = styled.div`
  font-size: 18px;
  font-weight: bold;
  color: #243544;
  line-height: 1;
  padding: 12px 0;
  border-bottom: 1px solid #e2e3e5;
`;

const DetailContent = styled.div`
  font-size: 14px;
  padding-top: 12px;
  line-height: 1.71;
  letter-spacing: normal;
  text-align: left;
  color: #243544;
`;

export const NotificationModal: React.FC<NotificationModalProps> = ({
  isOpen,
  onClose,
  notifications,
  refetch,
}) => {
  const [editReadSystemNotifications, { loading }] =
    useEditReadSystemNotificationsMutation();
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>(
    [],
  );
  const [selectedNotificationId, setSelectedNotificationId] =
    useState<number>(0);
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    if (notifications?.[0] && isOpen && selectedNotifications.length === 0) {
      setSelectedNotificationId(notifications[0].systemNotificationID);
      setSelectedNotifications([notifications[0].systemNotificationID]);
    }
  }, [isOpen, notifications, selectedNotifications.length]);

  useEffect(() => {
    if (!isOpen) {
      setSelectedNotifications([]);
      setSelectedNotificationId(0);
      setSelectAll(false);
    }
  }, [isOpen]);

  const handleCheckboxChange = (id: number) => {
    setSelectedNotifications((prev) =>
      prev.includes(id)
        ? prev.filter((notifId) => notifId !== id)
        : [...prev, id],
    );
  };

  const handleSelectAllChange = () => {
    if (selectAll) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(
        notifications?.map((n) => n.systemNotificationID) ?? [],
      );
    }
    setSelectAll(!selectAll);
  };

  const handleNotificationClick = (id: number) => {
    setSelectedNotificationId(id);
    let newSelectedNotifications = [...selectedNotifications];
    if (newSelectedNotifications.includes(id)) {
      newSelectedNotifications = newSelectedNotifications.filter(
        (notifId) => notifId !== id,
      );
    } else {
      newSelectedNotifications = [id, ...newSelectedNotifications];
    }
    if (newSelectedNotifications.length === notifications?.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
    setSelectedNotifications(newSelectedNotifications);
  };

  const selectedNotification = notifications?.find(
    (n) => n.systemNotificationID === selectedNotificationId,
  );

  const handleMarkAsRead = () => {
    editReadSystemNotifications({
      variables: {
        input: selectedNotifications,
      },
      onCompleted: () => {
        refetch();
      },
    });
  };

  return (
    <Modal
      isOpen={isOpen}
      onCancel={onClose}
      title="お知らせ"
      centered
      centerFooterContent
      width={1000}
      footer={[
        <Button key="close" onClick={onClose} varient="tertiary">
          閉じる
        </Button>,
      ]}
      destroyOnClose
    >
      <ModalContent>
        <ResizableHorizontalDivider width={380} minWidth={380}>
          <SidebarHeader>
            <SelectAllCheckbox>
              <CheckboxWrapper
                checked={selectAll}
                onChange={handleSelectAllChange}
              />
              <MarkAsReadButton
                varient="standard-sr"
                disabled={!selectedNotifications.length}
                onClick={handleMarkAsRead}
                loading={loading}
              >
                選択したお知らせを既読にする
              </MarkAsReadButton>
            </SelectAllCheckbox>
          </SidebarHeader>
          <NotificationList>
            {notifications?.map((notification) => (
              <NotificationItem
                key={notification.systemNotificationID}
                $isSelected={
                  notification.systemNotificationID === selectedNotificationId
                }
                onClick={() =>
                  handleNotificationClick(notification.systemNotificationID)
                }
              >
                <>
                  <CheckboxWrapper
                    checked={selectedNotifications.includes(
                      notification.systemNotificationID,
                    )}
                    onChange={() =>
                      handleCheckboxChange(notification.systemNotificationID)
                    }
                    onClick={(e) => e.stopPropagation()}
                  />
                  <NotificationItemContent>
                    <NotificationItemHeader>
                      <DateText>
                        {dayjs(notification.createdAt).format("YYYY/MM/DD")}
                      </DateText>
                      {notification.tags.map((tag) => (
                        <NotifyBadge key={tag} tag={tag} />
                      ))}
                    </NotificationItemHeader>
                    <NotificationTitle $isRead={notification.isRead}>
                      {notification.title}
                    </NotificationTitle>
                  </NotificationItemContent>
                </>
              </NotificationItem>
            ))}
          </NotificationList>
        </ResizableHorizontalDivider>

        <DetailArea>
          {selectedNotification && (
            <>
              <NotificationItemContent>
                <NotificationItemHeader>
                  <DateText>
                    {dayjs(selectedNotification.createdAt).format("YYYY/MM/DD")}
                  </DateText>
                  {selectedNotification.tags.map((tag) => (
                    <NotifyBadge key={tag} tag={tag} />
                  ))}
                </NotificationItemHeader>
                <DetailTitle>{selectedNotification.title}</DetailTitle>
              </NotificationItemContent>
              <DetailContent>
                <p
                  dangerouslySetInnerHTML={{
                    __html: selectedNotification.description,
                  }}
                ></p>
              </DetailContent>
            </>
          )}
        </DetailArea>
      </ModalContent>
    </Modal>
  );
};
