import React from "react";

import styled from "styled-components";

export enum NotificationType {
  IMPORTANT = 1,
  SERVICE = 2,
  CAMPAIGN = 3,
}

const BadgeContainer = styled.span<{
  $bgColor: string;
  $textColor: string;
  $padding: string;
  $border?: string;
}>`
  height: 20px;
  font-size: 12px;
  color: ${({ $textColor }) => $textColor};
  padding: ${({ $padding }) => $padding};
  background-color: ${({ $bgColor }) => $bgColor};
  border: ${({ $border }) => $border ?? "none"};
  border-radius: 2px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: default;
  span {
    line-height: 1;
  }
`;

const tagsConfigs = [
  {
    type: NotificationType.IMPORTANT,
    label: "重要",
    bgColor: "#e74c3c",
    textColor: "#fff",
    padding: "6px 16px 5px",
  },
  {
    type: NotificationType.SERVICE,
    label: "サービス改善",
    bgColor: "#fff",
    textColor: "#43a047",
    border: "1px solid #43a047",
    padding: "6px 8px 5px",
  },
  {
    type: NotificationType.CAMPAIGN,
    label: "キャンペーン",
    bgColor: "#fff",
    textColor: "#f39c12",
    border: "1px solid #f39c12",
    padding: "6px 16px 5px",
  },
];

interface NotifyBadgeProps {
  tag: NotificationType;
}

export const NotifyBadge: React.FC<NotifyBadgeProps> = ({ tag }) => {
  const tagConfig = tagsConfigs.find((config) => config.type === tag);

  if (!tagConfig) {
    return null;
  }

  return (
    <BadgeContainer
      key={tag}
      $bgColor={tagConfig.bgColor}
      $textColor={tagConfig.textColor}
      $padding={tagConfig.padding}
      $border={tagConfig.border}
    >
      <span>{tagConfig.label}</span>
    </BadgeContainer>
  );
};
