import React, { useMemo, useState } from "react";

import styled from "styled-components";
import dayjs from "dayjs";

import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { Button } from "@/components/ui/NewButton";

import { NotificationModal } from "./NotificationModal";
import { NotifyBadge } from "./NotifyBadge";

const NotifySectionContainer = styled.div`
  height: 230px;
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const Title = styled.h3`
  font-size: 16px;
  font-weight: 500;
  color: #243544;
  margin: 0;
  cursor: default;
  line-height: 1;
`;

const ViewAllButton = styled.button`
  color: #007aff;
  font-size: 14px;
  text-decoration: none;
  font-family: "NotoSansJP";
  line-height: 1;
  background: none;
  border: none;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
`;

const NotificationList = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const NotificationItem = styled.div`
  height: 54px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 4px;
  border-top: solid 1px #e2e3e5;

  &:last-child {
    border-bottom: solid 1px #e2e3e5;
  }
`;

const NotificationHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DateText = styled.span`
  color: #6a757d;
  font-size: 14px;
  font-family: "Roboto";
  line-height: 1;
  cursor: default;
`;

const NotificationTitle = styled.div<{ $isRead?: boolean }>`
  color: #243544;
  font-size: 14px;
  line-height: 1;
  font-weight: ${({ $isRead }) => ($isRead ? "normal" : "bold")};
  cursor: default;
`;

export const NotifySection = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    systemNotifications,
    readSystemNotifications,
    refetchReadSystemNotifications,
  } = usePatientContext();

  const formattedSystemNotifications = useMemo(() => {
    if (!systemNotifications || !readSystemNotifications) {
      return [];
    }
    const readIds = new Set(readSystemNotifications);
    return systemNotifications.map((notification) => ({
      ...notification,
      isRead: readIds.has(notification.systemNotificationID),
    }));
  }, [systemNotifications, readSystemNotifications]);

  const handleViewAllClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    if (refetchReadSystemNotifications) {
      refetchReadSystemNotifications();
    }
  };

  return (
    <NotifySectionContainer>
      <Header>
        <Title>GMOヘルステックからのお知らせ</Title>
        <Button onClick={handleViewAllClick} varient="inline">
          すべて見る
        </Button>
      </Header>
      <NotificationList>
        {!!formattedSystemNotifications?.length &&
          formattedSystemNotifications?.slice(0, 3).map((notification) => (
            <NotificationItem key={notification.systemNotificationID}>
              <NotificationHeader>
                <DateText>
                  {dayjs(notification.createdAt).format("YYYY/MM/DD")}
                </DateText>
                {notification.tags.map((tag) => (
                  <NotifyBadge key={tag} tag={tag} />
                ))}
              </NotificationHeader>
              <NotificationTitle $isRead={notification.isRead}>
                {notification.title}
              </NotificationTitle>
            </NotificationItem>
          ))}
      </NotificationList>
      {isModalOpen && (
        <NotificationModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          notifications={formattedSystemNotifications}
          refetch={refetchReadSystemNotifications}
        />
      )}
    </NotifySectionContainer>
  );
};
