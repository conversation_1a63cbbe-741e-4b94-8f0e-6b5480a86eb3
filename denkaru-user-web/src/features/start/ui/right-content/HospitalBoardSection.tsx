import React, { useState } from "react";

import styled from "styled-components";

import { Button } from "@/components/ui/NewButton";
import { useGetInhouseNoticeBoard } from "@/features/start/hooks/useGetInhouseNoticeBoard";
import { generateCalendarWeeks } from "@/features/start/utils/schedule";

import { HospitalBoardEditModal } from "./HospitalBoardEditModal";

import type { Dayjs } from "dayjs";

const HospitalBoardContainer = styled.div<{ $extraHeight?: boolean }>`
  height: ${({ $extraHeight }) => ($extraHeight ? "496px" : "420px")};
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const Title = styled.span`
  font-size: 16px;
  font-weight: 500;
  color: #243544;
  font-family: "NotoSansJP";
  cursor: default;
`;

const StyledButton = styled(Button)`
  width: 80px;
  height: 28px;
`;

const NotificationContent = styled.pre`
  white-space: pre-wrap;
  word-break: break-all;
  font-family: "Roboto";
  font-size: 14px;
  line-height: 1;
  color: #243544;
  margin: 0;
  height: 100%;
  overflow-y: auto;
  cursor: default;
`;

export const HospitalBoardSection = ({
  currentDate,
}: {
  currentDate: Dayjs;
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { content, refetch } = useGetInhouseNoticeBoard();

  const weeks = generateCalendarWeeks(currentDate);
  const weeksCount = weeks.length;

  const handleEditClick = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <HospitalBoardContainer $extraHeight={weeksCount === 6}>
        <Header>
          <Title>院内掲示板</Title>
          <StyledButton varient="secondary" onClick={handleEditClick}>
            編集
          </StyledButton>
        </Header>
        <NotificationContent>{content}</NotificationContent>
      </HospitalBoardContainer>

      <HospitalBoardEditModal
        isOpen={isModalOpen}
        initialContent={content?.trim() || ""}
        onClose={handleModalClose}
        refetch={refetch}
      />
    </>
  );
};
