import React from "react";

import styled from "styled-components";

import type { CustomEventProps } from "../../../types";

const EventCard = styled.div<{ $position: string }>`
  background-color: #d9f3f7;
  padding: 1px 4px;
  height: 16px;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  position: relative;
  z-index: 10;
  user-select: none;
  border-radius: 2px;

  /* Pseudo elements cho visual connection */
  ${(props) =>
    props.$position === "start" &&
    `
    &::after {
      content: '';
      position: absolute;
      right: -1px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: rgba(255,255,255,0.3);
    }
  `}

  ${(props) =>
    props.$position === "end" &&
    `
    &::before {
      content: '';
      position: absolute;
      left: -1px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: rgba(255,255,255,0.3);
    }
  `}

  /* Visual indication cho middle events */
  ${(props) =>
    props.$position === "middle" &&
    `
    opacity: 0.95;
    font-style: italic;
    
    &::before, &::after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background: rgba(255,255,255,0.2);
    }
    
    &::before {
      left: -1px;
    }
    
    &::after {
      right: -1px;
    }
  `}

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }

  &.appointment {
    line-height: 1;
  }
`;

export const CustomEvent: React.FC<CustomEventProps> = ({ event, onClick }) => {
  return (
    <EventCard
      $position={event.groupPosition || "start"}
      onClick={() => onClick?.(event)}
    >
      {event.title}
    </EventCard>
  );
};
