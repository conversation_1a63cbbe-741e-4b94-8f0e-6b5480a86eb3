import React, { useMemo, useRef, useState } from "react";

import { Calendar, momentLocalizer } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import moment from "moment";
import "moment/locale/ja";
import dayjs from "dayjs";
import styled from "styled-components";

import {
  isFirstOrLastOutsideMonthDate,
  getCalendarView,
  createInitialModalData,
  calculateRequiredWeeks,
  expandMultiDayEvents,
  assignEventLevels,
  getCalendarFormats,
} from "../../../utils/schedule";

import { ScheduleModal } from "./ScheduleModal";
import { CustomDateHeader } from "./CustomDateHeader";
import { CustomMonthView } from "./CustomMonthView";

import type { BigCalendarViewProps, BigCalendarEvent } from "../../../types";

const CalendarContainer = styled.div`
  flex: 1;
  height: 100%;
  overflow: hidden;

  .rbc-calendar {
    height: 100%;
    font-family: inherit;
  }

  .rbc-toolbar {
    display: none;
  }

  .rbc-header {
    background-color: #fff;
    border: none;
    height: 20px;
    font-size: 14px;
    font-weight: normal;
    color: #243544;
  }

  .rbc-month-view {
    border: none;
    border-bottom: 1px solid #e2e3e5;
    overflow: visible !important;
  }

  .rbc-month-row {
    border-left: 1px solid #e2e3e5;
    border-right: 1px solid #e2e3e5;
    overflow: visible !important;
  }

  .rbc-row {
    overflow: visible !important;
  }

  .rbc-month-view .rbc-row-segment {
    display: none;
  }

  .rbc-month-header {
    border-bottom: 1px solid #e2e3e5;
  }

  .rbc-date-cell {
    text-align: left;
    font-weight: normal;
    font-size: 14px;
    padding: 0;
  }

  .rbc-off-range-bg {
    background-color: #f5f5f5;
  }

  .rbc-today {
    background-color: #e3f2fd;
  }

  .rbc-month-view .rbc-event,
  .rbc-month-view .rbc-event-content,
  .rbc-month-view .rbc-event-container {
    display: none;
  }

  .rbc-ellipsis,
  .rbc-show-more,
  .rbc-row-segment .rbc-event-content,
  .rbc-event-label,
  .rbc-day-slot .rbc-event-content {
    font-size: 14px;
  }

  .rbc-time-view {
    border: none;
    border-radius: 0;
    overflow: hidden;
  }

  .rbc-time-header {
    border-bottom: 1px solid #e2e3e5;
    display: flex !important;
  }

  .rbc-time-header-content {
    min-height: 25px;
  }

  .rbc-allday-cell {
    box-sizing: content-box;
    width: 100%;
    height: auto;
    position: relative;
    min-height: 30px;
    display: block !important;
  }

  .rbc-allday-cell .rbc-event {
    background-color: #d9f3f7 !important;
    font-size: 12px;
    padding: 4px !important;
    margin: 1px;
    display: block !important;
  }

  .rbc-allday-cell .rbc-event-allday {
    background-color: #a2aeb8 !important;
    color: #fff !important;
    .rbc-event-content {
      line-height: 1;
    }
  }

  .rbc-time-view .rbc-event {
    background-color: #fff;
    border-radius: 2px;
    padding: 2px 0;
    color: #243544;
    display: block !important;
  }

  .rbc-day-slot .rbc-event-content {
    background-color: #d9f3f7;
    padding: 4px;
  }

  .rbc-time-view .rbc-event .rbc-event-label {
    display: none;
  }

  .rbc-day-slot .rbc-event,
  .rbc-day-slot .rbc-background-event {
    border: none;
    border-left: 2px solid #fff;
    border-top: 1px solid #e2e3e5;
  }

  .rbc-time-column .rbc-events-container {
    height: calc(100% - 2px) !important;
  }

  .rbc-label.rbc-time-header-gutter {
    width: 32px !important;
    min-width: 32px !important;
  }

  .rbc-time-content {
    border-top: none;
  }

  .rbc-time-gutter {
    background-color: #f8f9fa;
    border-right: 1px solid #e2e3e5;
  }

  .rbc-time-gutter .rbc-timeslot-group {
    border-bottom: none;
  }

  .rbc-time-column .rbc-timeslot-group {
    min-height: 48px !important;
  }

  .rbc-timeslot-group .rbc-time-slot {
    background-color: #fff;
  }

  .rbc-time-content > * + * > * {
    border-left: none;
  }

  .rbc-day-slot .rbc-time-slot {
    border-top: none;
  }

  .rbc-current-time-indicator {
    background-color: #ff4757;
    height: 2px;
    z-index: 3;
  }

  .rbc-agenda-view {
    border: 1px solid #e2e3e5;
    border-radius: 8px;
    overflow: hidden;
  }

  .rbc-agenda-table {
    font-size: 14px;
  }

  .rbc-agenda-date-cell,
  .rbc-agenda-time-cell,
  .rbc-agenda-event-cell {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .rbc-month-view .rbc-off-range {
    color: #243544;
  }

  .rbc-month-view .rbc-date-cell > a {
    display: block;
    height: 100%;
    width: 100%;
    text-decoration: none;
    color: inherit;
  }

  .rbc-row-bg {
    z-index: 0;
  }

  .rbc-time-view .rbc-label {
    padding: 0;
    padding-right: 2px;
    font-size: 12px;
    display: block;
    color: #6a757d;
  }
`;

moment.locale("ja");
const localizer = momentLocalizer(moment);

export const BigCalendarView: React.FC<BigCalendarViewProps> = ({
  currentDate,
  currentView,
  events,
  onNavigate,
  onView,
  refetchScheduleList,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<BigCalendarEvent | null>(
    null,
  );
  const isShowMoreAction = useRef(false);

  const weeksCount = useMemo(
    () => calculateRequiredWeeks(currentDate),
    [currentDate],
  );
  const calendarEvents = useMemo(() => {
    if (!events?.length) return [];
    const expanded = expandMultiDayEvents(events);
    return assignEventLevels(expanded);
  }, [events]);

  const handleSelectSlot = ({ start }: { start: Date }) => {
    if (isShowMoreAction.current) {
      isShowMoreAction.current = false;
      return;
    }
    if (currentView === "day") {
      setSelectedEvent({
        id: "temp-slot",
        groupScheduleId: "temp-slot",
        title: "",
        startDate: dayjs(start).format("YYYY-MM-DD"),
        endDate: dayjs(start).format("YYYY-MM-DD"),
        allDay: false,
        isExpanded: false,
        groupPosition: "single",
        groupSize: 1,
        dayIndex: 0,
        groupStartDate: dayjs(start).format("YYYY-MM-DD"),
        resource: { type: "schedule", comment: "" },
        startTime: dayjs(start).format("HH:mm"),
        endTime: dayjs(start).format("HH:mm"),
      });
    }
    setSelectedDate(dayjs(start));
    setIsModalOpen(true);
  };

  const handleNavigate = (date: Date) => {
    onNavigate?.(date);
    if (isShowMoreAction.current) {
      isShowMoreAction.current = false;
      return;
    }
    setSelectedDate(dayjs(date));
    setIsModalOpen(true);
  };

  const handleSelectEvent = (event: BigCalendarEvent) => {
    setSelectedEvent(event);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedDate(null);
    setSelectedEvent(null);
  };

  const handleViewChange = (view: string) => {
    if (view === "day" || view === "month") {
      onView?.(view);
    }
  };

  const handleShowMore = (date: Date) => {
    isShowMoreAction.current = true;
    onNavigate?.(date);
    onView?.("day");
    setTimeout(() => (isShowMoreAction.current = false), 100);
  };

  const handleDateClick = (date: Date) => {
    if (!isShowMoreAction.current) {
      setSelectedDate(dayjs(date));
      setIsModalOpen(true);
    }
  };

  if (currentView === "month") {
    return (
      <>
        <CalendarContainer>
          <CustomMonthView
            currentDate={currentDate}
            events={calendarEvents}
            onEventClick={handleSelectEvent}
            onShowMore={handleShowMore}
            onDateClick={handleDateClick}
          />
        </CalendarContainer>
        {isModalOpen && (
          <ScheduleModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            selectedDate={selectedDate || undefined}
            refetchScheduleList={refetchScheduleList}
            initialData={
              selectedEvent ? createInitialModalData(selectedEvent) : undefined
            }
            events={events}
            isMonthView
          />
        )}
      </>
    );
  }

  return (
    <>
      <CalendarContainer>
        <Calendar
          localizer={localizer}
          events={calendarEvents}
          startAccessor={(event: BigCalendarEvent) =>
            event.startTime
              ? new Date(`${event.startDate}T${event.startTime}`)
              : new Date(event.startDate)
          }
          endAccessor={(event: BigCalendarEvent) =>
            event.endTime
              ? new Date(`${event.endDate}T${event.endTime}`)
              : new Date(event.endDate)
          }
          allDayAccessor="allDay"
          view={getCalendarView(currentView)}
          views={["day", "month"]}
          onNavigate={handleNavigate}
          onSelectSlot={handleSelectSlot}
          onSelectEvent={handleSelectEvent}
          onView={handleViewChange}
          date={currentDate.toDate()}
          selectable={true}
          popup={true}
          formats={getCalendarFormats()}
          step={30}
          timeslots={2}
          showMultiDayTimes={true}
          components={{
            month: {
              dateHeader: ({ date, label }) => {
                const currentMonth = currentDate.toDate().getMonth();
                const isFirstOrLastOutsideDate = isFirstOrLastOutsideMonthDate(
                  date,
                  currentMonth,
                );
                return (
                  <CustomDateHeader
                    date={date}
                    label={label}
                    events={calendarEvents}
                    onEventClick={handleSelectEvent}
                    onShowMore={handleShowMore}
                    currentDate={currentDate.toDate()}
                    isFirstOrLastOfOutsideMonth={isFirstOrLastOutsideDate}
                    weeksCount={weeksCount}
                  />
                );
              },
            },
          }}
        />
      </CalendarContainer>
      {isModalOpen && (
        <ScheduleModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          selectedDate={selectedDate || undefined}
          refetchScheduleList={refetchScheduleList}
          initialData={
            selectedEvent ? createInitialModalData(selectedEvent) : undefined
          }
          events={events}
        />
      )}
    </>
  );
};
