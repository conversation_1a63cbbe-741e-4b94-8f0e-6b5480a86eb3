import React from "react";

import dayjs from "dayjs";
import styled from "styled-components";

import { getDateTextColor } from "@/features/start/utils/schedule";
import { useEventPositioning } from "@/features/start/hooks/useEventPositioning";

import { CustomEvent } from "./CustomEvent";

import type { CustomDateHeaderProps, BigCalendarEvent } from "../../../types";

const DateCellContainer = styled.div`
  height: 76px;
  padding: 4px;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
`;

const DateNumber = styled.div<{ $textColor: string }>`
  font-size: 14px;
  font-weight: normal;
  color: ${(props) => props.$textColor};
  text-align: center;
  cursor: pointer;
  flex-shrink: 0;
`;

const ShowMoreLink = styled.div`
  color: #243544;
  font-size: 11px;
  cursor: pointer;
  z-index: 10;
  margin-top: auto;
  flex-shrink: 0;
  padding-top: 2px;
`;

const MultiDayOverlay = styled.div<{ $spanDays: number; $top: number }>`
  position: absolute;
  top: ${(props) => props.$top}px;
  height: 16px;
  border-radius: 2px;
  background-color: #d9f3f7;
  font-size: 12px;
  padding: 2px 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  z-index: 20;
  user-select: none;
  box-sizing: border-box;
  width: ${(props) =>
    `calc(${props.$spanDays * 100}% + ${(props.$spanDays - 1) * 9}px)`};
`;

const EventsContainer = styled.div`
  flex-grow: 1;
  position: relative;
  margin-top: 2px;
`;

const EventWrapper = styled.div<{ $top: number }>`
  position: absolute;
  top: ${(props) => props.$top}px;
  left: 0;
  width: 100%;
  box-sizing: border-box;
`;

export const CustomDateHeader: React.FC<CustomDateHeaderProps> = ({
  date,
  label,
  events,
  onEventClick,
  onShowMore,
  isFirstOrLastOfOutsideMonth,
}) => {
  const { sortedEvents, eventPositions } = useEventPositioning(events, date);

  const getEventSpanDays = (event: BigCalendarEvent): number => {
    const allSegments = events
      .filter((e) => e.groupScheduleId === event.groupScheduleId)
      .sort((a, b) => a.dayIndex - b.dayIndex);

    if (allSegments.length === 0) return 1;

    const lastSegment = allSegments[allSegments.length - 1];
    if (!lastSegment) return 1;
    const realEndDate = dayjs(lastSegment.startDate);

    const dayOfWeek = date.getDay(); // 0 for Sunday
    const remainingDaysInWeek = 7 - dayOfWeek;

    const daysUntilEventEnds = realEndDate.diff(dayjs(date), "day") + 1;

    return Math.max(1, Math.min(remainingDaysInWeek, daysUntilEventEnds));
  };

  const totalEvents = sortedEvents.length;
  const displayEvents =
    totalEvents > 3 ? sortedEvents.slice(0, 2) : sortedEvents;
  const remainingCount = totalEvents > 3 ? totalEvents - 2 : 0;

  const dateLabel = isFirstOrLastOfOutsideMonth
    ? `${date.getMonth() + 1}月${date.getDate()}日`
    : label;

  const handleShowMoreClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShowMore?.(date);
  };

  return (
    <DateCellContainer>
      <DateNumber $textColor={getDateTextColor(date.getDay(), date)}>
        {dateLabel}
      </DateNumber>
      <EventsContainer>
        {displayEvents.map((event) => {
          const isOverlayStarter =
            event.isExpanded &&
            (event.groupPosition === "start" ||
              (date.getDay() === 0 && event.dayIndex > 0));

          const top = eventPositions.get(event.id) ?? 0;

          if (isOverlayStarter) {
            return (
              <MultiDayOverlay
                key={event.id}
                $spanDays={getEventSpanDays(event)}
                $top={top}
                onClick={() => onEventClick?.(event)}
              >
                {event.title}
              </MultiDayOverlay>
            );
          }

          if (event.groupPosition === "single") {
            return (
              <EventWrapper key={event.id} $top={top}>
                <CustomEvent event={event} onClick={onEventClick} />
              </EventWrapper>
            );
          }
          return null;
        })}
      </EventsContainer>
      {remainingCount > 0 && (
        <ShowMoreLink onClick={handleShowMoreClick}>
          他{remainingCount}件
        </ShowMoreLink>
      )}
    </DateCellContainer>
  );
};
