import React from "react";

import styled from "styled-components";

import {
  generateCalendarWeeks,
  getDateTextColor,
  isFirstOrLastOutsideMonthDate,
} from "../../../utils/schedule";

import { CustomDateHeader } from "./CustomDateHeader";

import type { BigCalendarEvent } from "../../../types";
import type { Dayjs } from "dayjs";

const MonthContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #e2e3e5;
  box-sizing: border-box;
`;

const WeekHeader = styled.div`
  display: flex;
  height: 20px;
  border-bottom: 1px solid #e2e3e5;
  box-sizing: border-box;
`;

const WeekHeaderCell = styled.div<{ $textColor: string }>`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: ${(props) => props.$textColor};
  box-sizing: border-box;
  min-width: 0;
  cursor: default;
`;

const WeekRow = styled.div`
  display: flex;
  flex: 1;
  border: 1px solid #e2e3e5;
  border-top: none;
  box-sizing: border-box;
  height: 76px;
  overflow: visible;

  &:last-child {
    border-bottom: none;
  }
`;

const DayCell = styled.div`
  flex: 1;
  border-right: 1px solid #e2e3e5;
  position: relative;
  background-color: #fff;
  box-sizing: border-box;
  min-width: 0;
  width: 0;
  overflow: visible;

  &:last-child {
    border-right: none;
  }
`;

interface CustomMonthViewProps {
  currentDate: Dayjs;
  events: BigCalendarEvent[];
  onEventClick?: (event: BigCalendarEvent) => void;
  onShowMore?: (date: Date) => void;
  onDateClick?: (date: Date) => void;
}

const weekDays = ["日", "月", "火", "水", "木", "金", "土"];

export const CustomMonthView: React.FC<CustomMonthViewProps> = ({
  currentDate,
  events,
  onEventClick,
  onShowMore,
  onDateClick,
}) => {
  const weeks = generateCalendarWeeks(currentDate);
  const weeksCount = weeks.length;
  const currentMonth = currentDate.month();

  const handleDateClick = (date: Date) => {
    onDateClick?.(date);
  };

  return (
    <MonthContainer>
      <WeekHeader>
        {weekDays.map((day, index) => (
          <WeekHeaderCell key={index} $textColor={getDateTextColor(index)}>
            {day}
          </WeekHeaderCell>
        ))}
      </WeekHeader>
      {weeks.map((week, weekIndex) => (
        <WeekRow key={weekIndex}>
          {week.map((date, dayIndex) => {
            const isFirstOrLastOutsideDate = isFirstOrLastOutsideMonthDate(
              date,
              currentMonth,
            );
            return (
              <DayCell key={dayIndex} onClick={() => handleDateClick(date)}>
                <CustomDateHeader
                  date={date}
                  label={date.getDate().toString()}
                  events={events}
                  onEventClick={onEventClick}
                  onShowMore={onShowMore}
                  currentDate={currentDate.toDate()}
                  isFirstOrLastOfOutsideMonth={isFirstOrLastOutsideDate}
                  weeksCount={weeksCount}
                />
              </DayCell>
            );
          })}
        </WeekRow>
      ))}
    </MonthContainer>
  );
};
