import React, { useEffect } from "react";

import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";
import dayjs from "dayjs";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { TextInput } from "@/components/ui/TextInput";
import { DatePicker } from "@/components/ui/DatePicker";
import { Checkbox } from "@/components/ui/Checkbox";
import { InputLabel } from "@/components/ui/InputLabel";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { ErrorText } from "@/components/ui/ErrorText";
import { usePostSchedule } from "@/features/start/hooks/usePostSchedule";
import { useDeleteScheduleMutation } from "@/apis/gql/operations/__generated__/schedule";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import { TimePickerDropdown } from "./TimePickerDropdown";

import type { Schedule } from "@/apis/gql/generated/types";
import type { Dayjs } from "dayjs";
import type { ScheduleFormData } from "../../../types";

const ModalContent = styled.div`
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
`;

const Hyphen = styled.span`
  width: 8px;
  height: 1px;
  background-color: #243544;
`;

const DateRangeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const StyledDatePicker = styled(DatePicker)`
  width: 120px;
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CommentTextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #e2e3e5;
  border-radius: 6px;
  background-color: #fbfcfe;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #4ebbe0;
  }

  &::placeholder {
    color: #a2aeb8;
  }
`;

const DeleteButton = styled.button`
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 14px;
  cursor: pointer;
  text-align: right;
  padding: 0;
  margin-top: 8px;
`;

const StyledInputLabel = styled(InputLabel)`
  font-size: 14px;
  font-weight: 500;
  color: #243544;
`;

interface ScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Partial<ScheduleFormData> & { scheduleID?: number };
  selectedDate?: Dayjs;
  refetchScheduleList: () => void;
  events?: Schedule[];
  isMonthView?: boolean;
}

export const ScheduleModal: React.FC<ScheduleModalProps> = ({
  isOpen,
  onClose,
  initialData,
  selectedDate,
  refetchScheduleList,
  events,
  isMonthView,
}) => {
  const { handleError } = useErrorHandler();
  const [deleteSchedule] = useDeleteScheduleMutation();
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<ScheduleFormData>({
    defaultValues: {
      title: initialData?.title || "",
      startDate: initialData?.startDate || selectedDate,
      endDate: initialData?.endDate || selectedDate,
      isAllDay: initialData?.isAllDay || false,
      comment: initialData?.comment || "",
      startTime: initialData?.startTime || "",
      endTime: initialData?.endTime || "",
    },
  });

  const { submitSchedule, loading } = usePostSchedule({
    onCompleted: () => {
      refetchScheduleList();
      onClose();
    },
    onError: (error) => {
      handleError({ error });
    },
  });

  useEffect(() => {
    if (isMonthView && !initialData?.scheduleID) {
      reset({
        title: "",
        startDate: selectedDate?.startOf("day"),
        endDate: selectedDate?.endOf("day"),
        isAllDay: true,
        comment: "",
        startTime: "",
        endTime: "",
      });
    } else {
      const event = events?.find(
        (event) => event.scheduleID === initialData?.scheduleID,
      );
      reset({
        title: event?.title || "",
        startDate: dayjs(event?.startDate) || selectedDate?.startOf("day"),
        endDate: dayjs(event?.endDate) || selectedDate?.endOf("day"),
        isAllDay: event?.isAllDay || false,
        comment: event?.comment || "",
        startTime: initialData?.startTime || "09:00",
        endTime: initialData?.endTime || "10:00",
      });
    }
  }, [initialData, selectedDate, reset, events, isMonthView]);

  const onSubmit = (data: ScheduleFormData) => {
    submitSchedule({
      ...data,
      scheduleID:
        initialData && "scheduleID" in initialData
          ? (initialData as { scheduleID?: number }).scheduleID
          : undefined,
    });
  };

  const onDelete = () => {
    deleteSchedule({
      variables: { scheduleID: initialData?.scheduleID || 0 },
      onCompleted: () => {
        refetchScheduleList();
        onClose();
      },
      onError: (error) => {
        handleError({ error });
      },
    });
  };

  return (
    <Modal
      isOpen={isOpen}
      title="スケジュール"
      width={480}
      onCancel={onClose}
      footer={[
        <Button key="cancel" varient="tertiary" shape="round" onClick={onClose}>
          キャンセル
        </Button>,
        <Button
          key="save"
          varient="primary"
          shape="round"
          onClick={handleSubmit(onSubmit)}
          loading={loading}
        >
          保存
        </Button>,
      ]}
    >
      <ModalContent>
        <FormField>
          <StyledInputLabel label="タイトル" required />
          <Controller
            name="title"
            control={control}
            rules={{ required: "タイトルを入力してください。" }}
            render={({ field }) => (
              <>
                <TextInput
                  {...field}
                  placeholder="院長休み"
                  hasError={!!errors.title}
                />
                {errors.title && <ErrorText>{errors.title.message}</ErrorText>}
              </>
            )}
          />
        </FormField>

        <FormField>
          <StyledInputLabel label="期間" required />
          <DateRangeContainer>
            {watch("isAllDay") && (
              <>
                <Controller
                  control={control}
                  name="startDate"
                  rules={{
                    validate: (startDate, formValues) => {
                      const endDate = formValues?.endDate || watch("endDate");
                      if (startDate && endDate && startDate.isAfter(endDate)) {
                        return "開始日は終了日以前の日付を選択してください。";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <StyledDatePicker
                      suffixIcon={<SvgIconCalendar />}
                      allowClear={false}
                      {...field}
                      hasError={!!errors.startDate}
                      format={watch("isAllDay") ? "YYYY/MM/DD" : undefined}
                      disabled={false}
                    />
                  )}
                />
                <span>〜</span>
                <Controller
                  control={control}
                  name="endDate"
                  render={({ field }) => (
                    <StyledDatePicker
                      suffixIcon={<SvgIconCalendar />}
                      allowClear={false}
                      {...field}
                      hasError={!!errors.startDate}
                      format={watch("isAllDay") ? "YYYY/MM/DD" : undefined}
                      disabled={false}
                    />
                  )}
                />
              </>
            )}
            {!watch("isAllDay") && (
              <>
                <Controller
                  name="startTime"
                  control={control}
                  rules={{ required: "開始時刻を選択してください。" }}
                  render={({ field }) => (
                    <TimePickerDropdown
                      label=""
                      timeSelected={field.value}
                      onChangeTime={field.onChange}
                      hasError={!!errors.startTime}
                    />
                  )}
                />
                <Hyphen />
                <Controller
                  name="endTime"
                  control={control}
                  rules={{
                    required: "終了時刻を選択してください。",
                    validate: (endTime, formValues) => {
                      const startTime =
                        formValues?.startTime || watch("startTime");
                      if (startTime && endTime && startTime >= endTime) {
                        return "終了時刻は開始時刻より後にしてください。";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <TimePickerDropdown
                      label=""
                      timeSelected={field.value}
                      onChangeTime={field.onChange}
                      hasError={!!errors.endTime}
                    />
                  )}
                />
              </>
            )}
          </DateRangeContainer>
          {errors.startDate && (
            <ErrorText>{errors.startDate.message}</ErrorText>
          )}
          {errors.startTime && (
            <ErrorText>{errors.startTime.message}</ErrorText>
          )}
          {errors.endTime && <ErrorText>{errors.endTime.message}</ErrorText>}
        </FormField>

        <CheckboxContainer>
          <Controller
            name="isAllDay"
            control={control}
            render={({ field: { value, onChange } }) => (
              <Checkbox
                checked={value}
                onChange={(e) => {
                  const checked = e.target.checked;
                  onChange(checked);
                }}
              >
                終日
              </Checkbox>
            )}
          />
        </CheckboxContainer>

        <FormField>
          <StyledInputLabel label="コメント" />
          <Controller
            name="comment"
            control={control}
            render={({ field }) => (
              <CommentTextArea {...field} placeholder="" />
            )}
          />
        </FormField>

        <DeleteButton onClick={onDelete}>スケジュールを削除</DeleteButton>
      </ModalContent>
    </Modal>
  );
};
