import type { Schedule } from "@/apis/gql/generated/types";
import type { Dayjs } from "dayjs";

export type ViewType = "day" | "month";

export type ScheduleEvent = {
  id: string;
  startTime: string;
  endTime: string;
  type: "appointment" | "examination" | "break";
};

export type ScheduleHeaderProps = {
  currentView: ViewType;
  currentDate: Dayjs;
  onViewChange: (view: ViewType) => void;
  onNavigateDate: (direction: "prev" | "next") => void;
};

export type DayViewProps = {
  currentDate: Dayjs;
  events?: ScheduleEvent[];
};

export type MonthViewProps = {
  currentDate: Dayjs;
  events?: ScheduleEvent[];
};

export type DateNavigationProps = {
  currentDate: Dayjs;
  currentView: ViewType;
  onNavigate: (direction: "prev" | "next") => void;
};

export type ViewToggleProps = {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
};

export type ScheduleFormData = {
  title: string;
  startDate: Dayjs;
  endDate: Dayjs;
  isAllDay: boolean;
  comment: string;
  startTime?: string;
  endTime?: string;
  scheduleID?: number;
};

export type ScheduleModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ScheduleFormData) => void;
  onDelete?: () => void;
  initialData?: Partial<ScheduleFormData>;
  selectedDate?: Dayjs;
  events?: Schedule[];
};

export interface CustomEventProps {
  event: BigCalendarEvent;
  onClick?: (event: BigCalendarEvent) => void;
}

export interface CustomDateHeaderProps {
  date: Date;
  label: string;
  events: BigCalendarEvent[];
  onEventClick?: (event: BigCalendarEvent) => void;
  currentDate: Date;
  isFirstOrLastOfOutsideMonth: boolean;
  onShowMore?: (date: Date) => void;
  weeksCount?: number;
}

export interface BigCalendarViewProps {
  currentDate: Dayjs;
  currentView: "day" | "month";
  events?: Schedule[];
  onNavigate?: (date: Date) => void;
  onView?: (view: "day" | "month") => void;
  refetchScheduleList: () => void;
}

export interface MultiDayEventInfo {
  event: BigCalendarEvent;
  startCol: number;
  span: number;
  row: number;
  weekStartDate: Date;
}

export interface BigCalendarEvent {
  id: string;
  groupScheduleId: string;
  title: string;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  allDay: boolean;
  isExpanded: boolean;
  groupPosition: "single" | "start" | "middle" | "end";
  groupSize: number;
  dayIndex: number;
  groupStartDate: string;
  resource?: {
    type: string;
    comment?: string | null;
  };
}

export enum HospitalInfoPageType {
  TOP = "TOP",
  STAFFS = "STAFFS",
}
