import moment from "moment";
import dayjs from "dayjs";
import { isHoliday } from "@holiday-jp/holiday_jp";

import type { Dayjs } from "dayjs";
import type { View } from "react-big-calendar";
import type { Schedule } from "@/apis/gql/generated/types";
import type { BigCalendarEvent, ViewType } from "../types";

export const getDateRange = (currentView: ViewType, currentDate: Dayjs) => {
  if (currentView === "month") {
    const totalWeeks = calculateRequiredWeeks(dayjs(currentDate));
    const startOfMonth = dayjs(currentDate).startOf("month");
    const startDateDayjs = startOfMonth.startOf("week");
    const endDateDayjs = startDateDayjs.add(totalWeeks * 7 - 1, "days");

    return {
      startDate: startDateDayjs.toISOString(),
      endDate: endDateDayjs.toISOString(),
    };
  } else {
    const startDate = dayjs(currentDate).startOf("day").toISOString();
    const endDate = dayjs(currentDate).endOf("day").toISOString();
    return {
      startDate,
      endDate,
    };
  }
};

export const calculateRequiredWeeks = (date: dayjs.Dayjs): number => {
  const startOfMonth = date.startOf("month");
  const endOfMonth = date.endOf("month");
  const startOfFirstWeek = startOfMonth.startOf("week");
  const endOfLastWeek = endOfMonth.endOf("week");
  const weeksDiff = endOfLastWeek.diff(startOfFirstWeek, "week") + 1;
  return Math.max(5, Math.min(6, weeksDiff));
};

export const generateCalendarWeeks = (date: dayjs.Dayjs): Date[][] => {
  const weeks: Date[][] = [];
  const startOfMonth = date.startOf("month");
  let currentWeekStart = startOfMonth.startOf("week");
  const totalWeeks = calculateRequiredWeeks(date);

  for (let weekIndex = 0; weekIndex < totalWeeks; weekIndex++) {
    const week: Date[] = [];
    for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
      const currentDay = currentWeekStart.add(dayIndex, "day");
      week.push(currentDay.toDate());
    }
    weeks.push(week);
    currentWeekStart = currentWeekStart.add(7, "day");
  }
  return weeks;
};

export const expandMultiDayEvents = (
  events: Schedule[],
): BigCalendarEvent[] => {
  const expandedEvents: BigCalendarEvent[] = [];

  events.forEach((event) => {
    try {
      const startDateTime = dayjs(event.startDate);
      const endDateTime = dayjs(event.endDate);

      if (!startDateTime.isValid() || !endDateTime.isValid()) return;

      const startDate = startDateTime.startOf("day");
      const endDate = endDateTime.startOf("day");
      const daysDifference = endDate.diff(startDate, "day") + 1;

      if (daysDifference <= 1) {
        expandedEvents.push({
          id: `${event.scheduleID}`,
          groupScheduleId: `${event.scheduleID}`,
          title: event.title ?? "",
          startDate: startDate.format("YYYY-MM-DD"),
          endDate: startDate.format("YYYY-MM-DD"),
          startTime: event.isAllDay ? undefined : startDateTime.format("HH:mm"),
          endTime: event.isAllDay ? undefined : endDateTime.format("HH:mm"),
          allDay: !!event.isAllDay,
          isExpanded: false,
          groupPosition: "single",
          groupSize: 1,
          dayIndex: 0,
          groupStartDate: startDate.format("YYYY-MM-DD"),
          resource: { type: "appointment", comment: event.comment },
        });
      } else {
        const groupStartDate = startDate.format("YYYY-MM-DD");
        for (let i = 0; i < daysDifference; i++) {
          const currentDay = startDate.add(i, "day");
          const position: "start" | "middle" | "end" =
            i === 0 ? "start" : i === daysDifference - 1 ? "end" : "middle";

          expandedEvents.push({
            id: `${event.scheduleID}-${i}`,
            groupScheduleId: `${event.scheduleID}`,
            title: event.title ?? "",
            startDate: currentDay.format("YYYY-MM-DD"),
            endDate: currentDay.format("YYYY-MM-DD"),
            startTime:
              i === 0 && !event.isAllDay
                ? startDateTime.format("HH:mm")
                : undefined,
            endTime:
              i === daysDifference - 1 && !event.isAllDay
                ? endDateTime.format("HH:mm")
                : undefined,
            allDay: !!event.isAllDay,
            isExpanded: true,
            groupPosition: position,
            groupSize: daysDifference,
            dayIndex: i,
            groupStartDate,
            resource: { type: "appointment", comment: event.comment },
          });
        }
      }
    } catch (error) {
      console.warn("Error processing event:", event, error);
    }
  });

  return expandedEvents;
};

export const assignEventLevels = (
  events: BigCalendarEvent[],
): BigCalendarEvent[] => {
  return events;
};

export const getEventsForDate = (
  events: BigCalendarEvent[],
  date: Date,
): BigCalendarEvent[] => {
  const targetDate = dayjs(date).format("YYYY-MM-DD");
  return events.filter((event) => event.startDate === targetDate);
};

export const isFirstOrLastOutsideMonthDate = (
  date: Date,
  currentMonth: number,
): boolean => {
  const dateMonth = date.getMonth();
  if (dateMonth !== currentMonth) {
    const day = date.getDay();
    if (dateMonth === (currentMonth - 1 + 12) % 12) {
      const nextDay = new Date(date);
      nextDay.setDate(date.getDate() + 1);
      return nextDay.getMonth() === currentMonth || day === 6;
    }
    if (dateMonth === (currentMonth + 1) % 12) {
      const prevDay = new Date(date);
      prevDay.setDate(date.getDate() - 1);
      return prevDay.getMonth() === currentMonth || day === 0;
    }
  }
  return false;
};

export const getCalendarView = (view: ViewType): View => {
  return view === "day" ? "day" : "month";
};

export const getCalendarFormats = () => ({
  monthHeaderFormat: "YYYY年M月",
  dayHeaderFormat: "M月D日(ddd)",
  dayRangeHeaderFormat: ({ start, end }: { start: Date; end: Date }) =>
    `${moment(start).format("M月D日")} - ${moment(end).format("M月D日")}`,
  agendaHeaderFormat: ({ start, end }: { start: Date; end: Date }) =>
    `${moment(start).format("M月D日")} - ${moment(end).format("M月D日")}`,
});

export const createInitialModalData = (event: BigCalendarEvent) => {
  return {
    title: event.title,
    startDate: dayjs(event.startDate),
    endDate: dayjs(event.endDate),
    isAllDay: event.allDay,
    comment: event.resource?.comment ?? undefined,
    scheduleID: event.groupScheduleId
      ? Number(event.groupScheduleId)
      : undefined,
    startTime: event.startTime,
    endTime: event.endTime,
  };
};

export const getDateTextColor = (dayOfWeek?: number, date?: Date): string => {
  if (date && isHoliday(date)) {
    return "#e74c3c";
  }

  if (dayOfWeek === 0) {
    return "#e74c3c";
  }

  if (dayOfWeek === 6) {
    return "#005bac";
  }

  return "#243544";
};
