import { useCallback } from "react";

import dayjs from "dayjs";

import {
  useAddScheduleMutation,
  useEditScheduleMutation,
} from "@/apis/gql/operations/__generated__/schedule";

import type { ApolloError } from "@apollo/client";
import type { ScheduleFormData } from "../types";

interface UsePostScheduleParams {
  onCompleted?: () => void;
  onError?: (error: Error | ApolloError) => void;
}

interface SubmitScheduleParams extends ScheduleFormData {
  scheduleID?: number;
}

export const usePostSchedule = ({
  onCompleted,
  onError,
}: UsePostScheduleParams = {}) => {
  const [addSchedule, { loading: addLoading }] = useAddScheduleMutation();
  const [editSchedule, { loading: editLoading }] = useEditScheduleMutation();

  const submitSchedule = useCallback(
    async (data: SubmitScheduleParams) => {
      const {
        scheduleID,
        title,
        startDate,
        endDate,
        isAllDay,
        comment,
        startTime,
        endTime,
      } = data;
      const variables = {
        title,
        startDate: isAllDay
          ? dayjs(startDate).startOf("day").toISOString()
          : dayjs(startDate)
              .hour(parseInt(startTime?.split(":")[0] || "0"))
              .minute(parseInt(startTime?.split(":")[1] || "0"))
              .toISOString(),
        endDate: isAllDay
          ? dayjs(endDate).endOf("day").toISOString()
          : dayjs(endDate)
              .hour(parseInt(endTime?.split(":")[0] || "0"))
              .minute(parseInt(endTime?.split(":")[1] || "0"))
              .toISOString(),
        isAllDay,
        comment,
      };
      try {
        if (scheduleID) {
          await editSchedule({
            variables: { scheduleID, ...variables },
            onCompleted,
            onError,
          });
        } else {
          await addSchedule({
            variables,
            onCompleted,
            onError,
          });
        }
      } catch (error) {
        if (onError) onError(error as Error);
      }
    },
    [addSchedule, editSchedule, onCompleted, onError],
  );

  return {
    submitSchedule,
    loading: addLoading || editLoading,
  };
};
