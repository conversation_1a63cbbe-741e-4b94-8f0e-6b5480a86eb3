import { useMemo } from "react";

import dayjs from "dayjs";

import { getEventsForDate } from "../utils/schedule";

import type { BigCalendarEvent } from "../types";

export const useEventPositioning = (events: BigCalendarEvent[], date: Date) => {
  const dayEvents = useMemo(
    () => getEventsForDate(events, date),
    [events, date],
  );

  const sortedEvents = useMemo(() => {
    return [...dayEvents].sort((a, b) => {
      const aIsMultiDay = a.isExpanded;
      const bIsMultiDay = b.isExpanded;

      if (aIsMultiDay && !bIsMultiDay) return -1;
      if (!aIsMultiDay && bIsMultiDay) return 1;

      if (aIsMultiDay && bIsMultiDay) {
        const aStart = dayjs(a.groupStartDate).valueOf();
        const bStart = dayjs(b.groupStartDate).valueOf();
        if (aStart !== bStart) return aStart - bStart;
      }

      if (a.allDay && !b.allDay) return -1;
      if (!a.allDay && b.allDay) return 1;

      const aTime = a.startTime || "00:00";
      const bTime = b.startTime || "00:00";
      if (aTime < bTime) return -1;
      if (aTime > bTime) return 1;

      return a.title.localeCompare(b.title);
    });
  }, [dayEvents]);

  const eventPositions = useMemo(() => {
    const positions = new Map<string, number>();
    const occupiedLevels: boolean[] = [];

    // Create a level map for all multi-day series in the view
    const multiDayLevelMap = new Map<string, number>();
    const multiDayEvents = events.filter((e) => e.isExpanded);
    const sortedMultiDayGroups = [
      ...new Map(multiDayEvents.map((e) => [e.groupScheduleId, e])).values(),
    ].sort((a, b) => dayjs(a.groupStartDate).diff(b.groupStartDate));

    sortedMultiDayGroups.forEach((event) => {
      let level = 0;
      // Find the first level that this event can fit into without overlapping
      while (true) {
        const hasConflict = sortedMultiDayGroups.some((placedEvent) => {
          if (multiDayLevelMap.get(placedEvent.groupScheduleId) !== level)
            return false;

          const aStart = dayjs(event.groupStartDate);
          const aEnd = aStart.add(event.groupSize - 1, "day");
          const bStart = dayjs(placedEvent.groupStartDate);
          const bEnd = bStart.add(placedEvent.groupSize - 1, "day");

          return (
            aStart.isBetween(bStart, bEnd, "day", "[]") ||
            bStart.isBetween(aStart, aEnd, "day", "[]")
          );
        });

        if (!hasConflict) {
          multiDayLevelMap.set(event.groupScheduleId, level);
          break;
        }
        level++;
      }
    });

    // Mark levels occupied by multi-day events on this specific day
    dayEvents.forEach((event) => {
      if (event.isExpanded) {
        const level = multiDayLevelMap.get(event.groupScheduleId);
        if (level !== undefined) {
          occupiedLevels[level] = true;
        }
      }
    });

    sortedEvents.forEach((event) => {
      let level = 0;
      if (event.isExpanded && multiDayLevelMap.has(event.groupScheduleId)) {
        level = multiDayLevelMap.get(event.groupScheduleId)!;
      } else {
        while (occupiedLevels[level]) {
          level++;
        }
      }
      positions.set(event.id, level * 18);
      occupiedLevels[level] = true; // Mark level as used for subsequent single-day events
    });

    return positions;
  }, [events, dayEvents, sortedEvents]);

  return { sortedEvents, eventPositions };
};
