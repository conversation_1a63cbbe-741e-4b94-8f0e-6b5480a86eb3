import { gql } from "@/apis/gql/apollo-client";

export const SAVE_EPS_GET_PRE_REGISTRATION = gql`
  mutation postApiEpsGetPreRegistrationData(
    $input: EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput
  ) {
    postApiEpsGetPreRegistrationData(
      emrCloudApiRequestsEpsGetPreRegistrationDataRequestInput: $input
    ) {
      data {
        preRegistrationCheckingModel {
          epsPrescriptionModel {
            accessCode
            bango
            createDate
            createId
            deleteReasonDisplay
            deletedDate
            deletedReason
            dispensingDate
            dispensingDateDisplay
            edaNo
            epsDispensing {
              bango
              cancelReason
              createDate
              createId
              createMachine
              dispensingDate
              dispensingDocument
              dispensingResultId
              dispensingTimes
              edaNo
              epsUpdateDateTime
              hokensyaNo
              hpId
              id
              isDeleted
              kigo
              kohiFutansyaNo
              kohiJyukyusyaNo
              messageFlg
              prescriptionId
              ptId
              receptionPharmacyName
              resultType
              updateDate
              updateId
              updateMachine
            }
            epsDispensingModel {
              bango
              cancelReason
              createDate
              createId
              createMachine
              dispensingDate
              dispensingDocument
              dispensingResultId
              dispensingTimes
              edaNo
              epsUpdateDateTime
              hokensyaNo
              hpId
              id
              isDeleted
              kigo
              kohiFutansyaNo
              kohiJyukyusyaNo
              messageFlg
              prescriptionId
              ptId
              receptionPharmacyName
              resultType
              updateDate
              updateId
              updateMachine
            }
            epsUpdateDateTime
            hokenDisplay
            hokensyaNo
            hpId
            id
            issueType
            issueTypeDisplay
            kaId
            kaInf {
              id
              kaId
              kaName
              kaSname
              receKaCd
              sortNo
              yousikiKaCd
            }
            kaSName
            kigo
            kohiFutansyaNo
            kohiJyukyusyaNo
            messageFlag
            pharmacyName
            prescriptionDocument
            prescriptionId
            ptId
            ptInf {
              age
              birthday
              birthdayDisplay
              comment
              deathDate
              email
              firstVisitDate
              homeAddress1
              homeAddress2
              homePost
              houmonAgreed
              hpId
              isDead
              isRyosyoDetail
              isShowKyuSeiName
              isTester
              job
              kanaName
              lastAppointmentDepartment
              lastVisitDate
              limitConsFlg
              mail
              mainHokenPid
              memo
              name
              nextAppointmentDepartment
              officeAddress1
              officeAddress2
              officeMemo
              officeName
              officePost
              officeTel
              primaryDoctor
              ptId
              ptNum
              rainCount
              rainCountInt
              referenceNo
              renrakuAddress1
              renrakuAddress2
              renrakuMemo
              renrakuName
              renrakuName2
              renrakuPost
              renrakuTel
              renrakuTel2
              seqNo
              setanusi
              sex
              sinDate
              tel1
              tel2
              zokugara
            }
            ptName
            ptNum
            ptNumDisplay
            raiinNo
            refileCount
            refill
            resultType
            resultTypeDisplay
            seqNo
            sinDate
            sinDateDisplay
            status
            statusDisplay
            tantoId
            tantoInf {
              drName
              email
              emailUpdateDate
              endDate
              functionMstModels {
                functionCd
                functionName
                jobCd
                permissions {
                  functionCd
                  permission
                  permissionKey
                }
                userMstModel {
                  functionCd
                  hpId
                  isDefault
                  permission
                  userId
                }
              }
              hpId
              id
              isDeleted
              isInitLoginId
              isInitPassword
              jobCd
              kaId
              kaSName
              kanaName
              loginId
              loginPass
              managerKbn
              mayakuLicenseNo
              missLoginCount
              name
              permissions {
                functionCd
                hpId
                isDefault
                permission
                userId
              }
              renkeiCd1
              sNameBinding
              sname
              sortNo
              startDate
              status
              userId
            }
            tantoName
            updateDate
            updateId
          }
          odrInfs {
            daysCnt
            hokenInfModel {
              bango
              confirmDate
              confirmDateList {
                checkComment
                checkId
                checkMachine
                checkName
                confirmDate
                hokenGrp
                hokenId
                isDeleted
                onlineConfirmationId
                ptId
                seqNo
              }
              edaNo
              endDate
              endDateSort
              filingInfModels {
                fileId
                fileLink
              }
              futansyaNo
              genmenGaku
              genmenKbn
              genmenRate
              hasDateConfirmed
              hokenEdaNo
              hokenId
              hokenKbn
              hokenMst {
                ageEnd
                ageStart
                calcSpKbn
                checkDigit
                countKbn
                dayLimitCount
                dayLimitFutan
                displayHokenNo
                displayTextMaster
                displayTextMasterWithoutHokenNo
                enTen
                endDate
                excepHokenSyas {
                  hokenEdaNo
                  hokenNo
                  hokensyaNo
                  hpId
                  id
                  prefNo
                  startDate
                }
                futanKbn
                futanRate
                futanYusen
                hokenEdaNo
                hokenKohiKbn
                hokenName
                hokenNameCd
                hokenNo
                hokenSName
                hokenSbtKbn
                houbetu
                houbetuDisplayText
                isAdded
                isFutansyaNoCheck
                isJyukyusyaNoCheck
                isLimitList
                isLimitListSum
                isOtherPrefValid
                isTokusyuNoCheck
                jyuKyuCheckDigit
                kaiFutangaku
                kaiLimitFutan
                kogakuHairyoKbn
                kogakuTekiyo
                kogakuTotalAll
                kogakuTotalExcFutan
                kogakuTotalKbn
                limitKbn
                moneyLimitListFlag
                monthLimitCount
                monthLimitFutan
                monthSpLimit
                prefNo
                prefactureName
                receFutanHide
                receFutanKbn
                receFutanRound
                receKisai
                receKisai2
                receKisaiKokho
                receSeikyuKbn
                receSpKbn
                receTenKisai
                receZeroKisai
                seikyuYm
                selectedValueMaster
                sortNo
                startDate
              }
              hokenMstDisplayTextMaster
              hokenMstEndDate
              hokenMstFutanKbn
              hokenMstFutanRate
              hokenMstHoubetu
              hokenMstSbtKbn
              hokenMstStartDate
              hokenNo
              hokenSbtCd
              hokenSentaku
              hokensyaAddress
              hokensyaMst {
                address1
                address2
                bango
                hokenKbn
                hokensyaNo
                houbetu
                houbetuKbn
                hpId
                isKigoNa
                isReadOnlyHokenSyaNo
                kanaName
                kigo
                name
                postCdDisplay
                postCode
                prefNo
                rateHonnin
                rateKazoku
                tel1
              }
              hokensyaName
              hokensyaNo
              hokensyaTel
              honkeKbn
              houbetu
              hpId
              insuredName
              isAddHokenCheck
              isAddNew
              isDeleted
              isEmptyModel
              isExpirated
              isHaveHokenMst
              isHoken
              isJibai
              isJibaiOrRosai
              isJihi
              isKokuho
              isNoHoken
              isNotKenkoKanri
              isNotNenkin
              isNotRodo
              isReceKisaiOrNoHoken
              isRousai
              isShaho
              isShahoOrKokuho
              jibaiHokenName
              jibaiHokenTanto
              jibaiHokenTel
              jibaiJyusyouDate
              keizokuKbn
              kenkoKanriBango
              kigo
              kofuDate
              kogakuKbn
              lastDateConfirmed
              listRousaiTenki {
                rousaiTenkiEndDate
                rousaiTenkiIsDeleted
                rousaiTenkiSinkei
                rousaiTenkiTenki
                seqNo
              }
              nenkinBango
              ptId
              rodoBango
              rousaiCityName
              rousaiJigyosyoName
              rousaiKantokuCd
              rousaiKofuNo
              rousaiPrefName
              rousaiReceCount
              rousaiRoudouCd
              rousaiSaigaiKbn
              rousaiSyobyoCd
              rousaiSyobyoDate
              ryoyoEndDate
              ryoyoStartDate
              seqNo
              sikakuDate
              sinDate
              startDate
              syokumuKbn
              tasukaiYm
              tokki1
              tokki2
              tokki3
              tokki4
              tokki5
              tokureiYm1
              tokureiYm2
            }
            hokenPid
            hpId
            id
            inoutKbn
            isDeleted
            kohiInfModel {
              birthday
              calcSpKbn
              confirmDate
              confirmDateList {
                checkComment
                checkId
                checkMachine
                checkName
                confirmDate
                hokenGrp
                hokenId
                isDeleted
                onlineConfirmationId
                ptId
                seqNo
              }
              endDate
              filingInfModels {
                fileId
                fileLink
              }
              futansyaNo
              gendoGaku
              hasDateConfirmed
              hokenEdaNo
              hokenId
              hokenMstModel {
                ageEnd
                ageStart
                calcSpKbn
                checkDigit
                countKbn
                dayLimitCount
                dayLimitFutan
                displayHokenNo
                displayTextMaster
                displayTextMasterWithoutHokenNo
                enTen
                endDate
                excepHokenSyas {
                  hokenEdaNo
                  hokenNo
                  hokensyaNo
                  hpId
                  id
                  prefNo
                  startDate
                }
                futanKbn
                futanRate
                futanYusen
                hokenEdaNo
                hokenKohiKbn
                hokenName
                hokenNameCd
                hokenNo
                hokenSName
                hokenSbtKbn
                houbetu
                houbetuDisplayText
                isAdded
                isFutansyaNoCheck
                isJyukyusyaNoCheck
                isLimitList
                isLimitListSum
                isOtherPrefValid
                isTokusyuNoCheck
                jyuKyuCheckDigit
                kaiFutangaku
                kaiLimitFutan
                kogakuHairyoKbn
                kogakuTekiyo
                kogakuTotalAll
                kogakuTotalExcFutan
                kogakuTotalKbn
                limitKbn
                moneyLimitListFlag
                monthLimitCount
                monthLimitFutan
                monthSpLimit
                prefNo
                prefactureName
                receFutanHide
                receFutanKbn
                receFutanRound
                receKisai
                receKisai2
                receKisaiKokho
                receSeikyuKbn
                receSpKbn
                receTenKisai
                receZeroKisai
                seikyuYm
                selectedValueMaster
                sortNo
                startDate
              }
              hokenName
              hokenNo
              hokenSbtKbn
              houbetu
              isDeleted
              isEmptyModel
              isAddNew
              isExpirated
              isExpired
              isHaveKohiMst
              isLimitList
              jyukyusyaNo
              kofuDate
              lastDateConfirmed
              limitListModel {
                biko
                code
                futanGaku
                hokenPid
                id
                isDeleted
                kohiId
                raiinNo
                seqNo
                sinDate
                sinDateD
                sinDateM
                sinDateY
                sort
                sortKey
                totalGaku
                totalMoney
              }
              prefNo
              prefNoMst
              rate
              seqNo
              sikakuDate
              sinDate
              startDate
              tokusyuNo
            }
            odrKouiKbn
            orderContainsCommonNameNotInCommonNameMedicines
            orderDetailsNotContainMedicine {
              alternationIndex
              bikoComment
              buiKbn
              bunkatu
              bunkatuKoui
              cdEdaNo
              cdKbn
              cdKbnNo
              cdKouNo
              centerCd
              centerItemCd1
              centerItemCd2
              centerName
              cmtCol1
              cmtCol2
              cmtCol3
              cmtCol4
              cmtColKeta1
              cmtColKeta2
              cmtColKeta3
              cmtColKeta4
              cmtName
              cmtOpt
              cnvTermVal
              cnvUnitName
              commentNewline
              drugKbn
              fontColor
              handanGrpKbn
              hpId
              inOutKbn
              ipnCd
              ipnName
              isAdopted
              isDrug
              isDrugUsage
              isDummy
              isGetPriceInYakka
              isGetYakka
              isInjection
              isInjectionUsage
              isKensaMstEmpty
              isNormalComment
              isNodspRece
              isShohoBiko
              isShohoComment
              isSuppUsage
              isStandardUsage
              itemCd
              jissiDate
              itemName
              jissiId
              jissiKbn
              jissiMachine
              kasan1
              kasan2
              kensaGaichu
              kikakiUnit
              kohatuKbn
              kokuji1
              kokuji2
              masterSbt
              memoItem
              odrKouiKbn
              odrTermVal
              odrUnitName
              ptId
              raiinNo
              refillSetting
              relationItem
              reqCd
              rikikaUnit
              rikikaRate
              rowNo
              rousaiKbn
              rpEdaNo
              senteiRyoyoKbn
              rpNo
              sinDate
              sinKouiKbn
              sinYm
              suryo
              syohoLimitKbn
              syohoKbn
              ten
              termVal
              unitName
              unitSbt
              yakka
              yakkaiUnit
              yjCd
              yohoKbn
              youkaiekiCd
            }
            ptHokenPatternModel {
              endDate
              hokenId
              hokenKbn
              hokenMemo
              hokenPid
              hokenSbtCd
              kohi1Id
              kohi2Id
              kohi3Id
              kohi4Id
              ptId
              seqNo
              startDate
            }
            ptId
            raiinNo
            rpName
            rpEdaNo
            rpNo
            santeiKbn
            sinDate
            sikyuKbn
            sortNo
            syohoSbt
            tosekiKbn
          }
          wasConfirmedOnline
        }
      }
      message
      status
    }
  }
`;

export const SAVE_EPS_GET_OUT_DRUG_CSV = gql`
  mutation postApiEpsGetOutDrugCsvData(
    $input: EmrCloudApiRequestsEpsGetOutDrugCsvDataRequestInput!
  ) {
    postApiEpsGetOutDrugCsvData(
      emrCloudApiRequestsEpsGetOutDrugCsvDataRequestInput: $input
    ) {
      data {
        outDrugCsvData
        prescriptionDocument
      }
      message
      status
    }
  }
`;

export const GET_EPS_DUPLICATE_MEDICATION_CHECK = gql`
  query getApiEpsGetDuplicateMedicationCheck(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiEpsGetDuplicateMedicationCheck(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        epsChkList {
          checkResult
          createDate
          createId
          createMachine
          drugInfo
          epsChkDetailModels {
            comment
            hpId
            message
            messageCategory
            messageId
            pastDate
            pastDispensingQuantity
            pastDosageForm
            pastInsurancePharmacyName
            pastMedicalInstitutionName
            pastPharmaceuticalCode
            pastPharmaceuticalCodeType
            pastPharmaceuticalName
            pastUsage
            pharmaceuticalsIngredientName
            ptId
            raiinNo
            seqNo
            targetDispensingQuantity
            targetDosageForm
            targetPharmaceuticalCode
            targetPharmaceuticalCodeType
            targetPharmaceuticalName
            targetUsage
          }
          hpId
          isDeleted
          onlineConsent
          oralBrowsingConsent
          ptId
          raiinNo
          sameMedicalInstitutionAlertFlg
          seqNo
          sinDate
          updateDate
          updateId
          updateMachine
        }
      }
      message
      status
    }
  }
`;

export const SAVE_EPS_DUPLICATE_MEDICATION_CHECK = gql`
  mutation postApiEpsSaveDuplicateMedicationCheck(
    $input: EmrCloudApiRequestsEpsSaveDuplicateMedicationCheckRequestInput
  ) {
    postApiEpsSaveDuplicateMedicationCheck(
      emrCloudApiRequestsEpsSaveDuplicateMedicationCheckRequestInput: $input
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;

export const POST_EPS_CHECK_ERROR_FOR_PRE_REGISTRATION = gql`
  mutation postApiEpsCheckErrorForPreRegistration(
    $input: EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput
  ) {
    postApiEpsCheckErrorForPreRegistration(
      emrCloudApiRequestsEpsGetPreRegistrationDataRequestInput: $input
    ) {
      data {
        errorMessage
      }
      message
      status
    }
  }
`;

export const POST_EPS_SAVE_PRESCRIPTION_INFO = gql`
  mutation postApiEpsSavePrescriptionInfo(
    $input: EmrCloudApiRequestsEpsSavePrescriptionInfoRequestInput
  ) {
    postApiEpsSavePrescriptionInfo(
      emrCloudApiRequestsEpsSavePrescriptionInfoRequestInput: $input
    ) {
      data {
        epsPrescriptions {
          accessCode
          bango
          deletedReason
          edaNo
          hokensyaNo
          hpId
          issueType
          kigo
          kohiFutansyaNo
          kohiJyukyusyaNo
          prescriptionDocument
          prescriptionId
          ptId
          raiinNo
          refileCount
          seqNo
          sinDate
          status
        }
        status
      }
      message
      status
    }
  }
`;
export const GET_API_EPS_INSURANCE_INFO = gql`
  query getApiEpsGetEpsInsuranceInfo(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiEpsGetEpsInsuranceInfo(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      message
      status
      data {
        ptHokenPatterns {
          endDate
          hokenId
          hokenKbn
          hokenPid
          hokenSbtCd
          kohi1Id
          kohi2Id
          kohi3Id
          kohi4Id
          ptId
          seqNo
          ptHokenInf {
            bango
            edaNo
            hokenEdaNo
            hokenId
            hokenKbn
            hokenNo
            hokensyaNo
            isDeleted
            kigo
            ptId
            startDate
            syokumuKbn
            tasukaiYm
          }
          ptKohi1 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
          ptKohi2 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
          ptKohi3 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
          ptKohi4 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
        }
        raiinInf {
          hokenPid
          hpId
          prescriptionIssueSelect
          printEpsReference
          ptId
          raiinNo
          sinDate
        }
        wasConfirmedOnline
      }
    }
  }
`;
