import { gql } from "@/apis/gql/apollo-client";

export const GET_TEMPLATE_DOC_PARAMS = gql`
  query getTemplateDocParams {
    getTemplateDocParams {
      itemName
      parameterName
      reflected_content
    }
  }
`;

export const CREATE_TEMPLATE_DOC = gql`
  mutation createTemplateDoc($input: TemplateDocInput!) {
    createTemplateDoc(input: $input) {
      templateDocId
      displayName
      fileName
      createdAt
      updatedAt
      s3Key
    }
  }
`;

export const GET_TEMPLATE_DOC_UPLOAD_URL = gql`
  query getTemplateDocUploadUrl($input: String!) {
    getTemplateDocUploadUrl(s3Key: $input)
  }
`;

export const GET_TEMPLATE_DOC_LIST = gql`
  query getTemplateDocList {
    getTemplateDocList {
      templates {
        templateDocId
        displayName
        fileName
        createdAt
        updatedAt
        s3Key
      }
    }
  }
`;

export const EDIT_TEMPLATE_DOC = gql`
  mutation editTemplateDoc($input: TemplateDocInput!) {
    editTemplateDoc(input: $input) {
      templateDocId
      displayName
      fileName
      createdAt
      updatedAt
      s3Key
    }
  }
`;

export const DELETE_TEMPLATE_DOC = gql`
  mutation deleteTemplateDoc($templateDocId: Int!) {
    deleteTemplateDoc(templateDocId: $templateDocId) {
      isDeleted
    }
  }
`;

export const GET_DOCUMENT_CATEGORY_LIST = gql`
  query getApiDocumentGetListDocumentCategory {
    getApiDocumentGetListDocumentCategory {
      data {
        templateFiles {
          displayName
          fileLink
          fileName
          updatedAt
        }
      }
    }
  }
`;

export const DOWNLOAD_DOCUMENT_TEMPLATE = gql`
  mutation postApiDocumentDowloadDocumentTemplate(
    $input: EmrCloudApiRequestsDocumentDownloadDocumentTemplateRequestInput
  ) {
    postApiDocumentDowloadDocumentTemplate(
      emrCloudApiRequestsDocumentDownloadDocumentTemplateRequestInput: $input
    ) {
      data {
        content
        fileName
        mimeType
      }
    }
  }
`;
