import { gql } from "@apollo/client";

export const GET_KENSA_HISTORY_LIST_KENSA_INF_DETAIL = gql`
  query getApiKensaHistoryGetListKensaInfDetail(
    $endDate: Int
    $getGetPrevious: Boolean
    $iraiCd: Int
    $iraiCdStart: Int
    $itemQuantity: Int
    $listSeqNoItems: [BigInt]
    $ptId: BigInt
    $setId: Int
    $showAbnormalKbn: Boolean
    $startDate: Int
  ) {
    getApiKensaHistoryGetListKensaInfDetail(
      endDate: $endDate
      getGetPrevious: $getGetPrevious
      iraiCd: $iraiCd
      iraiCdStart: $iraiCdStart
      itemQuantity: $itemQuantity
      listSeqNoItems: $listSeqNoItems
      ptId: $ptId
      setId: $setId
      showAbnormalKbn: $showAbnormalKbn
      startDate: $startDate
    ) {
      data {
        data {
          kensaInfDetailCol {
            bilirubin
            index
            iraiCd
            iraiDate
            nyubi
            seqGroupNo
            sikyuKbn
            tosekiKbn
            yoketu
          }
          kensaInfDetailData {
            dynamicArray {
              abnormalKbn
              bilirubin
              cmt1
              cmt2
              cmtCd1
              cmtCd2
              femaleStd
              femaleStdHigh
              femaleStdLow
              inoutKbn
              iraiCd
              iraiDate
              isDeleted
              kensaItemCd
              kensaKana
              kensaName
              maleStd
              maleStdHigh
              maleStdLow
              nyubi
              ptId
              raiinNo
              resultType
              resultVal
              rowSeqId
              seqGroupNo
              seqNo
              seqParentNo
              sikyuKbn
              sortNo
              status
              tosekiKbn
              unit
              yoketu
            }
            femaleStd
            iraiDate
            kensaItemCd
            kensaKana
            kensaName
            maleStd
            rowSeqId
            seqNo
            seqParentNo
            sortNo
            unit
          }
          totalCol
        }
      }
      message
      status
    }
  }
`;

export const GET_USER_CONF_GET_LIST_FOR_MODEL = gql`
  query getApiUserConfGetListForModel {
    getApiUserConfGetListForModel {
      data {
        userConfs {
          grpCd
          grpItemCd
          grpItemEdaNo
          param
          userId
          val
        }
      }
      message
      status
    }
  }
`;

export const GET_INFO_HOSPITAL_KENSA = gql`
  query getApiKensaMstGetInHospitalKensaMst($isExceptVital: Boolean!) {
    getApiKensaMstGetInHospitalKensaMst(isExceptVital: $isExceptVital) {
      data {
        listData {
          femaleStd
          maleStd
          kensaName
          unit
          kensaItemCd
        }
      }
      message
      status
    }
  }
`;

export const EXPORT_PDF_EXAMPLE_RESULT = gql`
  mutation postApiPdfCreatorKensaHistoryReport(
    $emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput: EmrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput!
  ) {
    postApiPdfCreatorKensaHistoryReport(
      emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput: $emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput
    ) {
      data {
        content
        fileName
      }
      message
      status
    }
  }
`;

export const SAVE_KARTE_EXAMPLE_RESULT = gql`
  mutation postApiExamResultSave(
    $isAdd: Boolean
    $isDeleted: Int
    $kensaTime: String
    $ptId: BigInt
    $raiinNo: BigInt
    $examResults: [EmrCloudApiRequestsExamResultSaveExamResultRequestInput]
    $iraiCd: BigInt
    $iraiDate: Int
  ) {
    postApiExamResultSave(
      emrCloudApiRequestsExamResultSaveExamResultsRequestInput: {
        ptId: $ptId
        kensaTime: $kensaTime
        isDeleted: $isDeleted
        isAdd: $isAdd
        raiinNo: $raiinNo
        examResults: $examResults
        iraiCd: $iraiCd
        iraiDate: $iraiDate
      }
    ) {
      message
      status
      data {
        success
      }
    }
  }
`;
