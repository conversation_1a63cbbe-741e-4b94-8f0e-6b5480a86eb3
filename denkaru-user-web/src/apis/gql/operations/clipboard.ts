import { gql } from "@apollo/client";

export const CLIPBOARD_UPSERT = gql`
  mutation postApiNextOrderUpsert(
    $emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput: EmrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput
  ) {
    postApiNextOrderUpsert(
      emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput: $emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput
    ) {
      message
      status
      data {
        validationOdrs {
          validationOdrs {
            orderInfDetailPosition
            orderInfPosition
            validationField
            status
            validationMessage
          }
        }
        validationNextOrders {
          validationNextOrders {
            validationMessage
            status
          }
        }
      }
    }
  }
`;

export const GET_DETAIL_CLIPBOARD = gql`
  query getApiNextOrderGet(
    $ptId: BigInt!
    $rsvkrtNo: BigInt!
    $rsvkrtKbn: Int!
    $sinDate: Int!
  ) {
    getApiNextOrderGet(
      ptId: $ptId
      rsvkrtNo: $rsvkrtNo
      rsvkrtKbn: $rsvkrtKbn
      sinDate: $sinDate
    ) {
      data {
        karteInfModel {
          richText
          text
        }
        groupHokenItems {
          groupOdrItems {
            odrInfs {
              tosekiKbn
              sortNo
              syohoSbt
              sikyuKbn
              santeiKbn
              rpNo
              rpName
              rpEdaNo
              odrKouiKbn
              isDeleted
              inoutKbn
              createName
              id
              hokenPid
              daysCnt
              rsvKrtOrderInfDetailItems {
                buiKbn
                rousaiKbn
                bunkatu
                centerName
                cmtName
                cmtOpt
                drugKbn
                ipnCd
                ipnName
                isNodspRece
                itemCd
                itemName
                kohatuKbn
                kokuji1
                kokuji2
                masterSbt
                rowNo
                rpEdaNo
                rpNo
                sinKouiKbn
                suryo
                syohoKbn
                yohoKbn
                unitSbt
                unitName
                termVal
                syohoLimitKbn
                cmtCol1
                cmtCol2
                cmtCol3
                cmtCol4
                cmtColKeta1
                cmtColKeta2
                cmtColKeta3
                cmtColKeta4
                isSelectiveComment
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_LIST_CLIPBOARD = gql`
  query getApiNextOrderGetList($ptId: BigInt!, $isDeleted: Boolean) {
    getApiNextOrderGetList(ptId: $ptId, isDeleted: $isDeleted) {
      data {
        nextOrders {
          isCheckAgainNextTime
          rsvName
          rsvkrtKbn
          rsvkrtNo
          rsvDate
          sortNo
        }
      }
    }
  }
`;

export const CHECK_IS_NEXT_CHECK_AGAIN_CLIPBOARD = gql`
  query getApiNextOrderGetNextOrderNextCheck($ptId: BigInt!) {
    getApiNextOrderGetNextOrderNextCheck(ptId: $ptId) {
      data {
        nextOrder {
          isCheckAgainNextTime
          rsvName
          rsvkrtKbn
          rsvkrtNo
          rsvDate
        }
      }
    }
  }
`;

export const SORT_CLIPBOARD = gql`
  mutation postApiNextOrderUpsertSortNoDataDrop(
    $rsvkrtNoFrom: BigInt!
    $rsvkrtNoTo: BigInt!
    $ptId: BigInt!
  ) {
    postApiNextOrderUpsertSortNoDataDrop(
      emrCloudApiRequestsMedicalExaminationUpsertSortNoDataDropRequestInput: {
        rsvkrtNoTo: $rsvkrtNoTo
        ptId: $ptId
        rsvkrtNoFrom: $rsvkrtNoFrom
      }
    ) {
      message
      status
      data {
        nextOrders {
          hpId
          isCheckAgainNextTime
        }
      }
    }
  }
`;
