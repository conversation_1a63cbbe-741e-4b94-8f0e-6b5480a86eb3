import { gql } from "@/apis/gql/apollo-client";

export const GET_CLIENT_CERTIFICATE = gql`
  query getClientCertificate {
    getClientCertificate {
      ClientCertificateID
      Label
      CommonName
      IssueDate
      ExpirationDate
      DownloadURL
      TokenExpireTime
      InstallPassword
    }
  }
`;

export const CREATE_CERTIFICATE = gql`
  mutation createClientCertificate($input: CreateClientCertificateReq!) {
    createClientCertificate(input: $input) {
      ClientCertificateID
      InstallPassword
      DownloadURL
      Label
      CommonName
      TokenExpireTime
    }
  }
`;

export const DELETE_CERTIFICATE = gql`
  mutation deleteClientCertificate($input: DeleteClientCertificateReq!) {
    deleteClientCertificate(input: $input) {
      message
    }
  }
`;
