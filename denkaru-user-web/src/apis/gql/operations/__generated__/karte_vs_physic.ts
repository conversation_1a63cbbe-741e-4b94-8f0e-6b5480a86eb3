import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables =
  Types.Exact<{
    ptId: Types.Scalars["BigInt"]["input"];
  }>;

export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery = {
  __typename?: "query_root";
  getApiKarteMedicalHistoryGetKarteMedicalHistory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistoryEmrCloudApiResponsesKarteMedicalHistoryGetKarteMedicalHistoryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesKarteMedicalHistoryEmrCloudApiResponsesKarteMedicalHistoryGetKarteMedicalHistoryResponse";
      kioRekis?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtKioRekiModel";
        byomei?: string;
        byomeiCd?: string;
        byotaiCd?: string;
        cmt?: string;
        hpId?: number;
        isDeleted?: number;
        onSetDate?: string;
        ptId?: string;
        startDate?: number;
      }>;
      octDrugs?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtOtcDrugModel";
        cmt?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        isDeleted?: number;
        ptId?: string;
        seqNo?: string;
        serialNum?: number;
        sortNo?: number;
        startDate?: number;
        tradeName?: string;
      }>;
      otherDrugs?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtOtherDrugModel";
        cmt?: string;
        drugName?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        isDeleted?: number;
        itemCd?: string;
        ptId?: string;
        seqNo?: string;
        sortNo?: number;
        startDate?: number;
      }>;
      pregnants?: Array<{
        __typename?: "DomainModelsKarteMedicalHistoryPtPregnancyRelatedModel";
        hpId?: number;
        breastfeedStatus?: number;
        isDeleted?: number;
        pregnancyStatus?: number;
        ptId?: string;
      }>;
      socialHistorys?: Array<{
        __typename?: "DomainModelsKarteMedicalHistoryPtSmokingRelatedModel";
        smokingDurationUnit?: number;
        smokingDuration?: number;
        smokingDetail?: string;
        seqNo?: string;
        ptId?: string;
        smokingDailyCount?: number;
        isDeleted?: number;
        hpId?: number;
        drinkingFrequency?: number;
        drinkingDetail?: string;
        drinkingAmount?: number;
        smokingStartAge?: number;
        smokingEndAge?: number;
        smokingEndYear?: number;
        smokingStartYear?: number;
        smokingStatus?: number;
        totalSmokingDuration?: number;
        brinkmanNumber?: string;
      }>;
      supples?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtSuppleModel";
        cmt?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        indexCd?: string;
        indexWord?: string;
        isDeleted?: number;
        ptId?: string;
        seqNo?: number;
        sortNo?: number;
        startDate?: number;
      }>;
    };
  };
};

export const GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument = gql`
  query getApiKarteMedicalHistoryGetKarteMedicalHistory($ptId: BigInt!) {
    getApiKarteMedicalHistoryGetKarteMedicalHistory(ptId: $ptId) {
      data {
        kioRekis {
          byomei
          byomeiCd
          byotaiCd
          cmt
          hpId
          isDeleted
          onSetDate
          ptId
          startDate
        }
        octDrugs {
          cmt
          endDate
          fullEndDate
          fullStartDate
          hpId
          isDeleted
          ptId
          seqNo
          serialNum
          sortNo
          startDate
          tradeName
        }
        otherDrugs {
          cmt
          drugName
          endDate
          fullEndDate
          fullStartDate
          hpId
          isDeleted
          itemCd
          ptId
          seqNo
          sortNo
          startDate
        }
        pregnants {
          hpId
          breastfeedStatus
          isDeleted
          pregnancyStatus
          ptId
        }
        socialHistorys {
          smokingDurationUnit
          smokingDuration
          smokingDetail
          seqNo
          ptId
          smokingDailyCount
          isDeleted
          hpId
          drinkingFrequency
          drinkingDetail
          drinkingAmount
          smokingStartAge
          smokingEndAge
          smokingEndYear
          smokingStartYear
          smokingStatus
          totalSmokingDuration
          brinkmanNumber
        }
        supples {
          cmt
          endDate
          fullEndDate
          fullStartDate
          hpId
          indexCd
          indexWord
          isDeleted
          ptId
          seqNo
          sortNo
          startDate
        }
      }
    }
  }
`;

/**
 * __useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery__
 *
 * To run a query within a React component, call `useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  > &
    (
      | {
          variables: GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >(GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument, options);
}
export function useGetApiKarteMedicalHistoryGetKarteMedicalHistoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >(GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument, options);
}
export function useGetApiKarteMedicalHistoryGetKarteMedicalHistorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >(GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument, options);
}
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryHookResult =
  ReturnType<typeof useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery>;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryLazyQueryHookResult =
  ReturnType<
    typeof useGetApiKarteMedicalHistoryGetKarteMedicalHistoryLazyQuery
  >;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistorySuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiKarteMedicalHistoryGetKarteMedicalHistorySuspenseQuery
  >;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryResult =
  Apollo.QueryResult<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >;
