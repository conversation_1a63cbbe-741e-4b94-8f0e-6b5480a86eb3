import { gql } from "@apollo/client";

export const GET_DRUG_INFORMATION = gql`
  query getApiInputItemGetDrugInf($itemCd: String, $sinDate: Int) {
    getApiInputItemGetDrugInf(itemCd: $itemCd, sinDate: $sinDate) {
      data {
        drugInf {
          name
          genericName
          unit
          maker
          vender
          kohatuKbnName
          ten
          receUnitName
          mark
          pathPicHou
          listPicHou
          listPicZai
          pathPicZai
          otherPicHou
          otherPicZai
          yjCode
          customPathPicHou
          customPathPicZai
          defaultPathPicHou
          defaultPathPicZai
          kohatuKbn
          madokuKbn
          kouseisinKbn
        }
      }
    }
  }
`;

export const GET_DRUG_MENU_TREE = gql`
  fragment DrugTreeFields on DomainModelsDrugDetailDrugMenuItemModel {
    dbLevel
    drugMenuName
    indexOfMenuLevel
    level
    menuName
    rawDrugMenuName
    seqNo
    yjCode
  }

  query getApiInputItemGetDrugMenuTree($sinDate: Int, $itemCd: String) {
    getApiInputItemGetDrugMenuTree(itemCd: $itemCd, sinDate: $sinDate) {
      data {
        listData {
          ...DrugTreeFields
          children {
            ...DrugTreeFields
            children {
              ...DrugTreeFields
            }
          }
        }
      }
    }
  }
`;

export const GET_ITEM_SHOW_MDB_BYOMEI = gql`
  query getApiInputItemShowMdbByomei(
    $drugName: String
    $itemCd: String
    $level: Int
    $yJCode: String
  ) {
    getApiInputItemShowMdbByomei(
      drugName: $drugName
      itemCd: $itemCd
      level: $level
      yJCode: $yJCode
    ) {
      data {
        htmlData
      }
      message
      status
    }
  }
`;

export const GET_ITEM_SHOW_KANJA_MUKE = gql`
  query getApiInputItemShowKanjaMuke(
    $level: Int
    $itemCd: String
    $drugName: String
    $yJCode: String
  ) {
    getApiInputItemShowKanjaMuke(
      drugName: $drugName
      itemCd: $itemCd
      level: $level
      yJCode: $yJCode
    ) {
      data {
        htmlData
      }
      message
      status
    }
  }
`;

export const GET_ITEM_SHOW_PRODUCT_INF = gql`
  query getApiInputItemShowProductInf(
    $drugName: String
    $itemCd: String
    $level: Int
    $sinDate: Int
    $yJCode: String
  ) {
    getApiInputItemShowProductInf(
      drugName: $drugName
      itemCd: $itemCd
      level: $level
      sinDate: $sinDate
      yJCode: $yJCode
    ) {
      data {
        htmlData
      }
      message
      status
    }
  }
`;

export const GET_DATA_PRINT_DRUG_INFO = gql`
  query getApiPdfCreatorGetDataPrintDrugInfo(
    $itemCd: String
    $level: Int
    $sinDate: Int
    $type: Int
    $yJCode: String
  ) {
    getApiPdfCreatorGetDataPrintDrugInfo(
      itemCd: $itemCd
      level: $level
      sinDate: $sinDate
      type: $type
      yJCode: $yJCode
    ) {
      data
      message
      status
    }
  }
`;
