import { gql } from "@apollo/client";

export const GET_EXAM_TIME_SLOTS = gql`
  query getExamTimeSlotsByConditions(
    $input: GetExamTimeSlotByConditionsInput!
  ) {
    getExamTimeSlotsByConditions(input: $input) {
      examTimeSlotID
      treatmentType
      examStartDate
      examEndDate
      calendar {
        calendarID
        reservableSlotSettingType
        calendarTreatMents {
          treatmentDepartment {
            treatmentDepartmentId
            firstConsultationTime
            nextConsultationTime
          }
        }
      }
      slotLimitReserveNum
      totalReserveSlots
      isSuspended
      reservableSlots
    }
  }
`;
