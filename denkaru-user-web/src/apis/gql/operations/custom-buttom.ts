import { gql } from "@apollo/client";

export const GET_CUSTOM_BUTTON_CONF = gql`
  query getApiCustomButtonConfListAllCustomButtonConf(
    $ptId: Int
    $raiinNo: BigInt
  ) {
    getApiCustomButtonConfListAllCustomButtonConf(
      ptId: $ptId
      raiinNo: $raiinNo
    ) {
      data {
        customButtonConfModels {
          urlGenerate
          filename
          hpId
          id
          isUrl
          name
          path
          pattern
          sort
          urlImage
          workdir
        }
      }
      message
      status
    }
  }
`;

export const UPDATE_CUSTOM_BUTTON_SORT = gql`
  mutation updateCustomButtonSort(
    $updateSortCustomButtonConfInputItem: [EmrCloudApiRequestsCustomButtonConfUpdateSortCustomButtonConfRequestCustomButtonConfRequestInput!]!
  ) {
    postApiCustomButtonConfUpdateSortForListCustomButtonConf(
      emrCloudApiRequestsCustomButtonConfUpdateSortCustomButtonConfRequestInput: {
        updateSortCustomButtonConfInputItem: $updateSortCustomButtonConfInputItem
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;

export const GET_CUSTOM_BUTTON_PARAMS = gql`
  query getApiCustomButtonParamMstListCustomButtonParamMsts {
    getApiCustomButtonParamMstListCustomButtonParamMsts {
      data {
        customButtonParamMstModels {
          id
          name
          param
          sort
        }
      }
      message
      status
    }
  }
`;

export const GET_CUSTOM_BUTTON_DETAIL = gql`
  query getApiCustomButtonConfGetDetailCustomButtonConf(
    $id: Int!
    $raiinNo: BigInt
    $ptId: Int
  ) {
    getApiCustomButtonConfGetDetailCustomButtonConf(
      id: $id
      raiinNo: $raiinNo
      ptId: $ptId
    ) {
      data {
        customButtonConfModel {
          filename
          id
          isUrl
          name
          path
          pattern
          sort
          workdir
          urlImage
          urlGenerate
          hpId
        }
      }
      message
      status
    }
  }
`;

export const DELETE_CUSTOM_BUTTON_CONF = gql`
  mutation deleteApiCustomButtonConfDeleteCustomButtonConf($id: Int!) {
    deleteApiCustomButtonConfDeleteCustomButtonConf(id: $id) {
      data {
        status
      }
      message
      status
    }
  }
`;

export const SAVE_CUSTOM_BUTTON_CONF = gql`
  mutation postApiCustomButtonConfSaveCustomButtonConf(
    $input: EmrCloudApiRequestsCustomButtonConfSaveCustomButtonConfRequestInput!
  ) {
    postApiCustomButtonConfSaveCustomButtonConf(
      emrCloudApiRequestsCustomButtonConfSaveCustomButtonConfRequestInput: $input
    ) {
      data {
        id
        status
      }
      message
      status
    }
  }
`;
