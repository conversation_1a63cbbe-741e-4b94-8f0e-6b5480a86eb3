import { gql } from "@apollo/client";

export const GET_CONSULTATION_HISTORY = gql`
  query getApiHistoryGetList(
    $keyWord: String
    $offset: Int
    $limit: Int
    $ptId: BigInt
    $sinDate: Int
    $isShowApproval: Int
    $userId: Int
    $odrKouiKbns: [Int]
    $treatmentDepartmentIds: [Int]
    $tantoIds: [Int]
    $hasSOAP: Boolean
  ) {
    getApiHistoryGetList(
      keyWord: $keyWord
      offset: $offset
      limit: $limit
      ptId: $ptId
      sinDate: $sinDate
      isShowApproval: $isShowApproval
      userId: $userId
      odrKouiKbns: $odrKouiKbns
      treatmentDepartmentIds: $treatmentDepartmentIds
      tantoIds: $tantoIds
      hasSOAP: $hasSOAP
    ) {
      data {
        karteOrdRaiins {
          karteEdition {
            hokenGroups {
              groupOdrItems {
                odrInfs {
                  odrDetails {
                    youkaiekiCd
                    rousaiKbn
                    centerName
                    centerCd
                    yohoKbn
                    yjCd
                    yakkaiUnit
                    yakka
                    unitSbt
                    unitName
                    termVal
                    ten
                    syohoLimitKbn
                    syohoKbn
                    suryo
                    sinKouiKbn
                    sinDate
                    rpNo
                    rpEdaNo
                    rowNo
                    rikikaUnit
                    rikikaRate
                    reqCd
                    raiinNo
                    ptId
                    odrUnitName
                    odrTermVal
                    memoItem
                    masterSbt
                    kokuji2
                    kokuji1
                    kohatuKbn
                    kikakiUnit
                    kensaGaichu
                    itemName
                    itemCd
                    isNodspRece
                    isKensaMstEmpty
                    isGetPriceInYakka
                    ipnName
                    ipnCd
                    hpId
                    hasCmtName
                    handanGrpKbn
                    drugKbn
                    fontColor
                    displayItemName
                    commentNewline
                    cnvUnitName
                    cnvTermVal
                    cmtOpt
                    cmtName
                    alternationIndex
                    bunkatu
                    bunkatuKoui
                    centerItemCd1
                    centerItemCd2
                    bikoComment
                    buiKbn
                    isAdopted
                    senteiRyoyoKbn
                  }
                  updateName
                  updateMachine
                  updateDateDisplay
                  updateDate
                  tosekiKbn
                  syohoSbt
                  sortNo
                  sinDate
                  sikyuKbn
                  santeiKbn
                  rpNo
                  rpName
                  rpEdaNo
                  raiinNo
                  ptId
                  odrKouiKbn
                  isDeleted
                  inoutKbn
                  id
                  hpId
                  hokenPid
                  groupOdrKouiKbn
                  daysCnt
                  createName
                  createMachine
                  createId
                  createDateDisplay
                  createDate
                }
                tosekiKbn
                syohoSbt
                sinkyuName
                sikyuName
                sikyuKbn
                santeiName
                santeiKbn
                kouiCode
                isKensa
                isDrug
                inOutName
                inOutKbn
                hokenPid
                groupName
                groupKouiCode
              }
              hokenTitle
              hokenPid
            }
            karteStatus
            karteStatusText
            listKarteFiles {
              createDate
              createDateDisplay
              createName
              isDeleted
              isSchema
              dspFileName
              linkFile
              seqNo
              updateDate
              updateDateDisplay
              updateName
              fileName
              categoryCd
              memo
            }
            ptId
            raiinNo
            updateDate
            updateId
            updateName
            karteHistories {
              karteData {
                createDate
                createDateDisplay
                createName
                hpId
                isDeleted
                karteKbn
                ptId
                raiinNo
                richText
                seqNo
                sinDate
                text
                updateDate
                updateDateDisplay
              }
              karteKbn
              kbnName
              kbnShortName
              sortNo
              canImage
            }
            isDeleted
            hpId
            headerOrderModels {
              updateUserName
              syosaisinKbn
              syosaishinBinding
              jikanKbn
              jikanBinding
              isDeleted
              hokenPattentName
              createDateBinding
            }
            edition
            createId
            createDate
            approvalId
            approvalDate
            approvalName
          }
          raiinNo
          santeiKbn
          santeiKbnDisplay
          sinDate
          sinEndTime
          sinStartTime
          sinryoTitle
          status
          syosaisinDisplay
          syosaisinKbn
          tagNo
          tantoFullName
          tantoId
          tantoName
          treatmentDepartmentId
          treatmentDepartmentTitle
          uketsukeName
          uketukeTime
          kaName
          kaId
          jikanKbn
          jikanDisplay
          hokenType
          hokenTitle
          hokenRate
          hokenPid
        }
        keyWord
        startPage
        total
        totalKeyWordMatched
        currentKeyWordMatched
      }
      message
      status
    }
  }
`;

export const GET_LIST_KARTE_FILTER = gql`
  query getApiKarteFilterGetList {
    getApiKarteFilterGetList {
      data {
        karteFilters {
          autoApply
          filterId
          filterName
          karteFilterDetailModel {
            bookMarkChecked
            listHokenId
            listKaId
            listUserId
          }
          sortNo
        }
      }
    }
  }
`;

export const GET_LIST_HISTORY_VERSION = gql`
  query getApiHistoryGetListVersion(
    $limit: Int
    $offset: Int
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
    $userId: Int
  ) {
    getApiHistoryGetListVersion(
      limit: $limit
      offset: $offset
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
      userId: $userId
    ) {
      message
      status
      data {
        karteOrdRaiins {
          karteEdition {
            hokenGroups {
              groupOdrItems {
                odrInfs {
                  odrDetails {
                    youkaiekiCd
                    yohoSets {
                      createDate
                      createId
                      createMachine
                      hpId
                      isDeleted
                      isModified
                      itemCd
                      itemname
                      setId
                      sortNo
                      updateDate
                      updateId
                      updateMachine
                      userId
                      yohoKbn
                    }
                    yohoKbn
                    yjCd
                    yakkaiUnit
                    yakka
                    unitSbt
                    unitName
                    termVal
                    ten
                    syohoLimitKbn
                    syohoKbn
                    suryo
                    sinKouiKbn
                    sinDate
                    rpNo
                    rpEdaNo
                    rowNo
                    rikikaUnit
                    rikikaRate
                    reqCd
                    raiinNo
                    ptId
                    odrUnitName
                    odrTermVal
                    memoItem
                    masterSbt
                    kokuji2
                    kokuji1
                    kohatuKbn
                    kikakiUnit
                    kensaGaichu
                    kasan2
                    kasan1
                    jissiMachine
                    jissiKbn
                    jissiId
                    jissiDate
                    itemName
                    itemCd
                    isNodspRece
                    isKensaMstEmpty
                    isGetPriceInYakka
                    ipnName
                    ipnCd
                    hpId
                    hasCmtName
                    handanGrpKbn
                    drugKbn
                    fontColor
                    displayItemName
                    commentNewline
                    cnvUnitName
                    cnvTermVal
                    cmtOpt
                    cmtName
                    cmtColKeta4
                    cmtColKeta3
                    cmtColKeta2
                    cmtColKeta1
                    cmtCol4
                    cmtCol3
                    cmtCol2
                    cmtCol1
                    alternationIndex
                    bunkatu
                    buiKbn
                    rousaiKbn
                    centerName
                    centerCd
                    bunkatuKoui
                    centerItemCd1
                    centerItemCd2
                  }
                  updateName
                  updateMachine
                  updateDateDisplay
                  updateDate
                  tosekiKbn
                  syohoSbt
                  sortNo
                  sinDate
                  sikyuKbn
                  santeiKbn
                  rpNo
                  rpName
                  rpEdaNo
                  raiinNo
                  ptId
                  odrKouiKbn
                  isDeleted
                  inoutKbn
                  id
                  hpId
                  hokenPid
                  groupOdrKouiKbn
                  daysCnt
                  createName
                  createMachine
                  createId
                  createDateDisplay
                  createDate
                }
                tosekiKbn
                syohoSbt
                sinkyuName
                sikyuName
                sikyuKbn
                santeiName
                santeiKbn
                kouiCode
                isKensa
                isDrug
                inOutName
                inOutKbn
                hokenPid
                groupName
                groupKouiCode
              }
              hokenTitle
              hokenPid
            }
            karteStatus
            karteStatusText
            listKarteFiles {
              createDate
              createDateDisplay
              createName
              isDeleted
              isSchema
              dspFileName
              linkFile
              seqNo
              updateDate
              updateDateDisplay
              updateName
              fileName
            }
            ptId
            raiinNo
            updateDate
            updateId
            updateName
            karteHistories {
              karteData {
                createDate
                createDateDisplay
                createName
                hpId
                isDeleted
                karteKbn
                ptId
                raiinNo
                richText
                seqNo
                sinDate
                text
                updateDate
                updateDateDisplay
              }
              karteKbn
              kbnName
              kbnShortName
              sortNo
              canImage
            }
            isDeleted
            hpId
            headerOrderModels {
              updateUserName
              syosaisinKbn
              syosaishinBinding
              jikanKbn
              jikanBinding
              isDeleted
              hokenPattentName
              createDateBinding
            }
            edition
            createId
            createDate
            approvalId
            approvalDate
            approvalName
          }
          raiinNo
          santeiKbn
          santeiKbnDisplay
          sinDate
          sinEndTime
          sinStartTime
          sinryoTitle
          status
          syosaisinDisplay
          syosaisinKbn
          tagNo
          tantoFullName
          tantoId
          tantoName
          treatmentDepartmentId
          treatmentDepartmentTitle
          uketsukeName
          uketukeTime
          kaName
          kaId
          jikanKbn
          jikanDisplay
          hokenType
          hokenTitle
          hokenRate
          hokenPid
        }
        startPage
        total
      }
    }
  }
`;
