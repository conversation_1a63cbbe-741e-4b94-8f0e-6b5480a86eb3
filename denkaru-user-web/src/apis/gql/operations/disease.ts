import { gql } from "@apollo/client";

export const GET_LIST = gql`
  query getApiDiseasesGetList(
    $ptId: BigInt
    $sinDate: Int
    $hokenId: Int
    $requestFrom: Int
    $isContiFiltered: Boolean
    $isInMonthFiltered: Boolean
  ) {
    getApiDiseasesGetList(
      ptId: $ptId
      sinDate: $sinDate
      hokenId: $hokenId
      requestFrom: $requestFrom
      isContiFiltered: $isContiFiltered
      isInMonthFiltered: $isInMonthFiltered
    ) {
      data {
        diseaseList {
          isFreeWord
          isTenki
          isContinous
          isInMonth
          hokenId
          hokenPid
          icd10
          icd102013
          icd1012013
          icd1022013
          hpId
          ptId
          seqNo
          byomeiCd
          sortNo
          prefixSuffixList {
            code
            name
          }
          byomei
          isSuspect
          startDate
          tenkiKbn
          tenkiDate
          syubyoKbn
          sikkanKbn
          nanbyoCd
          isNodspRece
          isNodspKarte
          isDeleted
          id
          isImportant
          sinDate
          hosokuCmt
          togetuByomei
          sikkanCd
          delDate
          itemCd
          isAdopted
          createUser
          createDate
          updateDate
          updateUser
          fullByomei
          isDspRece
          receTenkiKbn
          tenKiBinding
          isFree
          isMain
          byomeiHankToZen
        }
      }
    }
  }
`;

export const UPSERT = gql`
  mutation postApiDiseasesUpsert(
    $input: EmrCloudApiRequestsDiseasesUpsertPtDiseaseListRequestInput
  ) {
    postApiDiseasesUpsert(
      emrCloudApiRequestsDiseasesUpsertPtDiseaseListRequestInput: $input
    ) {
      data {
        ids
      }
      status
      message
    }
  }
`;

export const GET_CHECK_DISEASES = gql`
  mutation postApiMedicalExaminationGetCheckDiseases(
    $input: EmrCloudApiRequestsMedicalExaminationGetCheckDiseaseRequestInput
  ) {
    postApiMedicalExaminationGetCheckDiseases(
      emrCloudApiRequestsMedicalExaminationGetCheckDiseaseRequestInput: $input
    ) {
      data {
        checkDiseaseItemOutputDatas {
          checkedDiseaseItems {
            byomei
            byomeiMst {
              byomeiCd
              byomeiType
              icd10
              nanByo
              sbyomei
              isAdopted
              sikkan
            }
            isAdopted
            nanByoCd
            odrItemNo
            sikkanCd
            ptDisease {
              byomei
              byomeiCd
              hokenPid
              hosokuCmt
              hpId
              isDeleted
              id
              nanbyoCd
              isTenki
              isSuspect
              prefixSuffixList {
                code
                name
              }
              ptId
              seqNo
              sikkanKbn
              sinDate
              sortNo
              startDate
              syubyoKbn
              tenkiDate
              tenkiKbn
            }
          }
          itemCd
          itemName
        }
      }
    }
  }
`;
