import { gql } from "@apollo/client";

export const CONFIRM_ONLINE_HISTORY_BY_PT_ID = gql`
  query getApiOnlineGetListOnlineConfirmationHistoryByPtId($ptId: BigInt) {
    getApiOnlineGetListOnlineConfirmationHistoryByPtId(ptId: $ptId) {
      data {
        onlineConfirmationHistoryList {
          id
          qcXmlMsgResponse {
            messageBody {
              prescriptionIssueSelect
              processingResultCode
              processingResultMessage
              processingResultStatus
              qualificationValidity
              prescriptionInfo {
                selectPrescriptionList {
                  prescriptionId
                  requestPrescriptionFileName
                }
              }
              qualificationConfirmSearchInfo {
                arbitraryIdentifier
                birthdate
                insuredBranchNumber
                insuredCardSymbol
                insuredIdentificationNumber
                insurerNumber
                limitApplicationCertificateRelatedConsFlg
              }
              resultList {
                resultOfQualificationConfirmation {
                  address
                  arbitraryIdentifier
                  birthdate
                  insuredBranchNumber
                  disqualificationDate
                  diagnosisInfoConsTime
                  diagnosisInfoConsFlg
                  diagnosisInfoAvailableTime
                  elderlyRecipientCertificateInfo {
                    elderlyRecipientCertificateDate
                    elderlyRecipientContributionRatio
                    elderlyRecipientValidEndDate
                    elderlyRecipientValidStartDate
                  }
                  insuredIdentificationNumber
                  insuredCertificateIssuanceDate
                  insuredCardValidDate
                  insuredCardSymbol
                  insuredCardExpirationDate
                  insuredCardClassification
                  insuredName
                  insuredPartialContributionRatio
                  insurerName
                  insurerNumber
                  limitApplicationCertificateRelatedConsFlg
                  limitApplicationCertificateRelatedConsTime
                  name
                  nameKana
                  nameOfOther
                  nameOfOtherKana
                  operationInfoAvailableTime
                  operationInfoConsFlg
                  operationInfoConsTime
                  personalFamilyClassification
                  pharmacistsInfoAvailableTime
                  pharmacistsInfoConsFlg
                  pharmacistsInfoConsTime
                  postNumber
                  preschoolClassification
                  qualificationDate
                  reasonOfLoss
                  referenceNumber
                  sex1
                  sex2
                  specificDiseasesCertificateRelatedConsFlg
                  specificDiseasesCertificateRelatedConsTime
                  specificHealthCheckupsInfoAvailableTime
                  specificHealthCheckupsInfoConsFlg
                  specificHealthCheckupsInfoConsTime
                  specificDiseasesCertificateList {
                    specificDiseasesCertificateInfo {
                      specificDiseasesCertificateDate
                      specificDiseasesDiseaseCategory
                      specificDiseasesSelfPay
                      specificDiseasesValidEndDate
                      specificDiseasesValidStartDate
                    }
                  }
                  publicExpenseResultList {
                    medicalTicketInfo {
                      consistencyFlag
                      designatedMedicalInstitutionCode
                      designatedMedicalInstitutionFlag
                      designatedMedicalInstitutionName
                      districtContactName
                      handlingContactName
                      injuryName1
                      injuryName2
                      injuryName3
                      issueNumber
                      medicalTicketExpirationDate
                      medicalTicketValidDate
                      medicalTreatmentMonth
                      medicalTreatmentType
                      prescriptionIssuerMedicalInstitutionCode
                      prescriptionIssuerMedicalInstitutionName
                      remarks1
                      remarks2
                      remarks3
                      selfPayAmount
                      singleOrCombinedUse
                      statusOfElderlyMedicalCare
                      statusOfInfecton
                      statusOfPrefecturalExpenses
                      statusOfSocialInsurance
                      ticketType
                    }
                  }
                  limitApplicationCertificateRelatedInfo {
                    limitApplicationCertificateClassification
                    limitApplicationCertificateClassificationFlag
                    limitApplicationCertificateDate
                    limitApplicationCertificateValidEndDate
                    limitApplicationCertificateLongTermDate
                    limitApplicationCertificateValidStartDate
                  }
                }
              }
            }
            messageHeader {
              arbitraryFileIdentifier
              characterCodeIdentifier
              errorCode
              errorMessage
              medicalInstitutionCode
              processExecutionTime
              qualificationConfirmationDate
              referenceClassification
              segmentOfResult
            }
          }
          uketukeStatus
          ptId
          prescriptionIssueType
          onlineConfirmationDate
          infoConsFlg
          confirmationType
          confirmationResult
        }
      }
    }
  }
`;

export const GET_ONLINE_CONFIRMATION_HISTORY_BY_ID = gql`
  query getApiOnlineGetListOnlineConfirmationHistoryModelById(
    $onlineConfirmationHisId: BigInt
  ) {
    getApiOnlineGetListOnlineConfirmationHistoryModelById(
      onlineConfirmationHisId: $onlineConfirmationHisId
    ) {
      data {
        onlineConfirmationHistoryList {
          id
          confirmationResult
          confirmationType
          infoConsFlg
          onlineConfirmationDate
          prescriptionIssueType
          ptId
          qcXmlMsgResponse {
            messageBody {
              resultList {
                resultOfQualificationConfirmation {
                  address
                  arbitraryIdentifier
                  birthdate
                  diagnosisInfoAvailableTime
                  diagnosisInfoConsFlg
                  diagnosisInfoConsTime
                  disqualificationDate
                  insuredBranchNumber
                  insuredCardClassification
                  insuredCardExpirationDate
                  insuredCardSymbol
                  insuredCardValidDate
                  insuredCertificateIssuanceDate
                  insuredIdentificationNumber
                  insuredName
                  insuredPartialContributionRatio
                  insurerName
                  insurerNumber
                  name
                  nameKana
                  nameOfOther
                  nameOfOtherKana
                  operationInfoAvailableTime
                  operationInfoConsFlg
                  operationInfoConsTime
                  personalFamilyClassification
                  pharmacistsInfoAvailableTime
                  pharmacistsInfoConsFlg
                  pharmacistsInfoConsTime
                  postNumber
                  preschoolClassification
                  qualificationDate
                  reasonOfLoss
                  referenceNumber
                  sex1
                  sex2
                  elderlyRecipientCertificateInfo {
                    elderlyRecipientCertificateDate
                    elderlyRecipientContributionRatio
                    elderlyRecipientValidEndDate
                    elderlyRecipientValidStartDate
                  }
                  limitApplicationCertificateRelatedInfo {
                    limitApplicationCertificateClassification
                    limitApplicationCertificateClassificationFlag
                    limitApplicationCertificateDate
                    limitApplicationCertificateLongTermDate
                    limitApplicationCertificateValidEndDate
                    limitApplicationCertificateValidStartDate
                  }
                  limitApplicationCertificateRelatedConsFlg
                  limitApplicationCertificateRelatedConsTime
                  publicExpenseResultList {
                    medicalTicketInfo {
                      consistencyFlag
                      designatedMedicalInstitutionCode
                      designatedMedicalInstitutionFlag
                      designatedMedicalInstitutionName
                      districtContactName
                      handlingContactName
                      injuryName1
                      injuryName2
                      injuryName3
                      issueNumber
                      medicalTicketExpirationDate
                      medicalTicketValidDate
                      medicalTreatmentMonth
                      medicalTreatmentType
                      prescriptionIssuerMedicalInstitutionCode
                      prescriptionIssuerMedicalInstitutionName
                      remarks1
                      remarks2
                      remarks3
                      selfPayAmount
                      singleOrCombinedUse
                      statusOfElderlyMedicalCare
                      statusOfInfecton
                      statusOfPrefecturalExpenses
                      statusOfSocialInsurance
                      ticketType
                    }
                  }
                  specificDiseasesCertificateList {
                    specificDiseasesCertificateInfo {
                      specificDiseasesCertificateDate
                      specificDiseasesDiseaseCategory
                      specificDiseasesSelfPay
                      specificDiseasesValidEndDate
                      specificDiseasesValidStartDate
                    }
                  }
                  specificDiseasesCertificateRelatedConsFlg
                  specificDiseasesCertificateRelatedConsTime
                  specificHealthCheckupsInfoAvailableTime
                  specificHealthCheckupsInfoConsFlg
                  specificHealthCheckupsInfoConsTime
                }
              }
              prescriptionInfo {
                selectPrescriptionList {
                  prescriptionId
                  requestPrescriptionFileName
                }
              }
              prescriptionIssueSelect
              processingResultCode
              processingResultMessage
              processingResultStatus
              qualificationConfirmSearchInfo {
                arbitraryIdentifier
                birthdate
                insuredBranchNumber
                insuredCardSymbol
                insuredIdentificationNumber
                insurerNumber
                limitApplicationCertificateRelatedConsFlg
              }
              qualificationValidity
            }
            messageHeader {
              arbitraryFileIdentifier
              characterCodeIdentifier
              errorCode
              errorMessage
              medicalInstitutionCode
              processExecutionTime
              qualificationConfirmationDate
              referenceClassification
              segmentOfResult
            }
          }
          uketukeStatus
        }
      }
    }
  }
`;
