import { gql } from "@/apis/gql/apollo-client";

export const CREATE_FCO_API_KEY = gql`
  mutation CreateFcoApiKey(
    $input: EmrCloudApiRequestsFcoApiKeyCreateFcoApiKeyRequestInput!
  ) {
    postApiFcoApiKeyCreateFcoApiKey(
      emrCloudApiRequestsFcoApiKeyCreateFcoApiKeyRequestInput: $input
    ) {
      status
      message
      data {
        data {
          fcoApiKeyId
          hpId
          apiKey
          secretKey
          label
          createdAt
          updatedAt
          deletedAt
          isDeleted
        }
      }
    }
  }
`;

export const GET_FCO_API_KEYS = gql`
  query GetFcoApiKeys {
    getApiFcoApiKeyGetFcoApiKeys {
      status
      message
      data {
        listData {
          fcoApiKeyId
          hpId
          apiKey
          secretKey
          label
          createdAt
          updatedAt
          deletedAt
          isDeleted
        }
      }
    }
  }
`;

export const EDIT_FCO_API_KEY = gql`
  mutation UpdateFcoApiKey(
    $input: EmrCloudApiRequestsFcoApiKeyUpdateFcoApiKeyRequestInput!
  ) {
    postApiFcoApiKeyUpdateFcoApiKey(
      emrCloudApiRequestsFcoApiKeyUpdateFcoApiKeyRequestInput: $input
    ) {
      status
      message
      data {
        data {
          fcoApiKeyId
          hpId
          apiKey
          secretKey
          label
          createdAt
          updatedAt
          deletedAt
          isDeleted
        }
      }
    }
  }
`;

export const DELETE_FCO_API_KEY = gql`
  mutation DeleteFcoApiKey(
    $input: EmrCloudApiRequestsFcoApiKeyDeleteFcoApiKeyRequestInput!
  ) {
    postApiFcoApiKeyDeleteFcoApiKey(
      emrCloudApiRequestsFcoApiKeyDeleteFcoApiKeyRequestInput: $input
    ) {
      status
      message
      data {
        isDeleted
      }
    }
  }
`;
