{
  // エディタのタブのサイズを2に設定
  "editor.tabSize": 2,
  // エディタがインデントを自動検出しないように設定
  "editor.detectIndentation": false,
  // 検索から特定のファイルやパターンを除外
  "search.exclude": {
    "package-lock.json": true
  },
  // デフォルトのフォーマッタとしてESLintを使用
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // ファイルを保存するときに自動的にフォーマットする
  "editor.formatOnSave": true,
  // 保存時に特定のコードアクションを実行
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit", // ESLintのすべての修正を適用
    "source.addMissingImports": "explicit" // 不足しているインポートを追加
  },
  // ESLintのルールのカスタマイズ
  "eslint.rules.customizations": [
    {
      "rule": "*",
      "severity": "warn"
    }
  ],
  // ワークスペースのTypeScriptバージョンを使用
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  // JavaScriptファイルの設定
  "[javascript]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // JSO<PERSON>、J<PERSON><PERSON>、YAMLファイルの設定
  "[json][jsonc][yaml]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // .gitignoreにリストされているファイルをPrettierの対象から除外
  "prettier.ignorePath": ".gitignore",
  // TypeScript Reactファイルの設定
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // TypeScriptファイルの設定
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "jest.virtualFolders": [
    {
      "name": "server",
      "rootPath": "server"
    },
    {
      "name": "user-client",
      "rootPath": "user-client"
    }
  ],
  "jest.autoRun": "off"
}
