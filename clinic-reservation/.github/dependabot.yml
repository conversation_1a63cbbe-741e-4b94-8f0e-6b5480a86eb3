# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/ja/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  - package-ecosystem: "npm"
    target-branch: "dev2"
    directory: "/server"
    schedule:
      # [note] 試験的に毎日発行するように設定、タイミングをみて作成頻度を調整
      interval: "daily"
      timezone: "Asia/Tokyo"
    # Ph1リリース完了までは現在のバージョンで固定
    open-pull-requests-limit: 3
  - package-ecosystem: "npm"
    target-branch: "dev2"
    directory: "/user-client"
    schedule:
      # [note] 試験的に毎日発行するように設定、タイミングをみて作成頻度を調整
      interval: "daily"
      timezone: "Asia/Tokyo"
    # Ph1リリース完了までは現在のバージョンで固定
    open-pull-requests-limit: 3
