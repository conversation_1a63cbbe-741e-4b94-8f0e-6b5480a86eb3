name: Invoke PR Create lambda
run-name: ${{ github.actor }}のコードを検証中です 🚀
on:
  push:
    branches:
      - dev1_x
      - main
  # workflow_dispatch: # 手動実行を許可

env:
  AWS_REGION: ap-northeast-1
  LAMBDA_FUNCTION_NAME: tf-common-dev-create-github-pr-on-push

jobs:
  invoke_lambda:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read

    steps:
      # - name: Checkout code
      #   uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::744728153173:role/test-hattori-lambda-pr
          aws-region: ${{ env.AWS_REGION }}

      - name: Invoke Lambda function
        id: invoke
        run: |
          BRANCH_NAME="${{ github.ref_name }}"
          # リポジトリのフルネーム（例: owner/repo-name）を取得
          REPO_FULL_NAME="${{ github.repository }}" 
          
          # Lambdaに渡すペイロードをJSON形式で一時ファイルに書き出す
          echo "{\"branch\": \"$BRANCH_NAME\", \"repo_fullname\": \"$REPO_FULL_NAME\"}" > payload.json
          
          echo "Invoking Lambda '${{ env.LAMBDA_FUNCTION_NAME }}' with payload from file: payload.json"
          
          aws lambda invoke --function-name ${{ env.LAMBDA_FUNCTION_NAME }} --payload fileb://payload.json lambda_response.json

      - name: Show Lambda response
        run: |
          echo "Lambda Function Response:"
          cat lambda_response.json