name: User-Client's CI
run-name: ${{ github.actor }} is testing out GitHub Actions 🚀
on:
  push:
    branches:
      - main
      - dev
      - prd_hotfix
      - dev1_x
      - dev2
  pull_request:
    types:
      - opened
      - reopened
      - synchronize # when new commit pushed to PR
    branches:
      - main
      - dev
      - prd_hotfix
      - dev1_x
      - dev2

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout
jobs:
  run-user-client-CI:
    name: Run user-client CI
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: ${{ vars.STG_AWS_ROLE_ARN }}
          aws-region: ap-northeast-1
      - name: Run CodeBuild
        uses: aws-actions/aws-codebuild-run-build@v1
        with:
          project-name: ${{ vars.STG_USER_CLIENT_CI_CODEBUILD_NAME }}
      - run: echo "🍏 This job's status is ${{ job.status }}."
