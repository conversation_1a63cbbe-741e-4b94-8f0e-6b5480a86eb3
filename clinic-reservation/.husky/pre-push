#!/bin/sh

if [ "$LEFTHOOK_VERBOSE" = "1" -o "$LEFTHOOK_VERBOSE" = "true" ]; then
  set -x
fi

if [ "$LEFTHOOK" = "0" ]; then
  exit 0
fi

call_lefthook()
{
  if test -n "$LEFTHOOK_BIN"
  then
    "$LEFTHOOK_BIN" "$@"
  elif lefthook -h >/dev/null 2>&1
  then
    lefthook "$@"
  else
    dir="$(git rev-parse --show-toplevel)"
    osArch=$(uname | tr '[:upper:]' '[:lower:]')
    cpuArch=$(uname -m | sed 's/aarch64/arm64/;s/x86_64/x64/')
    if test -f "$dir/node_modules/lefthook-${osArch}-${cpuArch}/bin/lefthook"
    then
      "$dir/node_modules/lefthook-${osArch}-${cpuArch}/bin/lefthook" "$@"
    elif test -f "$dir/node_modules/@evilmartians/lefthook/bin/lefthook-${osArch}-${cpuArch}/lefthook"
    then
      "$dir/node_modules/@evilmartians/lefthook/bin/lefthook-${osArch}-${cpuArch}/lefthook" "$@"
    elif test -f "$dir/node_modules/@evilmartians/lefthook-installer/bin/lefthook"
    then
      "$dir/node_modules/@evilmartians/lefthook-installer/bin/lefthook" "$@"
    elif test -f "$dir/node_modules/lefthook/bin/index.js"
    then
      "$dir/node_modules/lefthook/bin/index.js" "$@"
    
    elif bundle exec lefthook -h >/dev/null 2>&1
    then
      bundle exec lefthook "$@"
    elif yarn lefthook -h >/dev/null 2>&1
    then
      yarn lefthook "$@"
    elif pnpm lefthook -h >/dev/null 2>&1
    then
      pnpm lefthook "$@"
    elif swift package plugin lefthook >/dev/null 2>&1
    then
      swift package --disable-sandbox plugin lefthook "$@"
    elif command -v mint >/dev/null 2>&1
    then
      mint run csjones/lefthook-plugin "$@"
    else
      echo "Can't find lefthook in PATH"
    fi
  fi
}

call_lefthook run "pre-push" "$@"

# server/src配下のユニットテストを実行
echo "👉 server/srcのユニットテストを実行中..."
echo ""

# 現在のディレクトリを保存
CURRENT_DIR=$(pwd)
cd server && yarn test --testRegex="src/.*\\.spec\\.ts$" --coverage --collectCoverageFrom="src/**/*.ts" --collectCoverageFrom="!src/**/*.spec.ts" > test_output.txt
echo ""

# テスト結果の確認
if [ $? -ne 0 ]; then
  echo "❌ サーバーのユニットテストが失敗しました。プッシュは中止されます。"
  # 元のディレクトリに戻る
  cd "$CURRENT_DIR"
  exit 1
else
  # カバレッジの情報を取得 - 修正版
  COVERAGE=$(grep -A 5 "All files" test_output.txt | grep -o "[0-9]\+\.[0-9]\+" | head -1)
  if [ -z "$COVERAGE" ]; then
    COVERAGE="計算できませんでした"
  fi
  echo "✓ サーバーのユニットテストが成功しました。カバレッジ: ${COVERAGE}%"
fi 

# 元のディレクトリに戻る
cd "$CURRENT_DIR"

# テスト出力とカバレッジレポートの削除
rm -f server/test_output.txt
rm -f server/test-results.json
rm -rf coverage