import { Args, Int, Query, Resolver } from '@nestjs/graphql';

import { ExamTimeSlot } from '@/models/ExamTimeSlot';

import { ExamTimeSlotByConditionsInput } from './exam-time-slot.input';
import { ExamTimeSlotService } from './exam-time-slot.service';

@Resolver()
export default class ExamTimeSlotResolver {
  constructor(private examTimeSlotService: ExamTimeSlotService) {}

  @Query(() => [ExamTimeSlot])
  async getExamTimeSlotByConditions(
    @Args('params', { type: () => ExamTimeSlotByConditionsInput })
    params: ExamTimeSlotByConditionsInput,
  ): Promise<Array<ExamTimeSlot>> {
    return await this.examTimeSlotService.getExamTimeSlotByConditions(params);
  }

  @Query(() => ExamTimeSlot)
  async getExamTimeSlotById(
    @Args('id', { type: () => Int })
    id: number,
  ): Promise<ExamTimeSlot> {
    return await this.examTimeSlotService.getExamSlotById(id);
  }
}
