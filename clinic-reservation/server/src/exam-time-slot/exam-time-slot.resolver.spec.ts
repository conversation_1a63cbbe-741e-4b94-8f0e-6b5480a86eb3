import { Test, TestingModule } from '@nestjs/testing';

import { ExamTimeSlot } from '@/models/ExamTimeSlot';

import { ExamTimeSlotByConditionsInput } from './exam-time-slot.input';
import ExamTimeSlotResolver from './exam-time-slot.resolver';
import { ExamTimeSlotService } from './exam-time-slot.service';

/**
 * ExamTimeSlotResolverのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. 条件に基づく診察時間枠の取得
 * 2. IDに基づく診察時間枠の取得
 */
describe('ExamTimeSlotResolver', () => {
  let resolver: ExamTimeSlotResolver;
  let mockExamTimeSlotService: jest.Mocked<ExamTimeSlotService>;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - リゾルバのインスタンス化
   */
  beforeEach(async () => {
    // ExamTimeSlotServiceのモック
    mockExamTimeSlotService = {
      getExamTimeSlotByConditions: jest.fn(),
      getExamSlotById: jest.fn(),
    } as unknown as jest.Mocked<ExamTimeSlotService>;

    // テストモジュールの設定
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExamTimeSlotResolver,
        {
          provide: ExamTimeSlotService,
          useValue: mockExamTimeSlotService,
        },
      ],
    }).compile();

    resolver = module.get<ExamTimeSlotResolver>(ExamTimeSlotResolver);
  });

  // リゾルバが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  /**
   * getExamTimeSlotByConditionsメソッドのテスト
   */
  describe('getExamTimeSlotByConditions', () => {
    /**
     * テストケース: 条件に基づいて診察時間枠を取得
     * 条件: 有効な入力パラメータを指定
     * 想定結果: サービスから返された診察時間枠のリストが返される
     */
    it('should return exam time slots by conditions', async () => {
      // テストデータの準備
      const params: ExamTimeSlotByConditionsInput = {
        hospitalId: 1,
        examTimeSlotIds: [1, 2],
      };

      const mockExamTimeSlots = [
        {
          examTimeSlotId: 1,
          calendarId: 1,
          examStartDate: new Date(),
          examEndDate: new Date(),
          slotLimitReserveNum: 5,
          reservableSlots: 2,
        },
      ] as ExamTimeSlot[];

      // モックの戻り値を設定
      mockExamTimeSlotService.getExamTimeSlotByConditions.mockResolvedValue(
        mockExamTimeSlots,
      );

      // メソッドの実行
      const result = await resolver.getExamTimeSlotByConditions(params);

      // 期待する結果の検証
      expect(
        mockExamTimeSlotService.getExamTimeSlotByConditions,
      ).toHaveBeenCalledWith(params);
      expect(result).toEqual(mockExamTimeSlots);
    });
  });

  /**
   * getExamTimeSlotByIdメソッドのテスト
   */
  describe('getExamTimeSlotById', () => {
    /**
     * テストケース: IDに基づいて診察時間枠を取得
     * 条件: 有効なIDを指定
     * 想定結果: サービスから返された診察時間枠が返される
     */
    it('should return exam time slot by id', async () => {
      // テストデータの準備
      const id = 1;

      const mockExamTimeSlot = {
        examTimeSlotId: 1,
        calendarId: 1,
        examStartDate: new Date(),
        examEndDate: new Date(),
        slotLimitReserveNum: 5,
        reservableSlots: 2,
      } as ExamTimeSlot;

      // モックの戻り値を設定
      mockExamTimeSlotService.getExamSlotById.mockResolvedValue(
        mockExamTimeSlot,
      );

      // メソッドの実行
      const result = await resolver.getExamTimeSlotById(id);

      // 期待する結果の検証
      expect(mockExamTimeSlotService.getExamSlotById).toHaveBeenCalledWith(id);
      expect(result).toEqual(mockExamTimeSlot);
    });
  });
});
