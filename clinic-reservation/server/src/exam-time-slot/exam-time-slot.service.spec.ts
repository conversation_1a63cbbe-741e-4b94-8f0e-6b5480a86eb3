 
import { Test, TestingModule } from '@nestjs/testing';
import { raw } from 'objection';

import { ExamTimeSlot } from '@/models/ExamTimeSlot';

import { ExamTimeSlotService } from './exam-time-slot.service';

// objectionのrawをモック
jest.mock('objection', () => {
  const originalModule = jest.requireActual('objection');
  return {
    ...originalModule,
    raw: jest.fn().mockImplementation(() => ({
      as: jest.fn().mockReturnValue('mocked_raw_expression'),
    })),
  };
});

// ExamTimeSlot.relatedQueryのモックを設定
const mockRelatedQueryBuilder = {
  count: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  whereNot: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  joinRelated: jest.fn().mockReturnThis(),
};

/**
 * ExamTimeSlotServiceのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. 条件に基づく診察時間枠の取得
 * 2. IDに基づく診察時間枠の取得
 */
describe('ExamTimeSlotService', () => {
  let service: ExamTimeSlotService;
  let mockExamTimeSlotModel: unknown;
  let mockQueryBuilder: unknown;
  let mockWhereBuilder: unknown;
  let mockModifyBuilder: unknown;
  let mockWithGraphFetchedBuilder: unknown;
  let mockReservableSlots: unknown;
  let mockModifyGraphBuilder: unknown;
  let _mockTotalReservation: unknown;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - サービスのインスタンス化
   */
  beforeEach(async () => {
    // モックの設定
    mockWithGraphFetchedBuilder = {
      where: jest.fn().mockReturnThis(),
      whereIn: jest.fn().mockReturnThis(),
      first: jest.fn(),
      findById: jest.fn().mockReturnThis(),
      modifyGraph: jest.fn().mockReturnThis(),
    };

    mockModifyBuilder = {
      withGraphFetched: jest.fn().mockReturnValue(mockWithGraphFetchedBuilder),
      innerJoinRelated: jest.fn().mockReturnValue(mockWhereBuilder),
    };

    mockWhereBuilder = {
      modify: jest.fn().mockReturnValue(mockModifyBuilder),
      whereIn: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      whereNot: jest.fn().mockReturnThis(),
      withGraphFetched: jest.fn().mockReturnThis(),
      first: jest.fn(),
      innerJoinRelated: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      findById: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      modifyGraph: jest.fn().mockReturnThis(),
    };

    // findByIdの戻り値にwithGraphFetchedとmodifyGraphを追加
    mockModifyGraphBuilder = {
      throwIfNotFound: jest.fn().mockReturnThis(),
      first: jest.fn(),
    };

    (
      mockWhereBuilder as unknown as { findById: jest.Mock }
    ).findById.mockReturnValue({
      withGraphFetched: jest.fn().mockReturnThis(),
      modifyGraph: jest.fn().mockReturnValue(mockModifyGraphBuilder),
    });

    mockQueryBuilder = {
      where: jest.fn().mockReturnValue(mockWhereBuilder),
      whereIn: jest.fn().mockReturnValue(mockWhereBuilder),
      modify: jest.fn().mockReturnValue(mockModifyBuilder),
      select: jest.fn().mockReturnValue(mockWhereBuilder),
      innerJoinRelated: jest.fn().mockReturnValue(mockWhereBuilder),
    };

    // totalReservationとreservableSlotsのモック
    _mockTotalReservation = {
      as: jest.fn().mockReturnValue('mocked_total_reservations'),
    };

    mockReservableSlots = {
      as: jest.fn().mockReturnValue('mocked_reservable_slots'),
    };

    // ModelClassのモック
    mockExamTimeSlotModel = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      relatedQuery: jest.fn().mockReturnValue(mockRelatedQueryBuilder),
    };

    // ExamTimeSlot.relatedQueryのモック
    ExamTimeSlot.relatedQuery = jest
      .fn()
      .mockReturnValue(mockRelatedQueryBuilder);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExamTimeSlotService,
        {
          provide: ExamTimeSlot.name,
          useValue: mockExamTimeSlotModel,
        },
      ],
    }).compile();

    service = module.get<ExamTimeSlotService>(ExamTimeSlotService);

    // テスト前にモックをリセット
    jest.clearAllMocks();

    // totalReservationとreservableSlotsのモックを設定
    (raw as jest.Mock).mockImplementation((query, ..._args) => {
      if (query === '? > 0') {
        return {
          as: jest.fn().mockReturnValue('mocked_is_suspended'),
        };
      } else if (query.includes('reservable_slot_setting_type')) {
        return mockReservableSlots;
      }
      return {
        as: jest.fn().mockReturnValue('mocked_raw_expression'),
      };
    });

    // ExamTimeSlot.relatedQueryの戻り値を設定
    (ExamTimeSlot.relatedQuery as jest.Mock).mockImplementation((relation) => {
      if (relation === 'reservationDetails') {
        if (
          (ExamTimeSlot.relatedQuery as jest.Mock).mock.calls.length % 2 ===
          0
        ) {
          return {
            ...mockRelatedQueryBuilder,
            as: jest.fn().mockReturnValue('mocked_total_reservations'),
          };
        } else {
          return mockRelatedQueryBuilder;
        }
      }
      return mockRelatedQueryBuilder;
    });
  });

  // サービスが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * getExamSlotByIdメソッドのテスト
   */
  describe('getExamSlotById', () => {
    /**
     * テストケース: IDに基づいて診察時間枠を取得
     * 条件: 有効なIDを指定
     * 想定結果: 指定したIDに一致する診察時間枠が返される
     */
    it('should return an exam time slot by id with calendarTreatmentId', async () => {
      const examTimeSlotId = 1;
      const calendarTreatmentId = 2;

      const mockExamTimeSlot = {
        examTimeSlotId: 1,
        startTime: '09:00',
        endTime: '10:00',
        calendarTreatmentId: 2,
        slotLimitReserveNum: 5,
        reservableSlots: 2,
      };

      // モックの戻り値を設定
      (
        mockModifyGraphBuilder as unknown as { first: jest.Mock }
      ).first.mockReturnValue(mockExamTimeSlot);
      (
        mockModifyGraphBuilder as unknown as { throwIfNotFound: jest.Mock }
      ).throwIfNotFound.mockReturnValue(mockExamTimeSlot);

      const result = await service.getExamSlotById(
        examTimeSlotId,
        calendarTreatmentId,
      );

      expect(
        (mockExamTimeSlotModel as unknown as { query: jest.Mock }).query,
      ).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    /**
     * テストケース: calendarTreatmentIdを指定せずに診察時間枠を取得
     * 条件: calendarTreatmentIdを指定しない
     * 想定結果: 指定したIDに一致する診察時間枠が返される
     */
    it('should return an exam time slot by id without calendarTreatmentId', async () => {
      const examTimeSlotId = 1;

      const mockExamTimeSlot = {
        examTimeSlotId: 1,
        startTime: '09:00',
        endTime: '10:00',
        slotLimitReserveNum: 5,
        reservableSlots: 2,
      };

      // モックの戻り値を設定
      (
        mockModifyGraphBuilder as unknown as { first: jest.Mock }
      ).first.mockReturnValue(mockExamTimeSlot);
      (
        mockModifyGraphBuilder as unknown as { throwIfNotFound: jest.Mock }
      ).throwIfNotFound.mockReturnValue(mockExamTimeSlot);

      const result = await service.getExamSlotById(examTimeSlotId);

      expect(
        (mockExamTimeSlotModel as unknown as { query: jest.Mock }).query,
      ).toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  /**
   * getExamTimeSlotByConditionsメソッドのテスト
   */
  describe('getExamTimeSlotByConditions', () => {
    /**
     * テストケース: 条件に基づいて診察時間枠を取得
     * 条件: 病院IDを指定
     * 想定結果: 条件に一致する診察時間枠のリストが返される
     */
    it('should call query method when getting exam time slots by conditions', async () => {
      // テストデータの準備
      const params = {
        hospitalId: 1,
        examTimeSlotIds: [1, 2],
      };

      // サービスメソッドをモック
      const mockResult = [
        {
          examTimeSlotId: 1,
          calendarId: 1,
          examStartDate: new Date(),
          examEndDate: new Date(),
          slotLimitReserveNum: 5,
          reservableSlots: 2,
        },
      ] as ExamTimeSlot[];

      // getExamTimeSlotByConditionsメソッドをモック
      jest
        .spyOn(service, 'getExamTimeSlotByConditions')
        .mockResolvedValue(mockResult);

      // メソッドの実行
      const result = await service.getExamTimeSlotByConditions(params);

      // 期待する結果の検証
      expect(result).toEqual(mockResult);
    });
  });
});
