import { Inject, Injectable } from '@nestjs/common';
import { ModelClass, raw } from 'objection';

import {
  IS_DELETED,
  RESERVABLE_SLOT_SETTING_TYPE,
  RESERVATION_STATUS,
  TREATMENT_TYPE,
} from '@/common/constants/master-type';
import { ExamTimeSlot } from '@/models/ExamTimeSlot';

import { ExamTimeSlotByConditionsInput } from './exam-time-slot.input';

@Injectable()
export class ExamTimeSlotService {
  constructor(
    @Inject(ExamTimeSlot.name) private examTimeSlot: ModelClass<ExamTimeSlot>,
  ) {}

  async getExamTimeSlotByConditions(
    params: ExamTimeSlotByConditionsInput,
  ): Promise<ExamTimeSlot[]> {
    const isSuspended = ExamTimeSlot.relatedQuery('reservationDetails')
      .count('reserveDetailId')
      .where('reservationDetails.isSuspended', true)
      .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
      .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE);

    const totalReservation = ExamTimeSlot.relatedQuery('reservationDetails')
      .count('reserveDetailId')
      .where('reservationDetails.isSuspended', false)
      .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
      .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE);

    const reservedSlots = raw(
      'case when c.reservable_slot_setting_type = ? then ? else ? end',
      RESERVABLE_SLOT_SETTING_TYPE.BY_FIX_NUMBER_OF_PATIENT,
      ExamTimeSlot.relatedQuery('reservationDetails')
        .count('reserve_detail_id')
        .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
        .andWhere('reservationDetails.isSuspended', false)
        .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE),
      ExamTimeSlot.relatedQuery('reservationDetails')
        .select(
          raw(
            `coalesce(SUM(case when reservation_details.treatment_type = ? then "ct:td".first_consultation_time else "ct:td".next_consultation_time end),0)`,
            TREATMENT_TYPE.FIRST_EXAM,
          ),
        )
        .joinRelated('calendarTreatment.treatmentDepartment', {
          aliases: {
            calendarTreatment: 'ct',
            treatmentDepartment: 'td',
          },
        })
        .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
        .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE)
        .andWhere('reservationDetails.isSuspended', false)
        .andWhere('ct:td.isDeleted', IS_DELETED.FALSE),
    );

    const queryBuilder = this.examTimeSlot
      .query()
      .select([
        'examTimeSlot.*',
        'examTimeSlotId',
        raw('? > 0', isSuspended).as('is_suspended'),
        totalReservation.as('total_reservations'),
        reservedSlots.as('reserved_slots'),
      ])
      .modify('filterByRangeDate', {
        examStartDate: params.examStartDate,
        examEndDate: params.examEndDate,
      })
      .innerJoinRelated(
        'calendar.[calendarTreatments, hospital.portalHospital]',
        {
          aliases: {
            calendar: 'c',
            calendarTreatments: 'cts',
            hospital: 'h',
            portalHospital: 'ph',
          },
        },
      )
      .whereNot('slotLimitReserveNum', 0)
      .andWhere('examTimeSlot.isDeleted', IS_DELETED.FALSE)
      .andWhere('c:h:ph.hospitalId', params.hospitalId)
      .andWhere('c:h:ph.isDeleted', IS_DELETED.FALSE)
      .andWhere('c:cts.isDeleted', IS_DELETED.FALSE)
      .orderBy('examStartDate')
      .groupBy('examTimeSlotId', 'reservableSlotSettingType')
      .withGraphFetched('calendar')
      .modifyGraph('calendar', (builder) => {
        builder.where('calendar.isDeleted', IS_DELETED.FALSE);
      });

    if (params.treatmentId) {
      queryBuilder.where('treatmentDepartmentId', params.treatmentId);
      isSuspended
        .joinRelated('calendarTreatment.treatmentDepartment', {
          aliases: {
            calendarTreatment: 'ct',
            treatmentDepartment: 'td',
          },
        })
        .where('ct.isDeleted', IS_DELETED.FALSE)
        .where('ct:td.isDeleted', IS_DELETED.FALSE)
        .where('ct:td.treatmentDepartmentId', params.treatmentId);
    }

    if (params.examTimeSlotIds) {
      queryBuilder.whereIn('examTimeSlotId', params.examTimeSlotIds);
    }

    const result = await queryBuilder;

    return result.map(
      (examTimeSlot) =>
        ({
          ...examTimeSlot,
          reservableSlots:
            examTimeSlot.slotLimitReserveNum -
            (examTimeSlot?.reservedSlots || 0),
        } as ExamTimeSlot),
    );
  }

  async getExamSlotById(
    id: number,
    calendarTreatmentId?: number,
  ): Promise<ExamTimeSlot> {
    const isSuspended = ExamTimeSlot.relatedQuery('reservationDetails')
      .count('reserveDetailId')
      .where('reservationDetails.isSuspended', true)
      .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
      .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE);

    const totalReservation = ExamTimeSlot.relatedQuery('reservationDetails')
      .count('reserveDetailId')
      .where('reservationDetails.isSuspended', false)
      .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
      .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE);

    const reservedSlots = raw(
      'case when calendar.reservable_slot_setting_type = ? then ? else ? end',
      RESERVABLE_SLOT_SETTING_TYPE.BY_FIX_NUMBER_OF_PATIENT,
      ExamTimeSlot.relatedQuery('reservationDetails')
        .count('reserve_detail_id')
        .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
        .andWhere('reservationDetails.isSuspended', false)
        .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE),
      ExamTimeSlot.relatedQuery('reservationDetails')
        .select(
          raw(
            `coalesce(SUM(case when reservation_details.treatment_type = ? then "ct:td".first_consultation_time else "ct:td".next_consultation_time end),0)`,
            TREATMENT_TYPE.FIRST_EXAM,
          ),
        )
        .joinRelated('calendarTreatment.treatmentDepartment', {
          aliases: {
            calendarTreatment: 'ct',
            treatmentDepartment: 'td',
          },
        })
        .whereNot('reservationDetails.status', RESERVATION_STATUS.CANCELED)
        .andWhere('reservationDetails.isDeleted', IS_DELETED.FALSE)
        .andWhere('reservationDetails.isSuspended', false)
        .andWhere('ct:td.isDeleted', IS_DELETED.FALSE),
    );

    if (calendarTreatmentId) {
      isSuspended
        .joinRelated('calendarTreatment')
        .where('calendarTreatment.calendarTreatmentId', calendarTreatmentId);
    }

    const result = await this.examTimeSlot
      .query()
      .select([
        'examTimeSlot.*',
        'examTimeSlotId',
        raw('? > 0', isSuspended).as('is_suspended'),
        totalReservation.as('total_reservations'),
        reservedSlots.as('reserved_slots'),
      ])
      .innerJoinRelated('calendar')
      .where('calendar.isDeleted', IS_DELETED.FALSE)
      .andWhere('examTimeSlot.isDeleted', IS_DELETED.FALSE)
      .findById(id)
      .withGraphFetched(
        '[reservationDetails(notCancel), calendar.calendarBasicSettings]',
      )
      .modifyGraph('reservationDetails', (builder) => {
        builder.where('isDeleted', IS_DELETED.FALSE);
      })
      .throwIfNotFound();

    result.reservableSlots =
      result.slotLimitReserveNum - (result?.reservedSlots || 0);

    return result;
  }
}
