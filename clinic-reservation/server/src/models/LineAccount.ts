import { Field, ObjectType } from '@nestjs/graphql';

import { LINE_LINKING_TYPE } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';

@ObjectType()
export class LineAccount extends BaseModel {
  static tableName = 'line_account';

  static idColumn = 'id';

  @Field(() => Number, { nullable: false })
  id: number;

  @Field(() => Number, { nullable: true })
  customerId: number;

  @Field(() => String, { nullable: false })
  lineUserId: string;

  @Field(() => String, { nullable: true })
  displayName: string;

  @Field(() => String, { nullable: true })
  pictureUrl: string;

  @Field(() => Number, {
    nullable: false,
    defaultValue: LINE_LINKING_TYPE.UNLINKED,
  })
  isLinked?: number;

  @Field(() => String, { nullable: true })
  lineLinkToken: string;

  @Field(() => Date, { nullable: true })
  linkTokenExpiry: Date;
}
