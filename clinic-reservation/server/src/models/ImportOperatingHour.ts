import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class ImportOperatingHour extends BaseModel {
  static tableName = 'portal_m_import_operating_hour';

  static idColumn = 'operatingHourId';

  @Field(() => Number, { nullable: false })
  operatingHourId: number;

  @Field(() => String, { nullable: false })
  scuelId: string;

  @Field(() => Date, { nullable: false })
  timeCategory: Date; // サービス_時間区分

  @Field(() => String, { nullable: false })
  typeCode: string; // サービス_種類コード

  @Field(() => String, { nullable: false })
  typeName: string; // サービス_種類名

  @Field(() => String, { nullable: false })
  serviceDay: string; // サービス_曜日

  @Field(() => Number, { nullable: false })
  timeSerial: number; // サービス_時間連番

  @Field(() => String, { nullable: false })
  startTime: string; // サービス_開始時間

  @Field(() => String, { nullable: false })
  endTime: string; // サービス_終了時間

  @Field(() => String, { nullable: true })
  earliestStartTime: string;

  @Field(() => String, { nullable: true })
  latestEndTime: string;
}
