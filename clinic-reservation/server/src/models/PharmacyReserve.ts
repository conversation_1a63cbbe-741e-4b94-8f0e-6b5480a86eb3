import { Field, ObjectType } from '@nestjs/graphql';
import { Model, RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { Meeting } from './Meeting';
import { Patient } from './Patient';
import { PharmacyDeliveryAddress } from './PharmacyDeliveryAddress';
import { PharmacyDeliveryHistory } from './PharmacyDeliveryHistory';
import { PharmacyDesiredDate } from './PharmacyDesiredDate';
import { PharmacyReserveDetail } from './PharmacyReserveDetail';
import { Reservation } from './Reservation';

@ObjectType()
export class PharmacyReserve extends BaseModel {
  static tableName = 'pharmacyReserve';

  static idColumn = 'pharmacyReserveId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    patient: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/Patient'),
      join: {
        from: 'pharmacyReserve.patientId',
        to: 'ptInf.ptId',
      },
      modify: (builder) => {
        builder.where('ptInf.isDelete', IS_DELETED.FALSE);
      },
    },
    reservation: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/Reservation'),
      join: {
        from: 'pharmacyReserve.reserveId',
        to: 'reserve.reserveId',
      },
      modify: (builder) => {
        builder.where('reserve.isDeleted', IS_DELETED.FALSE);
      },
    },
    pharmacyReservationDetails: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/PharmacyReserveDetail'),
      filter: (builder) => builder.where('isDeleted', IS_DELETED.FALSE),
      join: {
        from: 'pharmacyReserve.pharmacyReserveId',
        to: 'pharmacyReserveDetail.pharmacyReserveId',
      },
    },
    pharmacyDeliveryAddress: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/PharmacyDeliveryAddress'),
      join: {
        from: 'pharmacyReserve.pharmacyReserveId',
        to: 'pharmacyDeliveryAddress.pharmacyReserveId',
      },
      modify: (builder) => {
        builder.where('pharmacyDeliveryAddress.isDeleted', IS_DELETED.FALSE);
      },
    },
    meetings: {
      relation: Model.HasManyRelation,
      modelClass: join(__dirname, '/Meeting'),
      filter: (builder) => builder.where('isDeleted', IS_DELETED.FALSE),
      join: {
        from: 'pharmacyReserve.pharmacyReserveId',
        to: 'meeting.pharmacyReserveId',
      },
    },
    pharmacyDesiredDates: {
      relation: Model.HasManyRelation,
      modelClass: join(__dirname, '/PharmacyDesiredDate'),
      join: {
        from: 'pharmacyReserve.pharmacyReserveId',
        to: 'pharmacyDesiredDate.pharmacyReserveId',
      },
      modify: (builder) => {
        builder.where('pharmacyDesiredDate.isDeleted', IS_DELETED.FALSE);
      },
    },
    pharmacyDeliveryHistories: {
      relation: Model.HasManyRelation,
      modelClass: join(__dirname, '/PharmacyDeliveryHistory'),
      join: {
        from: 'pharmacyReserve.pharmacyReserveId',
        to: 'pharmacyDeliveryHistory.pharmacyReserveId',
      },
      modify: (builder) => {
        builder.where('pharmacyDeliveryHistory.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  @Field(() => Number, { nullable: false })
  pharmacyReserveId: number;

  @Field(() => Number, { nullable: false })
  patientId: number;

  @Field(() => Number, { nullable: false })
  desiredDateStatus: number;

  @Field(() => Date, { nullable: false })
  reserveUpdateDate: Date;

  @Field(() => Number, { nullable: true })
  reserveId?: number;

  @Field(() => Number, { nullable: false })
  smsStatus: number;

  @Field(() => Number, { nullable: false })
  videocallStatus: number;

  @Field(() => Number, { nullable: false })
  postalServiceType: number;

  @Field(() => Number, { nullable: false })
  csvStatus: number;

  @Field(() => Number, { nullable: false })
  pharmacistStatus: number;

  @Field(() => Number, { nullable: true })
  pharmacistId?: number;

  @Field(() => [PharmacyDesiredDate], { nullable: false })
  pharmacyDesiredDates: PharmacyDesiredDate[];

  @Field(() => [PharmacyReserveDetail], { nullable: false })
  pharmacyReservationDetails: PharmacyReserveDetail[];

  @Field(() => PharmacyDeliveryAddress, { nullable: true })
  pharmacyDeliveryAddress: PharmacyDeliveryAddress;

  @Field(() => [Meeting], { nullable: true })
  meetings: Meeting[];

  @Field(() => Reservation, { nullable: true })
  reservation?: Reservation;

  @Field(() => Patient, { nullable: false })
  patient: Patient;

  @Field(() => [PharmacyDeliveryHistory], { nullable: false })
  pharmacyDeliveryHistories: PharmacyDeliveryHistory[];

  @Field(() => String, { nullable: true })
  cardExpire?: string;

  sortDate: Date;
}
