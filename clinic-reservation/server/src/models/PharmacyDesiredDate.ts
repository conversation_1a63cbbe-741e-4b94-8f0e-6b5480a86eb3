import { Field, ObjectType } from '@nestjs/graphql';
import { AnyQueryBuilder, Model, Modifiers } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PharmacyDesiredDate extends BaseModel {
  static tableName = 'pharmacyDesiredDate';

  static idColumn = 'pharmacyDesiredDateId';

  static relationMappings = {
    pharmacyReserve: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/PharmacyReserve'),
      join: {
        from: 'pharmacyDesiredDate.pharmacyReserveId',
        to: 'pharmacyReserve.pharmacyReserveId',
      },
    },
  };

  static modifiers: Modifiers<AnyQueryBuilder> = {
    isActive(builder) {
      builder.where('isDeleted', IS_DELETED.FALSE);
    },
    orderByCreatedAt(builder) {
      builder.orderBy([
        { column: 'createdAt', order: 'desc' },
        { column: 'pharmacyDesiredDateId', order: 'desc' },
      ]);
    },
  };

  @Field(() => Number, { nullable: false })
  pharmacyDesiredDateId: number;

  @Field(() => Number, { nullable: false })
  pharmacyReserveId: number;

  @Field(() => Number, { nullable: false })
  desiredType: number;

  @Field(() => Date, { nullable: false })
  desiredDate: Date;

  @Field(() => Number, { nullable: false })
  isDeleted: number;
}
