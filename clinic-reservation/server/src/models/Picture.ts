import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';
import { ContentsPublish } from './ContentsPublish';

@ObjectType()
export class Picture extends BaseModel {
  static tableName = 'portalPict';

  static idColumn = 'pictId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    publish: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/ContentsPublish'),
      join: {
        from: 'portalPict.pictId',
        to: 'portalContentsPub.pictId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  pictId: number;

  @Field(() => String, { nullable: false })
  filepath: string;

  @Field(() => String, { nullable: false })
  fileName: string;

  @Field(() => [ContentsPublish], { nullable: false })
  publish: ContentsPublish[];
}
