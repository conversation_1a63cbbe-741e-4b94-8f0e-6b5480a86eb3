import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { Patient } from './Patient';
import { PharmacyReserve } from './PharmacyReserve';
import { Reservation } from './Reservation';

@ObjectType()
export class Meeting extends BaseModel {
  static tableName = 'meeting';

  static idColumn = 'meetingId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    patient: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Patient'),
      join: {
        from: 'meeting.patientId',
        to: 'ptInf.ptId',
      },
      modify: (builder) => {
        builder.where('ptInf.isDelete', IS_DELETED.FALSE);
      },
    },
    reservation: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Reservation'),
      join: {
        from: 'meeting.reserveId',
        to: 'reserve.reserveId',
      },
      modify: (builder) => {
        builder.where('reserve.isDeleted', IS_DELETED.FALSE);
      },
    },
    pharmacyReserve: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/PharmacyReserve'),
      join: {
        from: 'meeting.pharmacyReserveId',
        to: 'pharmacyReserve.pharmacyReserveId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  meetingId: number;

  @Field(() => String, { nullable: true })
  chimeMeetingId: string | null;

  @Field(() => Number, { nullable: false })
  hospitalId: number;

  @Field(() => Number, { nullable: true })
  reserveId: number;

  @Field(() => Reservation, { nullable: true })
  reservation: Reservation;

  @Field(() => Number, { nullable: true })
  pharmacyReserveId: number;

  @Field(() => PharmacyReserve, { nullable: true })
  pharmacyReserve: PharmacyReserve;

  @Field(() => Number, { nullable: false })
  patientId: number;

  @Field(() => Patient, { nullable: true })
  patient: Patient;

  @Field(() => Number, { nullable: false })
  isBothJoined: number;

  @Field(() => Number, { nullable: false })
  status: number;
}
