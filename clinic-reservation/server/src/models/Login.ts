import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';
import { Customer } from './Customer';
import { CustomerSocialLogin } from './CustomerSocialLogin';

@ObjectType()
export class Login extends BaseModel {
  static tableName = 'portalCustomerLogin';

  static idColumn = 'loginId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    customer: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Customer'),
      join: {
        from: `portalCustomerLogin.customerId`,
        to: `portalCustomer.customerId`,
      },
    },
    socialLogins: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/CustomerSocialLogin'),
      join: {
        from: `portalCustomerLogin.loginId`,
        to: `portalCustomerSocialLogin.loginId`,
      },
    },
  };

  @Field(() => Number, { nullable: false })
  loginId: number;

  @Field(() => Number, { nullable: false })
  customerId: number;

  @Field(() => String, { nullable: false })
  email: string;

  passwordHash: string;

  loginRefreshToken: string;

  @Field(() => Customer, { nullable: true })
  customer: Customer;

  @Field(() => [CustomerSocialLogin], { nullable: false })
  socialLogins: CustomerSocialLogin[];

  @Field(() => Number, { nullable: false })
  missLoginCount: number;
}
