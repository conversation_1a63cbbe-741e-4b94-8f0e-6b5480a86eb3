import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

export enum EventKeyEnum {
  HOSPITAL_SEARCH = 'hospital_search',
  HOSPITAL_SEARCH_KEYWORD_CHANGE = 'hospital_search_keyword_change',
}

registerEnumType(EventKeyEnum, {
  name: 'EventKeyEnum',
});

@ObjectType()
export class PortalEventCountLog extends BaseModel {
  static tableName = 'portalEventCountLog';

  static idColumn = ['eventKey', 'eventDate'];

  @Field(() => EventKeyEnum, { nullable: false })
  eventKey: EventKeyEnum;

  @Field(() => Number, { nullable: false })
  eventCount: number;

  @Field(() => String, { nullable: false })
  eventDate: string;
}
