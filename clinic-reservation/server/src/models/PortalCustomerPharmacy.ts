import { Field, ObjectType } from '@nestjs/graphql';
import { Model } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PortalCustomerPharmacy extends BaseModel {
  static tableName = 'portalCustomerPharmacy';

  static idColumn = 'portalCustomerPharmacyId';

  static relationMappings = {
    reserve: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/Reservation'),
      join: {
        from: 'portalCustomerPharmacy.reserveId',
        to: 'reserve.reserveId',
      },
    },
    customer: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/Customer'),
      join: {
        from: 'portalCustomerPharmacy.customerId',
        to: 'portalCustomer.customerId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  portalCustomerPharmacyId: number;

  @Field(() => Number, { nullable: false })
  reserveId: number;

  @Field(() => Number, { nullable: false })
  customerId: number;

  @Field(() => String, { nullable: false })
  pharmacyName: string;

  @Field(() => String, { nullable: true })
  pharmacyStoreName: string;

  @Field(() => String, { nullable: false })
  faxNumber: string;

  @Field(() => String, { nullable: false })
  address1: string;

  @Field(() => String, { nullable: true })
  address2?: string;

  @Field(() => String, { nullable: false })
  postCode: string;

  @Field(() => String, { nullable: false })
  phoneNumber: string;
}
