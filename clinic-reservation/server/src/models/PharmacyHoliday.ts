import { Field, ObjectType } from '@nestjs/graphql';
import { Model } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PharmacyHoliday extends BaseModel {
  static tableName = 'pharmacyHoliday';

  static idColumn = 'pharmacyHolidayId';

  static relationMappings = {
    hospital: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/HospitalInfo'),
      join: {
        from: 'pharmacyHoliday.hospitalId',
        to: 'hp_inf.hpId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  pharmacyHolidayId: number;

  @Field(() => Number, { nullable: false })
  hospitalId: number;

  @Field(() => Date, { nullable: false })
  holidayStartDate: Date;

  @Field(() => Date, { nullable: false })
  holidayEndDate: Date;
}
