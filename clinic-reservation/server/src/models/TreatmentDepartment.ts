import { Field, ObjectType } from '@nestjs/graphql';
import {
  AnyQueryBuilder,
  Model,
  Modifiers,
  RelationMappings,
  RelationMappingsThunk,
} from 'objection';
import { join } from 'path';

import {
  IS_ACTIVE,
  IS_DELETED,
  TREATMENT_DEPARTMENT_STATUS,
  TREATMENT_PORTAL_PUBLIC_STATUS,
} from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { CalendarTreatment } from './CalendarTreatment';
import { HospitalInfo } from './HospitalInfo';
import { PortalHospital } from './PortalHospital';
import { Survey } from './Survey';
import { TreatmentCategory } from './TreatmentCategory';
import { TreatmentFeeList } from './TreatmentFeeList';

@ObjectType()
export class TreatmentDepartment extends BaseModel {
  static tableName = 'treatmentDepartment';
  static idColumn = 'treatmentDepartmentId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    treatmentCategory: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/TreatmentCategory'),
      join: {
        from: 'treatmentDepartment.treatmentCategoryId',
        to: 'mTreatmentCategory.treatmentCategoryId',
      },
      modify: (builder) => {
        builder.where('mTreatmentCategory.isDeleted', IS_DELETED.FALSE);
      },
    },
    calendarTreatments: {
      relation: Model.HasManyRelation,
      modelClass: join(__dirname, '/CalendarTreatment'),
      join: {
        from: 'treatmentDepartment.treatmentDepartmentId',
        to: 'calendarTreatment.treatmentDepartmentId',
      },
    },
    treatmentFeeList: {
      relation: Model.HasManyRelation,
      modelClass: join(__dirname, '/TreatmentFeeList'),
      join: {
        from: 'treatmentDepartment.treatmentDepartmentId',
        to: 'treatmentFeeList.treatmentDepartmentId',
      },
      modify: (builder) => {
        builder.where('treatmentFeeList.isDeleted', IS_DELETED.FALSE);
      },
    },
    hospital: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/HospitalInfo'),
      join: {
        from: 'treatmentDepartment.hospitalId',
        to: 'hpInf.hpId',
      },
      modify: (builder) => {
        builder.where('hpInf.isDeleted', IS_DELETED.FALSE);
      },
    },
    portalHospital: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/PortalHospital'),
      join: {
        from: 'treatmentDepartment.hospitalId',
        to: 'portalHospital.hpInfId',
      },
      modify: (builder) => {
        builder.where('portalHospital.isDeleted', IS_DELETED.FALSE);
        builder.where('portalHospital.isActive', IS_ACTIVE.TRUE);
      },
    },
    firstMedicalInterviewForm: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/Survey'),
      join: {
        from: 'treatmentDepartment.firstMedicalInterviewFormId',
        to: 'survey.surveyId',
      },
      modify: (builder) => {
        builder.where('survey.isDeleted', IS_DELETED.FALSE);
      },
    },
    nextMedicalInterviewForm: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/Survey'),
      join: {
        from: 'treatmentDepartment.nextMedicalInterviewFormId',
        to: 'survey.surveyId',
      },
      modify: (builder) => {
        builder.where('survey.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  static modifiers: Modifiers<AnyQueryBuilder> = {
    isActive(builder) {
      builder
        .where('portalPublicStatus', TREATMENT_PORTAL_PUBLIC_STATUS.PUBLIC)
        .where('treatmentDepartmentStatus', TREATMENT_DEPARTMENT_STATUS.VALID)
        .where('treatmentDepartment.isDeleted', 0);
    },
  };

  @Field(() => Number, { nullable: false })
  treatmentDepartmentId: number;

  @Field(() => Number, { nullable: false })
  treatmentCategoryId: number;

  @Field(() => TreatmentCategory, { nullable: true })
  treatmentCategory: TreatmentCategory;

  @Field(() => Number, { nullable: false })
  hospitalId: number;

  @Field(() => String, { nullable: false })
  title: string;

  @Field(() => String, { nullable: false })
  description: string;

  @Field(() => String, { nullable: true })
  note: string | null;

  @Field(() => Number, { nullable: false })
  treatmentType: number;

  @Field(() => Number, { nullable: false })
  treatmentMethod: number;

  @Field(() => Number, { nullable: false })
  firstConsultationTime: number;

  @Field(() => Number, { nullable: false })
  nextConsultationTime: number;

  @Field(() => Number, { nullable: true })
  firstMedicalInterviewFormId: number | null;

  @Field(() => Number, { nullable: true })
  nextMedicalInterviewFormId: number | null;

  @Field(() => Number, { nullable: false })
  treatmentDepartmentStatus: number;

  @Field(() => Number, { nullable: false })
  portalPublicStatus: number;

  @Field(() => [Number], { nullable: true })
  reservationMethodTypes: Array<number>;

  @Field(() => [CalendarTreatment], { nullable: true })
  calendarTreatments: Array<CalendarTreatment>;

  @Field(() => HospitalInfo, { nullable: true })
  hospital: HospitalInfo;

  @Field(() => PortalHospital, { nullable: true })
  portalHospital: PortalHospital;

  @Field(() => Survey, { nullable: true })
  firstMedicalInterviewForm: Survey;

  @Field(() => Survey, { nullable: true })
  nextMedicalInterviewForm: Survey;

  @Field(() => Number, { nullable: false })
  order: number;

  @Field(() => String, { nullable: true })
  specialNote: string | null;

  @Field(() => String, { nullable: true })
  feeListNote: string | null;

  @Field(() => [TreatmentFeeList], { nullable: true })
  treatmentFeeList: Array<TreatmentFeeList>;
}
