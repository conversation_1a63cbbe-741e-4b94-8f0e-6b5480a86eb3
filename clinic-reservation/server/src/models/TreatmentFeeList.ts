import { Field, ObjectType } from '@nestjs/graphql';
import { Model, RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';

@ObjectType()
export class TreatmentFeeList extends BaseModel {
  static tableName = 'treatmentFeeList';
  static idColumn = 'feeId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    treatmentDepartment: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/TreatmentDepartment'),
      join: {
        from: 'treatmentFeeList.treatmentDepartmentId',
        to: 'treatmentDepartment.treatmentDepartmentId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  treatmentDepartmentId: number;

  @Field(() => Number, { nullable: false })
  feeId: number;

  @Field(() => String, { nullable: false })
  title: string;

  @Field(() => Number, { nullable: true })
  minFee: number;

  @Field(() => Number, { nullable: true })
  maxFee: number;

  @Field(() => Boolean, { nullable: false })
  isPriceRange: boolean;
}
