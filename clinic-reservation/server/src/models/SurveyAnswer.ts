import { Field, ObjectType } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-scalars';

import {
  CustomerSurveyCommon,
  CustomerSurveyPharmacy,
} from '@/customer-survey/customer-survey.input';

import { BaseModel } from './BaseModel';

@ObjectType()
export class SurveyAnswer extends BaseModel {
  static tableName = 'surveyAnswer';

  static idColumn = 'surveyAnswerId';

  @Field(() => Number, { nullable: false })
  surveyAnswerId: number;

  @Field(() => GraphQLJSON, { nullable: true })
  surveyAnswer: string | null;

  @Field(() => GraphQLJSON, { nullable: true })
  commonSurvey: CustomerSurveyCommon | null;

  @Field(() => GraphQLJSON, { nullable: true })
  pharmacySurvey: CustomerSurveyPharmacy | null;

  @Field(() => Number, { nullable: true })
  reserveDetailId?: number;

  @Field(() => Number, { nullable: false })
  patientId: number;

  @Field(() => Number, { nullable: false })
  hospitalId: number;

  @Field(() => Number, { nullable: true })
  treatmentType?: number;

  @Field(() => Number, { nullable: false })
  treatmentDepartmentId: number;

  @Field(() => Number, { nullable: true })
  pharmacyReserveDetailId?: number;
}
