import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class ImportMultilingual extends BaseModel {
  static tableName = 'portal_m_import_multilingual';

  static idColumn = 'multilingualId';

  @Field(() => Number, { nullable: false })
  multilingualId: number;

  @Field(() => String, { nullable: false })
  scuelId: string;

  @Field(() => Date, { nullable: false })
  dataUpdateDate: Date;

  @Field(() => String, { nullable: false })
  typeName: string; // 多言語対応_種類名

  @Field(() => String, { nullable: true })
  detail: string; // 多言語対応_詳細
}
