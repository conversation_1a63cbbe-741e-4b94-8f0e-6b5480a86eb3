import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PostCodeMst extends BaseModel {
  static tableName = 'commonPostCodeMst';

  @Field(() => Number, { nullable: false })
  id: number;

  @Field(() => String, { nullable: false })
  postCd: string;

  @Field(() => String, { nullable: false })
  prefKana: string;

  @Field(() => String, { nullable: false })
  cityKana: string;

  @Field(() => String, { nullable: false })
  postalTermKana: string;

  @Field(() => String, { nullable: false })
  prefName: string;

  @Field(() => String, { nullable: false })
  cityName: string;

  @Field(() => String, { nullable: true })
  banti: string;
}
