import { Field, ObjectType } from '@nestjs/graphql';
import {
  AnyQueryBuilder,
  Model,
  Modifiers,
  RelationMappings,
  RelationMappingsThunk,
} from 'objection';
import { join } from 'path';

import {
  IS_DELETED,
  PHARMACY_RESERVE_DETAIL_STATUS,
} from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { Patient } from './Patient';
import { PaymentPharmacyDetail } from './PaymentPharmacyDetail';
import { PharmacyPatientFile } from './PharmacyPatientFile';
import { PharmacyReserve } from './PharmacyReserve';
import { SurveyAnswer } from './SurveyAnswer';

@ObjectType()
export class PharmacyReserveDetail extends BaseModel {
  static tableName = 'pharmacyReserveDetail';

  static idColumn = 'pharmacyReserveDetailId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    pharmacyReserve: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/PharmacyReserve'),
      join: {
        from: 'pharmacyReserveDetail.pharmacyReserveId',
        to: 'pharmacyReserve.pharmacyReserveId',
      },
    },
    patient: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/Patient'),
      join: {
        from: 'pharmacyReserveDetail.patientId',
        to: 'ptInf.ptId',
      },
      modify: (builder) => {
        builder.where('ptInf.isDelete', IS_DELETED.FALSE);
      },
    },
    pharmacyPatientFiles: {
      relation: Model.HasManyRelation,
      modelClass: join(__dirname, '/PharmacyPatientFile'),
      join: {
        from: 'pharmacyReserveDetail.pharmacyReserveDetailId',
        to: 'pharmacyPatientFile.pharmacyReserveDetailId',
      },
      modify: (builder) => {
        builder.where('pharmacyPatientFile.isDeleted', IS_DELETED.FALSE);
      },
    },
    paymentPharmacyDetail: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/PaymentPharmacyDetail'),
      filter: (builder) => builder.where('is_deleted', IS_DELETED.FALSE),
      join: {
        from: 'pharmacyReserveDetail.pharmacyReserveDetailId',
        to: 'paymentPharmacyDetail.pharmacyReserveDetailId',
      },
    },
    surveyAnswer: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, `/SurveyAnswer`),
      join: {
        from: 'pharmacyReserveDetail.pharmacyReserveDetailId',
        to: 'surveyAnswer.pharmacyReserveDetailId',
      },
      modify: (builder) => {
        builder.where('surveyAnswer.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  static modifiers: Modifiers<AnyQueryBuilder> = {
    isActive(builder) {
      builder
        .where('isDeleted', IS_DELETED.FALSE)
        .whereNot('status', PHARMACY_RESERVE_DETAIL_STATUS.CANCELLED);
    },
  };

  @Field(() => Number, { nullable: false })
  pharmacyReserveDetailId: number;

  @Field(() => Number, { nullable: false })
  pharmacyReserveId: number;

  @Field(() => Number, { nullable: false })
  patientId: number;

  @Field(() => Number, { nullable: false })
  prescriptionType: number;

  @Field(() => Number, { nullable: false })
  status: number;

  @Field(() => Number, { nullable: false })
  guidanceStatus: number;

  @Field(() => Number, { nullable: false })
  paymentStatus: number;

  @Field(() => Boolean, { nullable: false })
  hasPrescriptionRecord: boolean;

  @Field(() => Boolean, { nullable: false })
  isTakingMedication: boolean;

  @Field(() => String, { nullable: true })
  takingMedicationNames: string;

  @Field(() => Patient, { nullable: false })
  patient: Patient;

  @Field(() => [PharmacyPatientFile], { nullable: false })
  pharmacyPatientFiles: PharmacyPatientFile[];

  @Field(() => Number, { nullable: true })
  reserveDetailId: number;

  @Field(() => Date, { nullable: true })
  medicationFormAnswerDate: Date;

  @Field(() => PaymentPharmacyDetail, { nullable: true })
  paymentPharmacyDetail: PaymentPharmacyDetail;

  @Field(() => PharmacyReserve, { nullable: true })
  pharmacyReserve: PharmacyReserve;

  @Field(() => Number, { nullable: true })
  genericDrugDesire: number;

  @Field(() => SurveyAnswer, { nullable: true })
  surveyAnswer: SurveyAnswer;

  @Field(() => Boolean, { nullable: true })
  usesElectronicPrescription: boolean;

  @Field(() => Boolean, { nullable: true })
  hasElectronicPrescription: boolean;

  @Field(() => String, { nullable: true })
  redemptionNumber: string;

  @Field(() => Number, { nullable: true })
  prescriptionDeliveryMethod: number;

  public get isCanceled(): boolean {
    return this.status === PHARMACY_RESERVE_DETAIL_STATUS.CANCELLED;
  }
}
