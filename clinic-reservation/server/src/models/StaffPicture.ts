import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { Picture } from './Picture';
import { PortalStaff } from './Staff';
@ObjectType()
export class StaffPicture extends BaseModel {
  static tableName = 'portalStaffPicture';

  static idColumn = ['pictId', 'hospitalStaffId'];

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    staff: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Staff'),
      join: {
        from: 'portalStaffPicture.hospitalStaffId',
        to: 'portalHospitalStaff.hospitalStaffId',
      },
    },
    pictureDetail: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Picture'),
      join: {
        from: 'portalStaffPicture.pictId',
        to: 'portalPict.pictId',
      },
      modify: (builder) => {
        builder.where('portalPict.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  @Field(() => Number, { nullable: false })
  pictId: number;

  @Field(() => Number, { nullable: false })
  hospitalStaffId: number;

  @Field(() => Picture, { nullable: false })
  pictureDetail: Picture;

  @Field(() => PortalStaff, { nullable: false })
  staff: PortalStaff;
}
