import { Field, ObjectType } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-scalars';

import { BaseModel } from './BaseModel';

@ObjectType()
export class Survey extends BaseModel {
  static tableName = 'survey';

  static idColumn = 'surveyId';

  @Field(() => Number, { nullable: false })
  surveyId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => GraphQLJSON, { nullable: false })
  fQuesJson: string;
}
