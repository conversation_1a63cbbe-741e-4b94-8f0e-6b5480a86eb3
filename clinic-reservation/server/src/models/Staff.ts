import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { PortalHospital } from './PortalHospital';
import { StaffPicture } from './StaffPicture';

@ObjectType()
export class PortalStaff extends BaseModel {
  static tableName = 'portalHospitalStaff';

  static idColumn = 'hospitalStaffId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    hospital: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/PortalHospital'),
      join: {
        from: 'portalHospitalStaff.hospitalId',
        to: 'portalHospital.hpInfId',
      },
    },
    pictures: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/StaffPicture'),
      join: {
        from: 'portalHospitalStaff.hospitalStaffId',
        to: 'portalStaffPicture.hospitalStaffId',
      },
      modify: (builder) => {
        builder.where('portalStaffPicture.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  @Field(() => Number, { nullable: false })
  hospitalStaffId: number;

  @Field(() => Number, { nullable: false })
  hospitalId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => String, { nullable: true })
  description: string | null;

  @Field(() => Number, { nullable: false })
  order: number;

  @Field(() => String, { nullable: true })
  specialistDetail: string | null;

  @Field(() => Boolean, { nullable: false })
  isDirector: boolean;

  @Field(() => [StaffPicture], { nullable: false })
  pictures: StaffPicture[];

  @Field(() => PortalHospital, { nullable: false })
  hospital: PortalHospital;

  @Field(() => String, { nullable: true })
  experienceDetail: string | null;
}
