import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class MailDeliverySettings extends BaseModel {
  static tableName = 'mMailDeliverySettings';

  static idColumn = 'staffId';

  @Field(() => Number, { nullable: false })
  staffId: number;

  @Field(() => Boolean, { nullable: false })
  allowLogin: boolean;

  @Field(() => Boolean, { nullable: false })
  allowReservation: boolean;

  @Field(() => Boolean, { nullable: false })
  allowNewMessage: boolean;

  @Field(() => Boolean, { nullable: false })
  allowTask: boolean;

  @Field(() => Number, { nullable: false, defaultValue: 0 })
  isDeleted: number;
}
