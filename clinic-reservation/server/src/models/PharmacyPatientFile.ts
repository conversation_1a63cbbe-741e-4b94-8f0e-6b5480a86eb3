import { Field, ObjectType } from '@nestjs/graphql';
import { AnyQueryBuilder, Model, Modifiers } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PharmacyPatientFile extends BaseModel {
  static tableName = 'pharmacyPatientFile';

  static idColumn = 'pharmacyPatientFileId';

  static relationMappings = {
    hospital: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/PharmacyReserveDetail'),
      join: {
        from: 'pharmacyPatientFile.pharmacyReserveDetailId',
        to: 'pharmacyReserveDetail.pharmacyReserveDetailId',
      },
    },
  };

  static modifiers: Modifiers<AnyQueryBuilder> = {
    isActive(builder) {
      builder.where('isDeleted', IS_DELETED.FALSE);
    },
  };

  @Field(() => Number, { nullable: false })
  pharmacyPatientFileId: number;

  @Field(() => Number, { nullable: false })
  pharmacyReserveDetailId: number;

  @Field(() => Number, { nullable: false })
  patientId: number;

  @Field(() => String, { nullable: false })
  originalFileName: string;

  @Field(() => String, { nullable: false })
  s3Key: string;

  @Field(() => Number, { nullable: false })
  type: number;

  @Field(() => String, { nullable: true })
  mimeType?: string;

  @Field(() => Number, { nullable: false })
  isDeleted: number;
}
