import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { BusinessTime } from './BusinessTime';
import { Examination } from './Examination';
import { HospitalFavorite } from './HospitalFavorite';
import { HospitalInfo } from './HospitalInfo';
import { HospitalNotification } from './HospitalNotification';
import { HospitalPicture } from './HospitalPicture';
import { HospitalStation } from './HospitalStation';
import { Specialist } from './Specialist';
import { PortalStaff } from './Staff';
import { Tag } from './Tag';
import { TreatmentCategory } from './TreatmentCategory';
import { TreatmentDepartment } from './TreatmentDepartment';

@ObjectType()
export class PortalHospital extends BaseModel {
  static tableName = 'portalHospital';

  static idColumn = 'hospitalId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    denkaruHospital: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/HospitalInfo'),
      join: {
        from: 'hpInf.hpId',
        to: 'portalHospital.hpInfId',
      },
    },
    hospitalStaffs: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/Staff'),
      join: {
        from: 'portalHospital.hpInfId',
        to: 'portalHospitalStaff.hospitalId',
      },
      modify: (builder) => {
        builder.where('portalHospitalStaff.isDeleted', IS_DELETED.FALSE);
      },
    },
    notifications: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/HospitalNotification'),
      join: {
        from: 'portalHospital.hospitalId',
        to: 'portalHospitalNotification.portalHospitalId',
      },
      modify: (builder) => {
        builder.where('portalHospitalNotification.isDeleted', IS_DELETED.FALSE);
      },
    },
    businessTimes: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/BusinessTime'),
      join: {
        from: 'portalHospital.hospitalId',
        to: 'portalBusinessTime.hospitalId',
      },
      modify: (builder) => {
        builder.where('portalBusinessTime.isDeleted', IS_DELETED.FALSE);
      },
    },
    treatmentCategories: {
      relation: BaseModel.ManyToManyRelation,
      modelClass: join(__dirname, '/TreatmentCategory'),
      join: {
        from: 'portalHospital.hpInfId',
        through: {
          from: 'kaMst.hpId',
          to: 'kaMst.kaName',
        },
        to: 'mTreatmentCategory.name',
      },
      modify: (builder) => {
        builder
          .where('kaMst.isDeleted', IS_DELETED.FALSE)
          .where('mTreatmentCategory.isDeleted', IS_DELETED.FALSE);
      },
    },
    treatmentDepartments: {
      relation: BaseModel.ManyToManyRelation,
      modelClass: join(__dirname, '/TreatmentDepartment'),
      join: {
        from: 'portalHospital.hpInfId',
        through: {
          from: 'hpInf.hpId',
          to: 'hpInf.hpId',
        },
        to: 'treatmentDepartment.hospitalId',
      },
      modify: (builder) => {
        builder
          .where('hpInf.isDeleted', IS_DELETED.FALSE)
          .where('treatmentDepartment.isDeleted', IS_DELETED.FALSE);
      },
    },
    pictures: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/HospitalPicture'),
      join: {
        from: 'portalHospital.hospitalId',
        to: 'portalHospPicture.hospitalId',
      },
      modify: (builder) => {
        builder.where('portalHospPicture.isDeleted', IS_DELETED.FALSE);
      },
    },
    tags: {
      relation: BaseModel.ManyToManyRelation,
      modelClass: join(__dirname, '/Tag'),
      join: {
        from: 'portalHospital.hospitalId',
        through: {
          from: 'portalHospTag.hospitalId',
          to: 'portalHospTag.tagId',
        },
        // to: 'portalMTag.tagId',
        to: 'portal_m_tag.tagId',
      },
      modify: (builder) => {
        builder
          .where('portalHospTag.isDeleted', IS_DELETED.FALSE)
          .where('portal_m_tag.isDeleted', IS_DELETED.FALSE);
      },
    },
    examinations: {
      relation: BaseModel.ManyToManyRelation,
      modelClass: join(__dirname, '/Examination'),
      join: {
        from: 'portalHospital.hospitalId',
        through: {
          from: 'portalHospitalExamination.portalHospitalId',
          to: 'portalHospitalExamination.examinationId',
        },
        to: 'portal_m_examination.examinationId',
      },
      modify: (builder) => {
        builder
          .where('portalHospitalExamination.isDeleted', IS_DELETED.FALSE)
          .where('portal_m_examination.isDeleted', IS_DELETED.FALSE);
      },
    },
    specialists: {
      relation: BaseModel.ManyToManyRelation,
      modelClass: join(__dirname, '/Specialist'),
      join: {
        from: 'portalHospital.hospitalId',
        through: {
          from: 'portalHospitalSpecialist.hospitalId',
          to: 'portalHospitalSpecialist.specialistId',
        },
        to: 'portal_m_specialist.specialistId',
      },
      modify: (builder) => {
        builder
          .where('portalHospitalSpecialist.isDeleted', IS_DELETED.FALSE)
          .where('portal_m_specialist.isDeleted', IS_DELETED.FALSE);
      },
    },
    stations: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/HospitalStation'),
      join: {
        from: 'portalHospital.hospitalId',
        to: 'portalHospStation.hospitalId',
      },
      modify: (builder) => {
        builder.where('portalHospStation.isDeleted', IS_DELETED.FALSE);
      },
    },
    hospitalFavorites: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/HospitalFavorite'),
      join: {
        from: 'portalHospital.hospitalId',
        to: 'portal_hospital_favorite.portalHospitalId',
      },
      modify: (builder) => {
        builder.where('portal_hospital_favorite.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  @Field(() => Number, { nullable: false })
  hospitalId: number;

  @Field(() => Number, { nullable: false })
  hpInfId: number;

  @Field(() => HospitalInfo, { nullable: true })
  denkaruHospital: HospitalInfo;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => String, { nullable: true })
  description: string | null;

  @Field(() => String, { nullable: true })
  telephone: string;

  @Field(() => String, { nullable: true })
  carparkDetail: string | null;

  @Field(() => String, { nullable: false })
  postCode: string;

  @Field(() => String, { nullable: false })
  address1: string;

  @Field(() => String, { nullable: true })
  address2: string | null;

  @Field(() => Number, { nullable: true })
  latitude: number | null;

  @Field(() => Number, { nullable: true })
  longitude: number | null;

  @Field(() => String, { nullable: true })
  fax: string | null;

  @Field(() => Boolean, { nullable: false })
  isCarpark: boolean;

  @Field(() => String, { nullable: false })
  descriptionTitle: string;

  @Field(() => String, { nullable: false })
  paymentDetails: string;

  @Field(() => String, { nullable: true })
  businessTimeDetail: string | null;

  @Field(() => String, { nullable: true })
  timelineDescription: string | null;

  @Field(() => String, { nullable: true })
  holidayDetail: string | null;

  @Field(() => String, { nullable: true })
  accessDetail: string | null;

  @Field(() => String, { nullable: true })
  homePage: string | null;

  @Field(() => String, { nullable: true })
  directorName: string | null;

  @Field(() => String, { nullable: true })
  mailAddress: string | null;

  @Field(() => Boolean, { nullable: false })
  isActive: boolean;

  @Field(() => [TreatmentDepartment], { nullable: true })
  treatmentDepartments: TreatmentDepartment[];

  @Field(() => [TreatmentCategory], { nullable: true })
  treatmentCategories: TreatmentCategory[];

  @Field(() => [BusinessTime], { nullable: false })
  businessTimes: BusinessTime[];

  @Field(() => [HospitalPicture], { nullable: false })
  pictures: HospitalPicture[];

  @Field(() => [Tag], { nullable: false })
  tags: Tag[];

  @Field(() => [Examination], { nullable: false })
  examinations: Examination[];

  @Field(() => [Specialist], { nullable: false })
  specialists: Specialist[];

  @Field(() => [HospitalStation], { nullable: false })
  stations: HospitalStation[];

  @Field(() => [PortalStaff], { nullable: false })
  hospitalStaffs: PortalStaff[];

  @Field(() => [HospitalNotification], { nullable: false })
  notifications: HospitalNotification[];

  @Field(() => [HospitalFavorite], { nullable: false })
  hospitalFavorites: HospitalFavorite[];

  @Field(() => Boolean, { defaultValue: false })
  isFavorite: boolean = false;
}
