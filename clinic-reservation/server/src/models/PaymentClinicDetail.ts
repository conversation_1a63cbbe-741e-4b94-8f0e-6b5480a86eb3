import { Field, ObjectType } from '@nestjs/graphql';
import { Model } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PaymentClinicDetail extends BaseModel {
  static tableName = 'paymentClinicDetail';

  static idColumn = 'paymentClinicDetailId';

  static relationMappings = {
    reserveDetail: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/ReservationDetail'),
      join: {
        from: 'paymentClinicDetail.reserveDetailId',
        to: 'reserveDetail.reserveDetailId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  paymentClinicDetail: number;

  @Field(() => Number, { nullable: false })
  actionType: number;

  @Field(() => Number, { nullable: false })
  paymentType: number;

  @Field(() => Date, { nullable: false })
  paymentDate: Date;

  @Field(() => Number, { nullable: false })
  paymentStatus: number;

  @Field(() => Number, { nullable: false })
  totalAmount: number;

  @Field(() => String, { nullable: false })
  cardNo: string;

  @Field(() => String, { nullable: false })
  expire: string;

  @Field(() => String, { nullable: false })
  brand: string;

  @Field(() => Number, { nullable: false })
  isRetryable: number;
}
