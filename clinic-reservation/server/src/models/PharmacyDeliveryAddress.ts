import { Field, ObjectType } from '@nestjs/graphql';
import { Model } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PharmacyDeliveryAddress extends BaseModel {
  static tableName = 'pharmacyDeliveryAddress';

  static idColumn = 'pharmacyDeliveryAddressId';

  static relationMappings = {
    pharmacyReserve: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/PharmacyReserve'),
      join: {
        from: 'pharmacyDeliveryAddress.pharmacyReserveId',
        to: 'pharmacyReserve.pharmacyReserveId',
      },
    },
    deliveryAddress: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/CustomerDeliveryAddress'),
      join: {
        from: 'pharmacyDeliveryAddress.deliveryAddressId',
        to: 'portalCustomerDeliveryAddress.deliveryAddressId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  pharmacyDeliveryAddressId: number;

  @Field(() => Number, { nullable: false })
  pharmacyReserveId: number;

  @Field(() => Number, { nullable: false })
  deliveryAddressId: number;

  @Field(() => String, { nullable: false })
  address1: string;

  @Field(() => String, { nullable: true })
  address2?: string | null;

  @Field(() => String, { nullable: false })
  postCode: string;

  @Field(() => String, { nullable: false })
  phoneNumber: string;
}
