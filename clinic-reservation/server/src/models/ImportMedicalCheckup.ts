import { Field, ObjectType } from '@nestjs/graphql';
import { AnyQueryBuilder, Modifiers } from 'objection';

import { BaseModel } from './BaseModel';

@ObjectType()
export class ImportMedicalCheckup extends BaseModel {
  static tableName = 'portal_m_import_medical_checkup';

  static idColumn = 'medicalCheckupId';

  static modifiers: Modifiers<AnyQueryBuilder> = {
    filterByExaminations(builder, range: { examinations: string[] }) {
      builder.whereIn('checkupDetails', range.examinations);
    },
  };

  @Field(() => Number, { nullable: false })
  medicalCheckupId: number;

  @Field(() => String, { nullable: false })
  scuelId: string;

  @Field(() => Date, { nullable: false })
  dataUpdateDate: Date;

  @Field(() => String, { nullable: false })
  checkupTypeName: string; // 健診・検診・人間ドック_種類名

  @Field(() => String, { nullable: true })
  checkupDetails: string; // 健診・検診・人間ドック_詳細
}
