import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class ImportMedicalSpecialist extends BaseModel {
  static tableName = 'portal_m_import_medical_specialist';

  static idColumn = 'medicalSpecialistId';

  @Field(() => Number, { nullable: false })
  medicalSpecialistId: number;

  @Field(() => String, { nullable: false })
  scuelId: string;

  @Field(() => Date, { nullable: false })
  dataUpdateDate: Date;

  @Field(() => String, { nullable: false })
  scuelAcadCode: string; // SCUEL学会コード

  @Field(() => String, { nullable: false })
  scuelAcadName: string; // SCUEL学会名

  @Field(() => String, { nullable: false })
  scuelCertCode: string; // SCUEL資格コード

  @Field(() => String, { nullable: false })
  scuelCertName: string; // SCUEL資格名

  @Field(() => String, { nullable: false })
  acadName: string; // 資格_学会名

  @Field(() => String, { nullable: false })
  specName: string; // 資格_専門医名

  @Field(() => String, { nullable: false })
  subjectTypeName: string; // 資格_科目種類名

  @Field(() => String, { nullable: false })
  specTypeName: string; // 資格_専門種類名

  @Field(() => Number, { nullable: false })
  certNumber: number; // 資格_人数
}
