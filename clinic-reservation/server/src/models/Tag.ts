import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class Tag extends BaseModel {
  static tableName = 'portal_m_tag';

  static idColumn = 'tagId';

  @Field(() => Number, { nullable: false })
  tagId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => Number, { nullable: false })
  sortOrder: number;
}
