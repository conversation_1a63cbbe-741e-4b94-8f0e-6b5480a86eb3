import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';
import { RaillineCorp } from './RaillineCorp';
import { Station } from './Station';

@ObjectType()
export class Railline extends BaseModel {
  static tableName = 'portal_m_railline';

  static idColumn = 'raillineId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    stations: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/Station'),
      join: {
        from: 'portal_m_railline.raillineId',
        to: 'portal_m_station.raillineId',
      },
    },
    raillineCorp: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/RaillineCorp'),
      join: {
        from: 'portal_m_railline.raillineCompanyId',
        to: 'portal_m_railline_company.raillineCompanyId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  raillineId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => Number, { nullable: false })
  raillineCompanyId: number;

  @Field(() => RaillineCorp, { nullable: false })
  raillineCorp: RaillineCorp;

  @Field(() => [Station], { nullable: true })
  stations: Station[] | null;
}
