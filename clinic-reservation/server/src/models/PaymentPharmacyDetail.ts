import { Field, ObjectType } from '@nestjs/graphql';
import { Model } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';

@ObjectType()
export class PaymentPharmacyDetail extends BaseModel {
  static tableName = 'paymentPharmacyDetail';

  static idColumn = 'paymentPharmacyDetailId';

  static relationMappings = {
    reserveDetail: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/PharmacyReserveDetail'),
      join: {
        from: 'paymentPharmacyDetail.pharmacyReserveDetailId',
        to: 'pharmacyReserveDetail.pharmacyReserveDetailId',
      },
    },
    patient: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/'),
      join: {
        from: 'paymentPharmacyDetail.pharmacyReserveDetailId',
        to: 'pharmacyReserveDetail.pharmacyReserveDetailId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  paymentPharmacyDetailId: number;

  @Field(() => Number, { nullable: false })
  actionType: number;

  @Field(() => Number, { nullable: false })
  paymentType: number;

  @Field(() => Date, { nullable: false })
  paymentDate: Date;

  @Field(() => Number, { nullable: false })
  paymentStatus: number;

  @Field(() => Number, { nullable: false })
  medicationCost: number;

  @Field(() => Number, { nullable: false })
  deliveryFee: number;

  @Field(() => String, { nullable: false })
  cardNo: string;

  @Field(() => String, { nullable: false })
  expire: string;

  @Field(() => String, { nullable: false })
  brand: string;

  @Field(() => Number, { nullable: false })
  isRetryable: number;
}
