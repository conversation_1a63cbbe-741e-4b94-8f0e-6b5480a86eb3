import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { RAILLINE_CORP_KIND_TYPE } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { Railline } from './Railline';

@ObjectType()
export class RaillineCorp extends BaseModel {
  static tableName = 'portal_m_railline_company';

  static idColumn = 'raillineCompanyId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    raillines: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/Railline'),
      join: {
        from: 'portal_m_railline_company.raillineCompanyId',
        to: 'portal_m_railline.raillineCompanyId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  raillineCompanyId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => Number, { nullable: false })
  type: RAILLINE_CORP_KIND_TYPE;

  @Field(() => [Railline], { nullable: true })
  raillines: Railline[] | null;
}
