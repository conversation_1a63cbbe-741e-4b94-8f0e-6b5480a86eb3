import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { HospitalStation } from './HospitalStation';
import { Railline } from './Railline';

@ObjectType()
export class Station extends BaseModel {
  static tableName = 'portal_m_station';

  static idColumn = 'stationId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    railline: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Railline'),
      join: {
        from: 'portal_m_station.raillineId',
        to: 'portal_m_railline.raillineId',
      },
      modify: (builder) => {
        builder.where('portal_m_railline.isDeleted', IS_DELETED.FALSE);
      },
    },
    hospitalStations: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/HospitalStation'),
      join: {
        from: 'portal_m_station.stationId',
        to: 'portalHospStation.stationId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  stationId: number;

  @Field(() => Number, { nullable: false })
  cityId: number;

  @Field(() => Number, { nullable: true })
  raillineId: number;

  @Field(() => String, { nullable: false })
  name: string;
  @Field(() => Railline, { nullable: true })
  railline: Railline;

  @Field(() => HospitalStation, { nullable: true })
  hospitalStations: HospitalStation[] | null;

  @Field(() => Number, { nullable: true })
  hospitalStationsCount: number | null;

  @Field(() => Number, { nullable: true })
  hospitalsCount: number | null;

  @Field(() => String, { nullable: true })
  description: string;
}
