import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappingsThunk } from 'objection';
import { Model } from 'objection';
import { RelationMappings } from 'objection';
import { join } from 'path';

import {
  IS_DELETED,
  TREATMENT_DEPARTMENT_STATUS,
  TREATMENT_PORTAL_PUBLIC_STATUS,
} from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { TreatmentDepartment } from './TreatmentDepartment';

@ObjectType()
export class PortalTreatmentDeptGrp extends BaseModel {
  static tableName = 'portal_treatment_dept_grp';

  static idColumn: string | string[] = 'treatmentDeptGrpId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    treatmentDepartment: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/TreatmentDepartment'),
      join: {
        from: 'portal_treatment_dept_grp.treatmentDepartmentId',
        to: 'treatmentDepartment.treatmentDepartmentId',
      },
      modify: (builder) => {
        builder.where({
          'treatmentDepartment.isDeleted': IS_DELETED.FALSE,
          'treatmentDepartment.treatmentDepartmentStatus':
            TREATMENT_DEPARTMENT_STATUS.VALID,
          'treatmentDepartment.portalPublicStatus':
            TREATMENT_PORTAL_PUBLIC_STATUS.PUBLIC,
        });
      },
    },
  };

  @Field(() => Number, { nullable: false })
  treatmentDeptGrpId: number;

  @Field(() => Number, { nullable: false })
  groupId: number;

  @Field(() => Number, { nullable: false })
  treatmentDepartmentId: number;

  @Field(() => TreatmentDepartment, { nullable: false })
  treatmentDepartment: TreatmentDepartment;
}
