import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';
import { PortalHospital } from './PortalHospital';

@ObjectType()
export class TreatmentCategory extends BaseModel {
  static tableName = 'mTreatmentCategory';

  static idColumn = 'treatmentCategoryId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    portalHospitals: {
      relation: BaseModel.ManyToManyRelation,
      modelClass: join(__dirname, '/PortalHospital'),
      join: {
        from: 'mTreatmentCategory.treatmentCategoryId',
        through: {
          from: 'treatmentDepartment.treatmentCategoryId',
          to: 'treatmentDepartment.hospitalId',
        },
        to: 'portalHospital.hpInfId',
      },
    },

    hospitals: {
      relation: BaseModel.ManyToManyRelation,
      modelClass: join(__dirname, '/PortalHospital'),
      join: {
        from: 'mTreatmentCategory.treatmentCategoryId',
        through: {
          from: 'treatmentDepartment.treatmentCategoryId',
          to: 'treatmentDepartment.hospitalId',
        },
        to: 'portalHospital.hpInfId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  treatmentCategoryId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => Number, { nullable: false })
  groupType: number;

  @Field(() => [PortalHospital], { nullable: true })
  hospitals: PortalHospital[];

  @Field(() => [PortalHospital], { nullable: true })
  portalHospitals: PortalHospital[];
}
