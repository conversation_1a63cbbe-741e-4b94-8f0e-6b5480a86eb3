import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class Prefecture extends BaseModel {
  static tableName = 'portal_m_prefecture';

  static idColumn: string | string[] = 'prefectureId';

  @Field(() => Number, { nullable: false })
  prefectureId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => String, { nullable: false })
  prefectureCode: string;

  @Field(() => String, { nullable: false })
  groupCode: string;

  @Field(() => String, { nullable: false })
  region: string;
}
