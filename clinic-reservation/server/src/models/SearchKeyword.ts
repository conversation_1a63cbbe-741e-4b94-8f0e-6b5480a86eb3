import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class SearchKeyword extends BaseModel {
  static tableName = 'portal_m_search_keyword_equiv';

  static idColumn = 'id';

  @Field(() => Number, { nullable: false })
  id: number;

  @Field(() => String, { nullable: false })
  keyword: string;

  @Field(() => String, { nullable: false })
  keywordEquiv: string;
}
