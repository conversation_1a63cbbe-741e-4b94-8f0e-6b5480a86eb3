import { Field, ObjectType } from '@nestjs/graphql';
import { Model, RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { Customer } from './Customer';
import { HospitalInfo } from './HospitalInfo';

@ObjectType()
export class Patient extends Model {
  static tableName = 'ptInf';

  static idColumn = ['hpId', 'ptId'];

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    portalCustomer: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Customer'),
      join: {
        from: 'ptInf.portalCustomerId',
        to: 'portalCustomer.customerId',
      },
      modify: (builder) => {
        builder.where('portalCustomer.isDeleted', IS_DELETED.FALSE);
      },
    },
    hospital: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/HospitalInfo'),
      join: {
        from: 'ptInf.hpId',
        to: 'hpInf.hpId',
      },
      modify: (builder) => {
        builder.where('hpInf.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  @Field(() => Number, { nullable: false })
  hpId: number;

  @Field(() => HospitalInfo, { nullable: false })
  hospital: HospitalInfo;

  @Field(() => Number, { nullable: false })
  ptId: number;

  @Field(() => Number, { nullable: false })
  seqNo: number;

  @Field(() => Number, { nullable: false })
  ptNum: number;

  @Field(() => String, { nullable: true })
  kanaName: string | null;

  @Field(() => String, { nullable: true })
  name: string | null;

  @Field(() => Number, { nullable: false })
  sex: number;

  @Field(() => Number, { nullable: false })
  birthday: number;

  @Field(() => Number, { nullable: false })
  isDead: number;

  @Field(() => Number, { nullable: false })
  deathDate: number;

  @Field(() => String, { nullable: true })
  homePost: string | null;

  @Field(() => String, { nullable: true })
  homeAddress1: string | null;

  @Field(() => String, { nullable: true })
  homeAddress2: string | null;

  @Field(() => String, { nullable: true })
  tel1: string | null;

  @Field(() => String, { nullable: true })
  tel2: string | null;

  @Field(() => String, { nullable: true })
  mail: string | null;

  @Field(() => String, { nullable: true })
  setainusi: string | null;

  @Field(() => String, { nullable: true })
  zokugara: string | null;

  @Field(() => String, { nullable: true })
  job: string | null;

  @Field(() => String, { nullable: true })
  renrakuName: string | null;

  @Field(() => String, { nullable: true })
  renrakuPost: string | null;

  @Field(() => String, { nullable: true })
  renrakuAddress1: string | null;

  @Field(() => String, { nullable: true })
  renrakuAddress2: string | null;

  @Field(() => String, { nullable: true })
  renrakuTel: string | null;

  @Field(() => String, { nullable: true })
  renrakuMemo: string | null;

  @Field(() => String, { nullable: true })
  officeName: string | null;

  @Field(() => String, { nullable: true })
  officePost: string | null;

  @Field(() => String, { nullable: true })
  officeAddress1: string | null;

  @Field(() => String, { nullable: true })
  officeAddress2: string | null;

  @Field(() => String, { nullable: true })
  officeTel: string | null;

  @Field(() => String, { nullable: true })
  officeMemo: string | null;

  @Field(() => Number, { nullable: false })
  isRyosyoDetail: number;

  @Field(() => Number, { nullable: false })
  primaryDoctor: number;

  @Field(() => Number, { nullable: false })
  isTester: number;

  @Field(() => Number, { nullable: false })
  isDelete: number;

  @Field(() => Date)
  createDate: Date;

  @Field(() => Number, { nullable: false })
  createId: number;

  @Field(() => String, { nullable: true })
  createMachine: string | null;

  @Field(() => Date)
  updateDate: Date;

  @Field(() => Number, { nullable: false })
  updateId: number;

  @Field(() => String, { nullable: true })
  updateMachine: string | null;

  @Field(() => Number, { nullable: false })
  mainHokenPid: number;

  @Field(() => Number, { nullable: false })
  referenceNo: number;

  @Field(() => Number, { nullable: false })
  limitConsFlg: number;

  @Field(() => Number, { nullable: true })
  portalCustomerId: number | null;

  @Field(() => Customer, { nullable: true })
  portalCustomer: Customer;
}
