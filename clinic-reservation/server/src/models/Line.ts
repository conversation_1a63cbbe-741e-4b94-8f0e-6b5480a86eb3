import { Field } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

export class LineUser extends BaseModel {
  static tableName = 'lineUser';

  static idColumn = 'lineUserNo';

  @Field(() => Number, { nullable: false })
  lineUserNo: number;

  @Field(() => String, { nullable: false })
  lineUserId: string;

  @Field(() => Boolean, { nullable: false, defaultValue: false })
  isFriend: boolean;

  @Field(() => String, { nullable: false })
  lineLinkToken: string;
}
