import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { ImportOperatingHour } from './ImportOperatingHour';

@ObjectType()
export class ImportService extends BaseModel {
  static tableName = 'portal_m_import_service';

  static idColumn = 'importServiceId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    operatingHours: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/ImportOperatingHour'),
      join: {
        from: 'portal_m_import_service.typeCode',
        to: 'portal_m_import_operating_hour.typeCode',
      },
      modify: (builder) => {
        builder.where(
          'portal_m_import_operating_hour.isDeleted',
          IS_DELETED.FALSE,
        );
      },
    },
  };

  @Field(() => Number, { nullable: false })
  importServiceId: number;

  @Field(() => String, { nullable: false })
  scuelId: string;

  @Field(() => Date, { nullable: false })
  dataUpdateDate: Date;

  @Field(() => String, { nullable: true })
  scuelServiceCode: string;

  @Field(() => String, { nullable: true })
  scuelCorporationCode: string;

  @Field(() => String, { nullable: true })
  scuelOfficeCode: string;

  @Field(() => String, { nullable: false })
  category: string;

  @Field(() => String, { nullable: false })
  typeName: string;

  @Field(() => String, { nullable: false })
  typeCode: string;

  @Field(() => String, { nullable: true })
  typeDetail: string;

  @Field(() => String, { nullable: false })
  scaleFlag: string;

  @Field(() => String, { nullable: false })
  officeName: string;

  @Field(() => String, { nullable: false })
  officeNameKana: string;

  @Field(() => String, { nullable: false })
  officeNumber: string;

  @Field(() => String, { nullable: false })
  corporationType: string;

  @Field(() => String, { nullable: true })
  corporationName: string;

  @Field(() => String, { nullable: true })
  scuelFounderCode: string;

  @Field(() => String, { nullable: true })
  founderName: string;

  @Field(() => String, { nullable: true })
  founderPosition: string;

  @Field(() => String, { nullable: true })
  scuelAdministratorCode: string;

  @Field(() => String, { nullable: true })
  administratorName: string;

  @Field(() => String, { nullable: true })
  telephoneNumber: string;

  @Field(() => String, { nullable: true })
  faxNumber: string;

  @Field(() => String, { nullable: true })
  homepage: string;

  @Field(() => String, { nullable: true })
  holidays: string;

  @Field(() => String, { nullable: true })
  specialNotes: string;

  @Field(() => Date, { nullable: true })
  startDate: Date;

  @Field(() => [ImportOperatingHour], { nullable: false })
  operatingHours: ImportOperatingHour[];
}
