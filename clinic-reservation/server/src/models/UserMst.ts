import { Field, ObjectType } from '@nestjs/graphql';
import { Model, RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { MailDeliverySettings } from './MailDeliverySettings';

@ObjectType()
export class UserMst extends Model {
  static tableName = 'userMst';
  static idColumn = 'id';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    mailSetting: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/MailDeliverySettings'),
      join: {
        from: 'userMst.id',
        to: 'mMailDeliverySettings.staffId',
      },
      modify: (builder) => {
        builder.where('mMailDeliverySettings.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  @Field(() => Number, { nullable: false })
  hpId: number;

  @Field(() => Number, { nullable: false })
  id: number;

  @Field(() => Number, { nullable: false })
  userId: number;

  @Field(() => Number, { nullable: false })
  jobCd: number;

  @Field(() => Number, { nullable: false })
  managerKbn: number;

  @Field(() => Number, { nullable: false })
  kaId: number;

  @Field(() => String, { nullable: true })
  kanaName?: string;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => String, { nullable: false })
  sname: string;

  @Field(() => String, { nullable: false })
  loginId: string;

  @Field(() => String, { nullable: true })
  mayakuLicenseNo?: string;

  @Field(() => Number, { nullable: false })
  startDate: number;

  @Field(() => Number, { nullable: false })
  endDate: number;

  @Field(() => Number, { nullable: false })
  sortNo: number;

  @Field(() => Number, { nullable: false })
  isDeleted: number;

  @Field(() => String, { nullable: false })
  createDate: string;

  @Field(() => Number, { nullable: false })
  createId: number;

  @Field(() => String, { nullable: true })
  email?: string;

  @Field(() => MailDeliverySettings, { nullable: true })
  mailSetting?: MailDeliverySettings;

  createMachine?: string;

  updateDate: string;

  updateId: number;

  updateMachine?: string;

  renkeiCd1?: string;

  drName?: string;

  loginType?: number;

  hpkiSn?: string;

  hpkiIssuerDn?: string;

  hashPassword: string;

  salt: string;

  isInitLoginId: number;

  isInitPassword: number;

  missLoginCount: number;

  status: number;
}
