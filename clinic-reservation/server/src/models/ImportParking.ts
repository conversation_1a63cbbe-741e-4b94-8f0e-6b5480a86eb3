import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class ImportParking extends BaseModel {
  static tableName = 'portal_m_import_parking';

  static idColumn = 'parkingId';

  @Field(() => Number, { nullable: false })
  parkingId: number;

  @Field(() => String, { nullable: false })
  scuelId: string;

  @Field(() => Date, { nullable: false })
  dataUpdateDate: Date;

  @Field(() => Boolean, { nullable: true })
  isCarpark: boolean; // 駐車場_有無

  @Field(() => Number, { nullable: true })
  paidParkingCount: number; // 駐車場_有料台数

  @Field(() => Number, { nullable: true })
  freeParkingCount: number; // 駐車場_無料台数

  @Field(() => Number, { nullable: true })
  otherParkingCount: number; // 駐車場_その他
}
