import { Field, ObjectType } from '@nestjs/graphql';
import { RelationMappings, RelationMappingsThunk } from 'objection';
import { join } from 'path';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { Meeting } from './Meeting';
import { Patient } from './Patient';
import { PharmacyReserve } from './PharmacyReserve';
import { PortalCustomerPharmacy } from './PortalCustomerPharmacy';
import { ReservationDetail } from './ReservationDetail';

@ObjectType()
export class Reservation extends BaseModel {
  static tableName = 'reserve';

  static idColumn = 'reserveId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    reservationDetails: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/ReservationDetail'),
      join: {
        from: 'reserve.reserveId',
        to: 'reserveDetail.reserveId',
      },
    },
    patient: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Patient'),
      join: {
        from: 'reserve.patientId',
        to: 'ptInf.ptId',
      },
      modify: (builder) => {
        builder.where('ptInf.isDelete', IS_DELETED.FALSE);
      },
    },
    pharmacyReserve: {
      relation: BaseModel.HasOneRelation,
      filter: (builder) =>
        builder
          .where('is_deleted', IS_DELETED.FALSE)
          .orderBy('created_at', 'desc'),
      modelClass: join(__dirname, '/PharmacyReserve'),
      join: {
        from: 'reserve.reserveId',
        to: 'pharmacyReserve.reserveId',
      },
    },
    portalCustomerPharmacy: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/PortalCustomerPharmacy'),
      join: {
        from: 'reserve.reserveId',
        to: 'portalCustomerPharmacy.reserveId',
      },
      modify: (builder) => {
        builder.where('portalCustomerPharmacy.isDeleted', IS_DELETED.FALSE);
      },
    },
    meeting: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, `/Meeting`),
      join: {
        from: 'reserve.reserveId',
        to: 'meeting.reserveId',
      },
      modify: (builder) => {
        builder.where('meeting.isDeleted', IS_DELETED.FALSE);
      },
    },
  };

  @Field(() => Number, { nullable: false })
  reserveId: number;

  @Field(() => Number, { nullable: false })
  prescriptionReceiveMethod: number;

  @Field(() => Patient, { nullable: true })
  patient: Patient | null;

  @Field(() => Number, { nullable: true })
  patientId: number | null;

  @Field(() => [ReservationDetail], { nullable: false })
  reservationDetails: ReservationDetail[];

  @Field(() => PharmacyReserve, { nullable: true })
  pharmacyReserve: PharmacyReserve;

  @Field(() => PortalCustomerPharmacy, { nullable: true })
  portalCustomerPharmacy: PortalCustomerPharmacy;

  @Field(() => Meeting, { nullable: true })
  meeting?: Meeting;
}
