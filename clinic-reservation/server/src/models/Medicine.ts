import { Field, ObjectType } from '@nestjs/graphql';

export enum DeliveryStatus {
  inPrepare = 0,
  shipping = 1,
  deliverd = 2,
}

@ObjectType()
class Medicine {
  @Field(() => String, { nullable: false })
  name: string;
  @Field(() => String, { nullable: false })
  frequency: string;
}

@ObjectType()
class PrescriptMedicine {
  @Field(() => [Medicine], { nullable: true })
  medicines: Medicine[] | null;
  @Field(() => String, { nullable: false })
  name: string;
  @Field(() => String, { nullable: true })
  time: string | null;
}

@ObjectType()
export class MedicineDelivery {
  @Field(() => [PrescriptMedicine], { nullable: false })
  prescriptMedicines: PrescriptMedicine[];

  @Field(() => Number, { nullable: false })
  deliveryStatus: DeliveryStatus;

  @Field(() => String, { nullable: false })
  deliveryMethod: string;

  @Field(() => String, { nullable: false })
  deliveryPhone: string;

  @Field(() => String, { nullable: false })
  deliveryDestination: string;
}
