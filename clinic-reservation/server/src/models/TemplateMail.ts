import { Field, ObjectType } from '@nestjs/graphql';

import { IS_DELETED } from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';

@ObjectType()
export class TemplateMail extends BaseModel {
  static tableName = 'mTemplateMail';

  static idColumn = 'templateMailCode';

  @Field(() => String, { nullable: false })
  templateMailCode: string;

  @Field(() => Number, { nullable: false })
  mailContentType: number;

  @Field(() => Number, { nullable: false })
  allowType: number;

  @Field(() => String, { nullable: false })
  mailSubject: string;

  @Field(() => String, { nullable: false })
  mailBody: string;

  @Field(() => Boolean)
  pharmacyFlg: boolean | false;

  @Field(() => Number, { nullable: false, defaultValue: IS_DELETED.FALSE })
  isDeleted: number;

  @Field(() => String, { nullable: false })
  fromMailAddress: string;
}
