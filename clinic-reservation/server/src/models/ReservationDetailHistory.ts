import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class ReservationDetailHistory extends BaseModel {
  static tableName = 'reserveDetailHistory';
  static idColumn = 'reserveDetailHistoryId';

  @Field(() => Number, { nullable: false })
  reserveDetailHistoryId: number;

  @Field(() => Number, { nullable: true })
  examDetailId: number | null;

  @Field(() => Number, { nullable: false })
  status: number;

  @Field(() => Number, { nullable: true })
  reserveCancelType: number | null;

  @Field(() => Number, { nullable: true })
  reserveType: number | null;

  @Field(() => Number, { nullable: true })
  treatmentType: number | null;

  @Field(() => Number, { nullable: false })
  calendarTreatmentId: number;

  @Field(() => String, { nullable: true })
  memo: string | null;

  @Field(() => Number, { nullable: false })
  reserveDetailId: number;

  @Field(() => Number, { nullable: false })
  examTimeSlotId: number;
}
