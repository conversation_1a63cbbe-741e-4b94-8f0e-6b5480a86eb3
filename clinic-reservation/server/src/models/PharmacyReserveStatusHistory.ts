import { Field, ObjectType } from '@nestjs/graphql';
import { Model } from 'objection';
import { join } from 'path';

import { BaseModel } from './BaseModel';
import { PharmacyReserveDetail } from './PharmacyReserveDetail';

@ObjectType()
export class PharmacyReserveStatusHistory extends BaseModel {
  static tableName = 'pharmacyReserveStatusHistory';

  static idColumn = 'pharmacyReserveStatusHistoryId';

  static relationMappings = {
    pharmacyReserveDetail: {
      relation: Model.BelongsToOneRelation,
      modelClass: join(__dirname, '/PharmacyReserveDetail'),
      join: {
        from: 'pharmacyReserveStatusHistory.pharmacyReserveDetailId',
        to: 'pharmacyReserveDetail.pharmacyReserveDetailId',
      },
    },
  };

  @Field(() => Number, { nullable: false })
  pharmacyReserveStatusHistoryId: number;

  @Field(() => Number, { nullable: false })
  pharmacyReserveDetailId: number;

  @Field(() => Number, { nullable: true })
  status: number | null;

  @Field(() => Number, { nullable: true })
  statusCancelType: number | null;

  @Field(() => Number, { nullable: true })
  guidanceStatus: number | null;

  @Field(() => Number, { nullable: true })
  pharmacistId: number | null;

  @Field(() => String, { nullable: true })
  pharmacistMemo: string | null;

  @Field(() => Date, { nullable: false })
  createdAt: Date;

  @Field(() => String, { nullable: false })
  createdBy: string;

  @Field(() => Date, { nullable: false })
  updatedAt: Date;

  @Field(() => String, { nullable: false })
  updatedBy: string;

  @Field(() => Number, { nullable: false })
  isDeleted: number;

  @Field(() => PharmacyReserveDetail, { nullable: false })
  pharmacyReserveDetail: PharmacyReserveDetail;
}
