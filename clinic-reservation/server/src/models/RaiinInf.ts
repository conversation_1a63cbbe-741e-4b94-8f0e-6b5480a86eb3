import { Field, ObjectType } from '@nestjs/graphql';

import { BaseModel } from './BaseModel';

@ObjectType()
export class RaiinInf extends BaseModel {
  static tableName = 'raiinInf';

  static idColumn = 'raiinNo';

  @Field(() => Number, { nullable: false })
  raiinNo: number;

  @Field(() => Number, { nullable: false })
  hpId: number;

  @Field(() => Number, { nullable: false })
  ptId: number;

  @Field(() => Number, { nullable: false })
  sinDate: number;

  @Field(() => Number, { nullable: false })
  oyaRaiinNo: number;

  @Field(() => Number, { nullable: false })
  status: number;

  @Field(() => Number, { nullable: false })
  isYoyaku: number;

  @Field(() => String, { nullable: true })
  yoyakuTime?: string;

  @Field(() => Number, { nullable: false })
  yoyakuId: number;

  @Field(() => Number, { nullable: false })
  uketukeSbt: number;

  @Field(() => String, { nullable: true })
  uketukeTime?: string;

  @Field(() => Number, { nullable: false })
  uketukeId: number;

  @Field(() => Number, { nullable: false })
  uketukeNo: number;

  @Field(() => String, { nullable: true })
  sinStartTime?: string;

  @Field(() => String, { nullable: true })
  sinEndTime?: string;

  @Field(() => String, { nullable: true })
  kaikeiTime?: string;

  @Field(() => Number, { nullable: false })
  kaikeiId: number;

  @Field(() => Number, { nullable: false })
  kaId: number;

  @Field(() => Number, { nullable: false })
  tantoId: number;

  @Field(() => Number, { nullable: false })
  hokenPid: number;

  @Field(() => Number, { nullable: false })
  syosaisinKbn: number;

  @Field(() => Number, { nullable: false })
  jikanKbn: number;

  @Field(() => Number, { nullable: false })
  santeiKbn: number;

  @Field(() => String, { nullable: true })
  confirmationResult?: string;

  @Field(() => Number, { nullable: false })
  confirmationState: number;

  @Field(() => Number, { nullable: false })
  confirmationType: number;

  @Field(() => String, { nullable: true })
  infoConsFlg?: string;

  @Field(() => Number, { nullable: false })
  prescriptionIssueType: number;

  @Field(() => Number, { nullable: false })
  printEpsReference: number;

  @Field(() => Number, { nullable: false })
  statusMstKbn: number;

  @Field(() => Number, { nullable: false })
  treatmentDepartmentId: number;

  @Field(() => Date, { nullable: true })
  statusKbnUpdateTime?: Date;

  @Field(() => Number, { nullable: false })
  monshinStatus: number;

  @Field(() => String, { nullable: true })
  yoyakuEndTime?: string;

  @Field(() => Number, { nullable: true })
  reserveDetailId?: number;

  @Field(() => Number, { nullable: false })
  paymentMethodCd: number;

  @Field(() => Number, { nullable: false })
  onlineConfirmationId: number;
}
