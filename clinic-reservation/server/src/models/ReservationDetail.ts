import { Field, ObjectType } from '@nestjs/graphql';
import {
  AnyQueryBuilder,
  Modifiers,
  RelationMappings,
  RelationMappingsThunk,
} from 'objection';
import { join } from 'path';

import {
  IS_DELETED,
  RESERVATION_STATUS,
  TREATMENT_TYPE,
} from '@/common/constants/master-type';

import { BaseModel } from './BaseModel';
import { CalendarTreatment } from './CalendarTreatment';
import { ExamTimeSlot } from './ExamTimeSlot';
import { Patient } from './Patient';
import { PaymentClinicDetail } from './PaymentClinicDetail';
import { PharmacyReserveDetail } from './PharmacyReserveDetail';
import { RaiinInf } from './RaiinInf';
import { Reservation } from './Reservation';
import { ReservationDetailHistory } from './ReservationDetailHistory';
import { SurveyAnswer } from './SurveyAnswer';

@ObjectType()
export class ReservationDetail extends BaseModel {
  static tableName = 'reserveDetail';

  static idColumn = 'reserveDetailId';

  static relationMappings: RelationMappings | RelationMappingsThunk = {
    reservation: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Reservation'),
      join: {
        from: 'reserveDetail.reserveId',
        to: 'reserve.reserveId',
      },
      modify: (builder) => {
        builder.where('reserve.isDeleted', IS_DELETED.FALSE);
      },
    },
    examDetails: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/ExamDetail'),
      join: {
        from: 'reserveDetail.reserveDetailId',
        to: 'portalExamDetail.reserveDetailId',
      },
      modify: (builder) => {
        builder.where('portalExamDetail.isDeleted', IS_DELETED.FALSE);
      },
    },
    calendarTreatment: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/CalendarTreatment'),
      join: {
        from: 'reserveDetail.calendarTreatmentId',
        to: 'calendarTreatment.calendarTreatmentId',
      },
    },
    patient: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/Patient'),
      join: {
        from: 'reserveDetail.patientId',
        to: 'ptInf.ptId',
      },
      modify: (builder) => {
        builder.where('ptInf.isDelete', IS_DELETED.FALSE);
      },
    },
    surveyAnswer: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, `/SurveyAnswer`),
      join: {
        from: 'reserveDetail.reserveDetailId',
        to: 'surveyAnswer.reserveDetailId',
      },
      modify: (builder) => {
        builder.where('surveyAnswer.isDeleted', IS_DELETED.FALSE);
      },
    },
    examTimeSlot: {
      relation: BaseModel.BelongsToOneRelation,
      modelClass: join(__dirname, '/ExamTimeSlot'),
      join: {
        from: 'reserveDetail.examTimeSlotId',
        to: 'examTimeSlot.examTimeSlotId',
      },
      modify: (builder) => {
        builder.where('examTimeSlot.isDeleted', IS_DELETED.FALSE);
      },
    },
    reservationDetailHistories: {
      relation: BaseModel.HasManyRelation,
      modelClass: join(__dirname, '/ReservationDetailHistory'),
      join: {
        from: 'reserveDetail.reserveDetailId',
        to: 'reserveDetailHistory.reserveDetailId',
      },
      modify: (builder) => {
        builder.where('reserveDetailHistory.isDeleted', IS_DELETED.FALSE);
      },
    },
    pharmacyReserveDetail: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/PharmacyReserveDetail'),
      filter: (builder) => builder.where('is_deleted', IS_DELETED.FALSE),
      join: {
        from: 'reserveDetail.reserveDetailId',
        to: 'pharmacyReserveDetail.reserveDetailId',
      },
    },
    paymentClinicDetail: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/PaymentClinicDetail'),
      filter: (builder) => builder.where('is_deleted', IS_DELETED.FALSE),
      join: {
        from: 'reserveDetail.reserveDetailId',
        to: 'paymentClinicDetail.reserveDetailId',
      },
    },
    raiinInf: {
      relation: BaseModel.HasOneRelation,
      modelClass: join(__dirname, '/RaiinInf'),
      filter: (builder) => {
        builder.where('is_deleted', IS_DELETED.FALSE);
      },
      join: {
        from: 'raiinInf.reserveDetailId',
        to: 'reserveDetail.reserveDetailId',
      },
    },
  };

  static modifiers: Modifiers<AnyQueryBuilder> = {
    notCancel(builder) {
      builder.whereNot('status', RESERVATION_STATUS.CANCELED);
    },
  };

  @Field(() => Number, { nullable: false })
  reserveDetailId: number;

  @Field(() => Number, { nullable: false })
  reserveId: number;

  @Field(() => Number, { nullable: true })
  patientId: number | null;

  @Field(() => Number, { nullable: true })
  examDetailId: number | null;

  @Field(() => Number, { nullable: false })
  status: number;

  @Field(() => Number, { nullable: true })
  reserveCancelType: number | null;

  @Field(() => Number, { nullable: true })
  reserveType: number | null;

  @Field(() => Number, { nullable: false })
  treatmentType: TREATMENT_TYPE;

  @Field(() => Number, { nullable: true })
  calendarTreatmentId: number | null;

  @Field(() => CalendarTreatment, { nullable: true })
  calendarTreatment: CalendarTreatment;

  @Field(() => Number, { nullable: true })
  queueId: number | null;

  @Field(() => String, { nullable: false })
  memo: string;

  @Field(() => Reservation, { nullable: true })
  reservation: Reservation;

  @Field(() => Patient, { nullable: false })
  patient: Patient;

  @Field(() => SurveyAnswer, { nullable: true })
  surveyAnswer: SurveyAnswer;

  @Field(() => String, { nullable: true })
  paymentCardId: string | null;

  @Field(() => String, { nullable: true })
  fincodeCustomerId: string | null;

  @Field(() => String, { nullable: true })
  fincodeTenantId: string | null;

  @Field(() => Boolean, { nullable: false })
  isSuspended: boolean;

  @Field(() => Number, { nullable: false })
  examTimeSlotId: number;

  @Field(() => ExamTimeSlot, { nullable: false })
  examTimeSlot: ExamTimeSlot;

  @Field(() => Number, { nullable: true })
  total: number;

  @Field(() => [ReservationDetailHistory], { nullable: true })
  reservationDetailHistories: ReservationDetailHistory[];

  @Field(() => PharmacyReserveDetail, { nullable: true })
  pharmacyReserveDetail: PharmacyReserveDetail;

  @Field(() => String, { nullable: true })
  cardExpire?: string;

  @Field(() => Number, { nullable: false })
  paymentStatus: number;

  @Field(() => PaymentClinicDetail, { nullable: true })
  paymentClinicDetail?: PaymentClinicDetail;

  @Field(() => RaiinInf, { nullable: true })
  raiinInf?: RaiinInf;

  sortDate: Date;

  public get isCanceled(): boolean {
    return this.status === RESERVATION_STATUS.CANCELED;
  }
}
