import { Field, InputType, registerEnumType } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsObject, ValidateNested } from 'class-validator';

export enum DiseaseType {
  Diabetes = 'Diabetes',
  HighBloodPressure = 'HighBloodPressure',
  HeartDisease = 'HeartDisease',
  KidneyDisease = 'KidneyDisease',
  LiverDisease = 'LiverDisease',
  Asthma = 'Asthma',
  Stroke = 'Stroke',
  Cancer = 'Cancer',
  ProstateEnlargement = 'ProstateEnlargement',
  Glaucoma = 'Glaucoma',
  MentalIllness = 'MentalIllness',
  None = 'None',
  Other = 'Other',
}

registerEnumType(DiseaseType, {
  name: 'DiseaseType',
});

export enum AllergyType {
  DrugAllergy = 'DrugAllergy',
  FoodAllergy = 'FoodAllergy',
  PollenDustAllergy = 'PollenDustAllergy',
  Other = 'Other',
  None = 'None',
}

registerEnumType(AllergyType, {
  name: 'AllergyType',
});

@InputType()
export class CustomerSurveyCommon {
  @Field(() => Number, { nullable: true })
  height: number | null;

  @Field(() => Number, { nullable: true })
  weight: number | null;

  @Field(() => [DiseaseType])
  diseases: DiseaseType[];

  @Field(() => String)
  diseasesOtherDetail: string;

  @Field(() => [AllergyType])
  allergies: AllergyType[];

  @Field(() => String)
  allergiesOtherDetail: string;

  @Field(() => String)
  foodAllergyDetail: string;

  @Field(() => String)
  drugAllergyDetail: string;

  @Field(() => String)
  pollenDustAllergyDetail: string;

  @Field(() => Number, { nullable: true })
  drinkingFrequency: number;

  @Field(() => Number, { nullable: true })
  smokingStatus: number;

  @Field(() => Number, { nullable: true })
  pregnancyStatus: number;

  @Field(() => Number, { nullable: true })
  breastfeedStatus: number;
}

@InputType()
export class CustomerSurveyCommonInput {
  @Field(() => Number, { nullable: false })
  customerId: number;

  @Field(() => CustomerSurveyCommon)
  @ValidateNested()
  @Type(() => CustomerSurveyCommon)
  @IsObject()
  common: CustomerSurveyCommon;
}

@InputType()
class SideEffectType {
  @Field(() => String)
  name: string;

  @Field(() => String)
  timing: string;

  @Field(() => String)
  symptoms: string;
}

export enum PhysicalConditionType {
  WeakStomach = 'WeakStomach',
  ProneToDiarrhea = 'ProneToDiarrhea',
  ProneToConstipation = 'ProneToConstipation',
  PoorSleepOnset = 'PoorSleepOnset',
  ProneToFever = 'ProneToFever',
  FrequentHeadaches = 'FrequentHeadaches',
  None = 'None',
  Other = 'Other',
}

registerEnumType(PhysicalConditionType, {
  name: 'PhysicalConditionType',
});

export enum LifestyleHabitType {
  Driving = 'Driving',
  DangerousWork = 'DangerousWork',
  NightShiftWork = 'NightShiftWork',
  IrregularMeal = 'IrregularMeal',
  None = 'None',
}

registerEnumType(LifestyleHabitType, {
  name: 'LifestyleHabitType',
});

export enum RegularDrinkType {
  Caffeine = 'Caffeine',
  Citrus = 'Citrus',
  Milk = 'Milk',
  NutritionSupplement = 'NutritionSupplement',
  None = 'None',
  Other = 'Other',
}

registerEnumType(RegularDrinkType, {
  name: 'RegularDrinkType',
});

export enum GettingMedIssueType {
  HearingImpairment = 'HearingImpairment',
  VisualImpairment = 'VisualImpairment',
  SpeechImpairment = 'SpeechImpairment',
  HandImpairment = 'HandImpairment',
  LegImpairment = 'LegImpairment',
  Other = 'Other',
}

registerEnumType(GettingMedIssueType, {
  name: 'GettingMedIssueType',
});

export enum TakingMedIssueType {
  PowderMed = 'PowderMed',
  CapsuleMed = 'CapsuleMed',
  Other = 'Other',
}

registerEnumType(TakingMedIssueType, {
  name: 'TakingMedIssueType',
});

@InputType()
export class CustomerSurveyPharmacy {
  @Field(() => Number, { nullable: true })
  hasSideEffect: number;

  @Field(() => [SideEffectType])
  sideEffects: SideEffectType[];

  @Field(() => [PhysicalConditionType])
  physicalConditions: PhysicalConditionType[];

  @Field(() => String)
  physicalConditionsOtherDetail: string;

  @Field(() => [LifestyleHabitType])
  lifestyleHabits: LifestyleHabitType[];

  @Field(() => [RegularDrinkType])
  regularDrinks: RegularDrinkType[];

  @Field(() => String)
  regularDrinksOtherDetail: string;

  @Field(() => Number, { nullable: true })
  hasIssueGettingMed: number;

  @Field(() => [GettingMedIssueType])
  issuesGettingMed: GettingMedIssueType[];

  @Field(() => String)
  issuesGettingMedOtherDetail: string;

  @Field(() => Number, { nullable: true })
  hasIssueTakingMed: number;

  @Field(() => [TakingMedIssueType])
  issuesTakingMed: TakingMedIssueType[];

  @Field(() => String)
  issuesTakingMedOtherDetail: string;

  @Field(() => Number, { nullable: true })
  isMedSelfManaged: number;

  @Field(() => String)
  otherInfo: string;
}

@InputType()
export class CustomerSurveyPharmacyInput {
  @Field(() => Number, { nullable: false })
  customerId: number;

  @Field(() => CustomerSurveyPharmacy)
  @ValidateNested()
  @Type(() => CustomerSurveyPharmacy)
  @IsObject()
  pharmacy: CustomerSurveyPharmacy;
}
