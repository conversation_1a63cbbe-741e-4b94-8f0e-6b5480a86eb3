import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Resolver } from '@nestjs/graphql';

import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import { ActiveCustomerLoginGuard } from '@/common/guards/customer/active-customer-login.guard';

import { ERROR_TYPE } from '../common/constants/error-type';
import { LoginCustomer } from '../customer/customer.output';
import { CustomerService } from '../customer/customer.service';
import {
  CustomerSurveyCommonInput,
  CustomerSurveyPharmacyInput,
} from './customer-survey.input';
import { CustomerSurveyUpdateResponse } from './customer-survey.output';
import { CustomerSurveyService } from './customer-survey.service';

@Resolver()
export class CustomerSurveyResolver {
  constructor(
    private readonly customerSurveyService: CustomerSurveyService,
    private customerService: CustomerService,
  ) {}

  @Mutation(() => CustomerSurveyUpdateResponse)
  @UseGuards(ActiveCustomerLoginGuard, ActiveAgreeGuard)
  async editCustomerSurveyCommon(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('customerSurveyInput', { type: () => CustomerSurveyCommonInput })
    customerSurveyInput: CustomerSurveyCommonInput,
  ): Promise<CustomerSurveyUpdateResponse> {
    const isValidCustomer = await this.customerService.validateIsFamilyMember(
      loginCustomer,
      customerSurveyInput.customerId,
    );

    if (!isValidCustomer) {
      return {
        message: '特権が無い',
        error: ERROR_TYPE.PERMISSION_DENIED,
      };
    }

    return this.customerSurveyService.editCustomerSurveyCommon(
      customerSurveyInput,
    );
  }

  @Mutation(() => CustomerSurveyUpdateResponse)
  @UseGuards(ActiveCustomerLoginGuard, ActiveAgreeGuard)
  async editCustomerSurveyPharmacy(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('customerSurveyPharmacyInput', {
      type: () => CustomerSurveyPharmacyInput,
    })
    customerSurveyPharmacyInput: CustomerSurveyPharmacyInput,
  ): Promise<CustomerSurveyUpdateResponse> {
    const isValidCustomer = await this.customerService.validateIsFamilyMember(
      loginCustomer,
      customerSurveyPharmacyInput.customerId,
    );

    if (!isValidCustomer) {
      return {
        message: '特権が無い',
        error: ERROR_TYPE.PERMISSION_DENIED,
      };
    }

    return this.customerSurveyService.editCustomerSurveyPharmacy(
      customerSurveyPharmacyInput,
    );
  }
}
