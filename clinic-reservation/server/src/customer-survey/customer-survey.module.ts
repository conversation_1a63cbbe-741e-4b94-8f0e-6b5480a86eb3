import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AgreeService } from '@/agree/agree.service';
import { TokenService } from '@/common/service/token.service';
import { ReservationModule } from '@/reservation/reservation.module';

import { CustomerModule } from '../customer/customer.module';
import { CustomerSurveyResolver } from './ccustomer-survey.resolver';
import { CustomerSurveyService } from './customer-survey.service';

@Module({
  imports: [HttpModule, CustomerModule, ReservationModule],
  providers: [
    CustomerSurveyResolver,
    CustomerSurveyService,
    AgreeService,
    TokenService,
  ],
})
export class CustomerSurveyModule {}
