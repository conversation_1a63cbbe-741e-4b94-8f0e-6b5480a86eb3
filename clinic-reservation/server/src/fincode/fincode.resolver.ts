import { UseGuards } from '@nestjs/common';
import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';

import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { LoginCustomer } from '@/customer/customer.output';

import { RegisterCardInput } from './fincode.input';
import {
  CardResponse,
  FincodeIdResponse,
  RegisterCardResponse,
} from './fincode.output';
import { FincodeService } from './fincode.service';

@Resolver()
export default class FincodeResolver {
  constructor(private fincodeService: FincodeService) {}

  @Query(() => FincodeIdResponse)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getFincodeCustomer(
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<FincodeIdResponse> {
    let customerPayment = await this.fincodeService.getCustomerPayment(
      loginCustomer.customerId,
    );
    if (!customerPayment) {
      customerPayment = await this.fincodeService.createCustomer(
        loginCustomer.customerId,
      );
    }
    return {
      insurancePlatformId:
        customerPayment!.insurancePlatformPayment.fincodeCustomerId,
      freePlatformId: customerPayment!.freePlatformPayment.fincodeCustomerId,
      insuranceHtPlatformId:
        customerPayment!.insuranceHtPlatformPayment.fincodeCustomerId,
      freeHtPlatformId:
        customerPayment!.freeHtPlatformPayment.fincodeCustomerId,
    };
  }

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Query(() => CardResponse, { nullable: true })
  async getCards(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('platformId', { type: () => Int }) platformId: number,
  ): Promise<CardResponse | null> {
    return await this.fincodeService.getCardByCustomerId(
      loginCustomer.customerId,
      platformId,
    );
  }

  @Mutation(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async deleteCards(
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<boolean> {
    await this.fincodeService.deleteCard(loginCustomer);
    return true;
  }

  @Query(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async checkIsDeletableCard(
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<boolean> {
    return await this.fincodeService.checkIsDeletableCard(
      loginCustomer.customerId,
    );
  }

  @Mutation(() => RegisterCardResponse)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async registerCard(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('input', { type: () => RegisterCardInput }) input: RegisterCardInput,
  ): Promise<RegisterCardResponse> {
    return this.fincodeService.registerCard(loginCustomer.customerId, input);
  }
}
