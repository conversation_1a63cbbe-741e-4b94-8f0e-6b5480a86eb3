import { AxiosInstance } from 'axios';

import {
  CardInfo,
  CustomerInformationResponse,
  DEFAULT_CARD_FLAG,
  DeleteCardSuccess,
  DeleteCustomerSuccess,
  FincodeError,
  RegisterCardSuccess,
  RegisterFincodeCardArgs,
  TDS_TYPE,
} from './type';

/**
 *
 * ref: https://docs.fincode.jp/api#tag/%E9%A1%A7%E5%AE%A2/operation/postCustomers
 */
export const postCustomers = async (
  axiosService: AxiosInstance,
): Promise<CustomerInformationResponse> => {
  const res = await axiosService.post('v1/customers', {});
  return res.data;
};

/**
 *
 * ref: https://docs.fincode.jp/api#tag/%E3%82%AB%E3%83%BC%E3%83%89/operation/getCustomersCustomer_idCards
 */
export const getCardsByCustomerId = async (
  axiosService: AxiosInstance,
  customerId: string,
): Promise<CardInfo[]> => {
  const endpoint = '/v1/customers/{customer_id}/cards'.replace(
    '{customer_id}',
    customerId,
  );
  const res = await axiosService.get(endpoint);
  return res.data.list;
};

/**
 *
 * ref: https://docs.fincode.jp/api#tag/%E3%82%AB%E3%83%BC%E3%83%89/operation/deleteCustomersCustomer_idCardsId
 */
export const deleteCustomersCustomerIdCardsId = async (
  axiosService: AxiosInstance,
  customerId: string,
  cardId: string,
): Promise<DeleteCardSuccess> => {
  const endpoint = '/v1/customers/{customer_id}/cards/{id}'
    .replace('{customer_id}', customerId)
    .replace('{id}', cardId);
  const dataRes = await axiosService.delete(endpoint);
  return dataRes.data;
};

/**
 *
 * ref: https://docs.fincode.jp/api#tag/%E9%A1%A7%E5%AE%A2/operation/deleteCustomer
 */
export const deleteCustomer = async (
  axiosService: AxiosInstance,
  customerId: string,
): Promise<DeleteCustomerSuccess> => {
  const res = await axiosService.delete(`/v1/customers/${customerId}`);
  return res.data;
};

/**
 *
 * ref: https://docs.fincode.jp/api#tag/%E6%B1%BA%E6%B8%88%E6%89%8B%E6%AE%B5/operation/createCustomerPaymentMethod
 */
export const registerCard = async (
  args: RegisterFincodeCardArgs,
): Promise<RegisterCardSuccess> => {
  const {
    axiosService,
    customerId,
    token,
    successReturnUrl,
    failedReturnUrl,
    shouldVerifyCard,
  } = args;
  const res = await axiosService.post(
    `/v1/customers/${customerId}/payment_methods`,
    {
      pay_type: 'Card',
      default_flag: DEFAULT_CARD_FLAG,
      return_url: successReturnUrl,
      return_url_on_failure: failedReturnUrl,
      card: {
        token,
        tds_type: shouldVerifyCard
          ? TDS_TYPE.THREE_D_SECURE
          : TDS_TYPE.NOT_THREE_D_SECURE,
      },
    },
  );

  const resData = res.data as RegisterCardSuccess;
  return {
    id: resData.id,
    customer_id: resData.customer_id,
    redirect_url: resData.redirect_url,
    status: resData.status,
  };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isFincodeError(obj: any): obj is FincodeError {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    Array.isArray(obj.errors) &&
    obj.errors.every(
      (error: { error_code: string; error_message: string }) =>
        typeof error.error_code === 'string' &&
        typeof error.error_message === 'string',
    )
  );
}
