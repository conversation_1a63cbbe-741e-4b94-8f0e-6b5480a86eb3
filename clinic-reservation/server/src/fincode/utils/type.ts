import { Field, ObjectType } from '@nestjs/graphql';
import { AxiosInstance } from 'axios';

// https://docs.fincode.jp/api#tag/%E9%A1%A7%E5%AE%A2/operation/postCustomers
type PostCustomerRequest = {
  id?: string | null;
  name: string;
  email: string;
  phone_cc?: string;
  phone_no?: string;
  addr_city?: string;
  addr_country?: string;
  addr_line_1?: string;
  addr_line_2?: string | null;
  addr_line_3?: string | null;
  addr_post_code?: string;
  addr_state?: string;
};

export type CustomerInformationResponse = PostCustomerRequest & {
  card_registration: string;
  created: string;
  updated: string;
};

type CardBrand = {
  /**
   * string[ 1 .. 50 ] characters
   *
   * If the international brand cannot be identified, it will be an empty string.
   */
  brand: '' | 'VISA' | 'MASTER' | 'JCB' | 'AMEX' | 'DINERS';
};

// https://docs.fincode.jp/api#tag/%E3%82%AB%E3%83%BC%E3%83%89/operation/getCustomersCustomer_idCards
export type CardInfo = CardBrand & {
  customer_id: string;
  id: string;
  /**
   * string= 1 characters
   *
   * default flag1:ON 0:OFF
   */
  default_flag: '0' | '1';
  card_no: string;
  /**
   * string= 4 characters
   *
   * Card expiration date.yymm形式
   */
  expire: string;
  holder_name: string;
  /**
   * string [ 1 .. 64 ] characters
   *
   * Card number hash value
   */
  card_no_hash: string;
  created: string;
  /**
   * string^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2}).(\d{3})$
   *
   * Update date-time YYYY/MM/dd HH:mm:ss.SSSform
   */
  updated: string;
  /**
   * string= 1 characters
   *
   * Card type
   *
   * 0- not clear
   *
   * 1- debit card
   *
   * 2- prepaid card
   *
   * 3- credit card
   */
  type: '0' | '1' | '2' | '3';
};

/**
 * https://docs.fincode.jp/api#tag/%E3%82%AB%E3%83%BC%E3%83%89/operation/deleteCustomersCustomer_idCardsId
 */
export type DeleteCardSuccess = {
  /**
   * string [ 1 .. 60 ] characters
   *
   * 顧客ID
   */
  customer_id: string;

  /**
   * string = 25 characters
   *
   * カードID
   */
  id: string;

  /**
   * string = 1 characters
   *
   * 削除フラグ
   *
   * 必須項目
   */
  delete_flag: '1';
};

/**
 * https://docs.fincode.jp/api#tag/%E9%A1%A7%E5%AE%A2/operation/deleteCustomer
 */
export type DeleteCustomerSuccess = {
  /**
   * string [ 1 .. 60 ] characters
   *
   * 顧客ID
   */
  customer_id: string;

  /**
   * Enum: "0" "1"
   *
   * 削除フラグ
   */
  delete_flag: '0' | '1';
};

export const DEFAULT_CARD_FLAG = '1';

export const REGISTER_CARD_STATUS = {
  INACTIVATED: 'INACTIVATED',
  AWAITING_CUSTOMER_ACTION: 'AWAITING_CUSTOMER_ACTION',
  ACTIVATED: 'ACTIVATED',
  FAILED: 'FAILED',
} as const;

type REGISTER_CARD_STATUS_TYPE =
  (typeof REGISTER_CARD_STATUS)[keyof typeof REGISTER_CARD_STATUS];

@ObjectType()
export class RegisterCardSuccess {
  /**
   * 決済手段ID
   */
  id: string;

  customer_id: string;

  /**
   * リダイレクトURL
   *
   * 購入者をこのURLへリダイレクトさせ、ステータスがAWAITING_CUSTOMER_ACTION の決済手段を有効化するための後続の処理へ誘導してください。
   */
  @Field(() => String)
  redirect_url: string;

  /**
   * 決済手段のステータス
   *
   * INACTIVATED: 有効化前
   * まだこの決済手段は決済に使用できません。
   *
   * AWAITING_CUSTOMER_ACTION: 顧客のアクション待ち
   * この決済手段は登録のための購入者の承認がされていない状態です。まだこの決済手段は決済に使用できません。
   *
   * ACTIVATED: 有効化済み
   * この決済手段は決済に使用できます。
   *
   * FAILED: 失敗
   * この決済手段は決済に使用できません。
   */
  @Field(() => String)
  status: REGISTER_CARD_STATUS_TYPE;
}

export type RegisterFincodeCardArgs = {
  axiosService: AxiosInstance;
  customerId: string;
  token: string;
  successReturnUrl?: string;
  failedReturnUrl?: string;
  isDefaultCard?: boolean;
  shouldVerifyCard?: boolean;
};

export type FincodeError = {
  errors: {
    error_code: string;
    error_message: string;
  }[];
};

export type RegisterCardState = {
  customerId: number;
  cardId: number;
  fincodeInsuranceCard: RegisterCardSuccess;
  fincodeFreeCard: RegisterCardSuccess;
  fincodeHtInsuranceCard: RegisterCardSuccess;
  fincodeHtFreeCard: RegisterCardSuccess;
  actionBeforeRegister: number;
};

export const TDS_TYPE = {
  NOT_THREE_D_SECURE: '0',
  THREE_D_SECURE: '2',
} as const;
