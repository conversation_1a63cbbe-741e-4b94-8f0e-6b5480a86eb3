import { Field, ObjectType } from '@nestjs/graphql';

import { CustomerPayment } from '@/models/CustomerPayment';

export class CustomerPaymentResponse {
  insurancePlatformPayment: CustomerPayment;
  freePlatformPayment: CustomerPayment;
  insuranceHtPlatformPayment: CustomerPayment;
  freeHtPlatformPayment: CustomerPayment;
}

@ObjectType()
export class FincodeIdResponse {
  @Field(() => String)
  insurancePlatformId: string;

  @Field(() => String)
  freePlatformId: string;

  @Field(() => String)
  insuranceHtPlatformId: string;

  @Field(() => String)
  freeHtPlatformId: string;
}

@ObjectType()
export class CardResponse {
  @Field(() => String)
  brand: '' | 'VISA' | 'MASTER' | 'JCB' | 'AMEX' | 'DINERS';

  @Field(() => String)
  customer_id: string;

  @Field(() => String)
  id: string;

  @Field(() => String)
  default_flag: '0' | '1';

  @Field(() => String)
  card_no: string;

  @Field(() => String)
  expire: string;

  @Field(() => String)
  holder_name: string;

  @Field(() => String)
  card_no_hash: string;

  @Field(() => String)
  created: string;

  @Field(() => String)
  updated: string;

  @Field(() => String)
  type: '0' | '1' | '2' | '3';
}

@ObjectType()
export class DeleteCardResponse {
  @Field(() => String, { nullable: true })
  error?: string;
  @Field(() => String, { nullable: true })
  message?: string;
  @Field(() => String, { nullable: true })
  deleteFlag?: string;
}

@ObjectType()
export class DeleteCustomerResponse {
  success: boolean;
}

@ObjectType()
export class RegisterCardResponse {
  @Field(() => String)
  redirectUrl: string;
}

export class RedirectResponse {
  url: string;
}
