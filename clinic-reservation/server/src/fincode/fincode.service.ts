import { Inject, Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { ModelClass, Transaction } from 'objection';
import { v4 as uuidv4 } from 'uuid';

import { RESERVER_REQUEST_TYPE, TEMPLATE_MAIL_CODE } from '@/common/constants';
import { ERROR_TYPE } from '@/common/constants/error-type';
import {
  IS_DELETED,
  PAYMENT_ACTION_TYPE,
  PAYMENT_DETAIL_IS_RETRYABLE,
  PAYMENT_PLATFORM,
  PAYMENT_STATUS,
  PAYMENT_TYPE,
  RESERVATION_STATUS,
  RESERVATION_TYPE,
} from '@/common/constants/master-type';
import { Logger } from '@/common/logger/logger';
import { MailService } from '@/common/service/mail.service';
import { ClinicError } from '@/common/utils/error';
import { execWithTx } from '@/common/utils/transaction';
import { LoginCustomer } from '@/customer/customer.output';
import { Customer } from '@/models/Customer';
import { CustomerPayment } from '@/models/CustomerPayment';
import { CustomerPaymentCard } from '@/models/CustomerPaymentCard';
import { Login } from '@/models/Login';
import { PaymentClinicDetail } from '@/models/PaymentClinicDetail';
import { PaymentPharmacyDetail } from '@/models/PaymentPharmacyDetail';
import { ReservationDetail } from '@/models/ReservationDetail';
import PharmacyService from '@/pharmacy/pharmacy.service';
import { RedisService } from '@/redis/redis.service';

import { RegisterCardInput } from './fincode.input';
import {
  CustomerPaymentResponse,
  DeleteCardResponse,
  DeleteCustomerResponse,
  RegisterCardResponse,
} from './fincode.output';
import {
  deleteCustomer,
  deleteCustomersCustomerIdCardsId,
  getCardsByCustomerId,
  isFincodeError,
  postCustomers,
  registerCard,
} from './utils/server';
import {
  CardInfo,
  DEFAULT_CARD_FLAG,
  REGISTER_CARD_STATUS,
  RegisterCardState,
  RegisterCardSuccess,
} from './utils/type';

@Injectable()
export class FincodeService {
  constructor(
    @Inject('CustomerPayment')
    private customerPayment: ModelClass<CustomerPayment>,
    @Inject('Customer')
    private customer: ModelClass<Customer>,
    @Inject('Login')
    private login: ModelClass<Login>,
    @Inject('PaymentClinicDetail')
    private paymentClinicDetail: ModelClass<PaymentClinicDetail>,
    @Inject('PaymentPharmacyDetail')
    private paymentPharmacyDetail: ModelClass<PaymentPharmacyDetail>,
    @Inject('ReservationDetail')
    private reservationDetail: ModelClass<ReservationDetail>,
    @Inject('CustomerPaymentCard')
    private customerPaymentCard: ModelClass<CustomerPaymentCard>,

    private readonly mailService: MailService,
    private readonly pharmacyService: PharmacyService,
    private readonly logger: Logger,
    private readonly redisService: RedisService,
  ) {}

  private _httpInsuranceRequest = axios.create({
    baseURL: process.env.FINCODE_BASE_URL_JS,
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Authorization: `Bearer ${process.env.FINCODE_INSURANCE_MEDICAL_SK}`,
    },
  });

  private _httpFreeRequest = axios.create({
    baseURL: process.env.FINCODE_BASE_URL_JS,
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Authorization: `Bearer ${process.env.FINCODE_FREE_MEDICAL_SK}`,
    },
  });

  private _httpInsuranceHtRequest = axios.create({
    baseURL: process.env.FINCODE_BASE_URL_JS,
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Authorization: `Bearer ${process.env.FINCODE_HT_INSURANCE_MEDICAL_SK}`,
    },
  });

  private _httpFreeHtRequest = axios.create({
    baseURL: process.env.FINCODE_BASE_URL_JS,
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Authorization: `Bearer ${process.env.FINCODE_HT_FREE_MEDICAL_SK}`,
    },
  });

  async createCustomer(
    customerId: number,
  ): Promise<CustomerPaymentResponse | null> {
    const customerPaymentInsertData = [];
    const {
      insurancePlatformPayment,
      freePlatformPayment,
      insuranceHtPlatformPayment,
      freeHtPlatformPayment,
    } = await this.getCustomerPaymentDetail(customerId);

    if (!insurancePlatformPayment) {
      const insurancePlatformCustomer = await postCustomers(
        this._httpInsuranceRequest,
      );

      if (!insurancePlatformCustomer.id) {
        throw new ClinicError(ERROR_TYPE.REGISTER_CUSTOMER_TO_FINCODE_FAIL);
      }

      customerPaymentInsertData.push({
        createdBy: 'API',
        updatedBy: 'API',
        customerId: customerId,
        fincodeCustomerId: insurancePlatformCustomer.id,
        platformId: PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
      });
    }

    if (!freePlatformPayment) {
      const freePlatformCustomer = await postCustomers(this._httpFreeRequest);

      if (!freePlatformCustomer.id) {
        throw new ClinicError(ERROR_TYPE.REGISTER_CUSTOMER_TO_FINCODE_FAIL);
      }

      customerPaymentInsertData.push({
        createdBy: 'API',
        updatedBy: 'API',
        customerId: customerId,
        fincodeCustomerId: freePlatformCustomer.id,
        platformId: PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
      });
    }

    if (!insuranceHtPlatformPayment) {
      const insuranceHtPlatformCustomer = await postCustomers(
        this._httpInsuranceHtRequest,
      );

      if (!insuranceHtPlatformCustomer.id) {
        throw new ClinicError(ERROR_TYPE.REGISTER_CUSTOMER_TO_FINCODE_FAIL);
      }

      customerPaymentInsertData.push({
        createdBy: 'API',
        updatedBy: 'API',
        customerId: customerId,
        fincodeCustomerId: insuranceHtPlatformCustomer.id,
        platformId: PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
      });
    }

    if (!freeHtPlatformPayment) {
      const freeHtPlatformCustomer = await postCustomers(
        this._httpFreeHtRequest,
      );

      if (!freeHtPlatformCustomer.id) {
        throw new ClinicError(ERROR_TYPE.REGISTER_CUSTOMER_TO_FINCODE_FAIL);
      }

      customerPaymentInsertData.push({
        createdBy: 'API',
        updatedBy: 'API',
        customerId: customerId,
        fincodeCustomerId: freeHtPlatformCustomer.id,
        platformId: PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
      });
    }

    if (customerPaymentInsertData.length) {
      await this.customerPayment.query().insert(customerPaymentInsertData);
    }

    return this.getCustomerPayment(customerId);
  }

  async updateDataAfterChangeCard(
    tx: Transaction,
    customerId: number,
    dbCardId: number,
  ): Promise<void> {
    // delete old card in db
    await this.customerPaymentCard
      .query(tx)
      .where('customerId', customerId)
      .whereNot('cardId', dbCardId)
      .where('isDeleted', IS_DELETED.FALSE)
      .patch({
        isDeleted: IS_DELETED.TRUE,
      });

    // update isRetryable
    await this.updateIsRetryable(customerId, tx);

    // delete old card in fincode
    try {
      const customerPayment = await this.getCustomerPayment(customerId);
      if (!customerPayment) {
        return;
      }

      // delete old card in fincode
      await Promise.all([
        this.deleteOldCard(
          customerPayment.freePlatformPayment.fincodeCustomerId,
          PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
        ),
        this.deleteOldCard(
          customerPayment.insurancePlatformPayment.fincodeCustomerId,
          PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
        ),
        this.deleteOldCard(
          customerPayment.freeHtPlatformPayment.fincodeCustomerId,
          PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
        ),
        this.deleteOldCard(
          customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
          PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
        ),
      ]);
    } catch (err) {
      this.logger.error(
        'Failed to delete not default cards after update card success',
        err,
      );
    }

    // ご登録情報変更完了のお知らせのメールを送信
    try {
      const customerLogin = await this.login
        .query()
        .where('customerId', customerId)
        .first();

      if (customerLogin) {
        await this.mailService.sendMailByMailCode(
          customerLogin.email,
          TEMPLATE_MAIL_CODE.CHANGE_CUSTOMER_INFO,
        );
      }
    } catch (err) {
      this.logger.error(
        '登録情報変更完了のお知らせメールの送信に失敗しました。',
        err,
      );
    }
  }

  private async deleteOldCard(
    fincodeCustomerId: string,
    platformId: PAYMENT_PLATFORM,
  ): Promise<void> {
    const httpRequest = this.getHttpRequestByPlatformId(platformId);
    const cardList = await getCardsByCustomerId(httpRequest, fincodeCustomerId);

    const notDefaultCardList = cardList.filter(
      (card) => card.default_flag !== DEFAULT_CARD_FLAG,
    );

    // delete all not default card after change card
    if (notDefaultCardList.length) {
      const deleteCardReq = notDefaultCardList.map((card) =>
        this.deleteCardByFincodeCustomerId(
          fincodeCustomerId,
          card.id,
          platformId,
        ),
      );
      await Promise.all(deleteCardReq);
    }
  }

  private async getFamilyMemberIds(customerId: number): Promise<number[]> {
    const familyMember = await this.customer
      .query()
      .where('parentId', customerId);

    return [customerId, ...familyMember.map((member) => member.customerId)];
  }

  private async updateIsRetryable(
    customerId: number,
    tx: Transaction,
  ): Promise<void> {
    const familyMemberIds = await this.getFamilyMemberIds(customerId);

    // Update isRetryable to true for all reservation payment failed
    await this.paymentClinicDetail
      .query(tx)
      .whereIn('customerId', familyMemberIds)
      .where('paymentStatus', PAYMENT_STATUS.FAILED)
      .where('paymentType', PAYMENT_TYPE.CARD)
      .where('actionType', PAYMENT_ACTION_TYPE.NEW)
      .where('isDeleted', IS_DELETED.FALSE)
      .patch({
        isRetryable: PAYMENT_DETAIL_IS_RETRYABLE.TRUE,
      });

    // Update isRetryable to true for all pharmacy reservation payment failed
    await this.paymentPharmacyDetail
      .query(tx)
      .whereIn('customerId', familyMemberIds)
      .where('paymentStatus', PAYMENT_STATUS.FAILED)
      .where('paymentType', PAYMENT_TYPE.CARD)
      .where('actionType', PAYMENT_ACTION_TYPE.NEW)
      .where('isDeleted', IS_DELETED.FALSE)
      .patch({
        isRetryable: PAYMENT_DETAIL_IS_RETRYABLE.TRUE,
      });
  }

  async deleteCard(loginCustomer: LoginCustomer): Promise<void> {
    const customerId = loginCustomer.customerId;
    const isDeletable = await this.checkIsDeletableCard(customerId);
    if (!isDeletable) {
      throw new ClinicError(ERROR_TYPE.EXIST_UNCOMPLETED_ONLINE_RESERVATION);
    }

    const customerPayment = await this.getCustomerPayment(customerId);
    if (!customerPayment) {
      throw new ClinicError(ERROR_TYPE.INTERNAL_SERVER_ERROR);
    }

    await this.customerPaymentCard
      .query()
      .where('customerId', customerId)
      .patch({
        isDeleted: IS_DELETED.TRUE,
      });

    const insuranceCardList = await getCardsByCustomerId(
      this._httpInsuranceRequest,
      customerPayment.insurancePlatformPayment.fincodeCustomerId,
    );

    if (insuranceCardList.length) {
      const deleteCardReq = insuranceCardList.map((card) =>
        this.deleteCardByFincodeCustomerId(
          card.customer_id,
          card.id,
          PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
        ),
      );
      await Promise.all(deleteCardReq);
    }

    const freeCardList = await getCardsByCustomerId(
      this._httpFreeRequest,
      customerPayment.freePlatformPayment.fincodeCustomerId,
    );
    if (freeCardList.length) {
      const deleteCardReq = freeCardList.map((card) =>
        this.deleteCardByFincodeCustomerId(
          card.customer_id,
          card.id,
          PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
        ),
      );
      await Promise.all(deleteCardReq);
    }

    const insuranceHtCardList = await getCardsByCustomerId(
      this._httpInsuranceHtRequest,
      customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
    );
    if (insuranceHtCardList.length) {
      const deleteCardReq = insuranceHtCardList.map((card) =>
        this.deleteCardByFincodeCustomerId(
          card.customer_id,
          card.id,
          PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
        ),
      );
      await Promise.all(deleteCardReq);
    }

    const freeHtCardList = await getCardsByCustomerId(
      this._httpFreeHtRequest,
      customerPayment.freeHtPlatformPayment.fincodeCustomerId,
    );
    if (freeHtCardList.length) {
      const deleteCardReq = freeHtCardList.map((card) =>
        this.deleteCardByFincodeCustomerId(
          card.customer_id,
          card.id,
          PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
        ),
      );
      await Promise.all(deleteCardReq);
    }

    // ご登録情報変更完了のお知らせのメールを送信
    if (
      insuranceCardList.length &&
      freeCardList.length &&
      insuranceHtCardList.length &&
      freeHtCardList.length
    ) {
      try {
        await this.mailService.sendMailByMailCode(
          loginCustomer.email,
          TEMPLATE_MAIL_CODE.CHANGE_CUSTOMER_INFO,
        );
      } catch (err) {
        this.logger.error(
          '登録情報変更完了のお知らせメールの送信に失敗しました。',
          err,
        );
      }
    }
  }

  async checkIsDeletableCard(customerId: number): Promise<boolean> {
    const familyMemberIds = await this.getFamilyMemberIds(customerId);
    const countUncompletedOnlineReservationPromise = this.reservationDetail
      .query()
      .innerJoinRelated('patient')
      .whereIn('patient.portalCustomerId', familyMemberIds)
      .where('isDeleted', IS_DELETED.FALSE)
      .where('reserveType', RESERVATION_TYPE.ONLINE)
      .whereNotIn('status', [
        RESERVATION_STATUS.CANCELED,
        RESERVATION_STATUS.APPOINTMENT_COMPLETED,
      ])
      .resultSize();
    const [countUncompletedOnlineReservation, upcomingPharmacyReserve] =
      await Promise.all([
        countUncompletedOnlineReservationPromise,
        this.pharmacyService.getPharmacyReservesByConditions(
          familyMemberIds,
          RESERVER_REQUEST_TYPE.UP_COMING_RESERVE,
        ),
      ]);

    return (
      !countUncompletedOnlineReservation && !upcomingPharmacyReserve.length
    );
  }

  async getCardByCustomerId(
    customerId: number,
    platformId?: PAYMENT_PLATFORM,
  ): Promise<CardInfo | null> {
    const customerPayment = await this.getCustomerPayment(customerId);
    if (!customerPayment) {
      return null;
    }

    await this.handleDeleteNotVerifiedCard(customerId, customerPayment);

    const dbCardList = await this.customerPaymentCard.query().where({
      customerId,
      isDeleted: IS_DELETED.FALSE,
    });

    if (!dbCardList.length) {
      return null;
    }

    const fincodeInsuranceCard = await this.getDefaultCard(
      customerPayment.insurancePlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
    );

    const fincodeFreeCard = await this.getDefaultCard(
      customerPayment.freePlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
    );

    const fincodeHtInsuranceCard = await this.getDefaultCard(
      customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
    );

    const fincodeHtFreeCard = await this.getDefaultCard(
      customerPayment.freeHtPlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
    );

    // If no card, return null
    if (
      !fincodeInsuranceCard ||
      !fincodeFreeCard ||
      !fincodeHtInsuranceCard ||
      !fincodeHtFreeCard
    ) {
      return null;
    }

    // compare all card info
    const cardList = [
      fincodeInsuranceCard,
      fincodeFreeCard,
      fincodeHtInsuranceCard,
      fincodeHtFreeCard,
    ];
    const isAllCardSame = cardList.every(
      (card) =>
        card.card_no === fincodeInsuranceCard.card_no &&
        card.expire === fincodeInsuranceCard.expire,
    );

    if (!isAllCardSame) {
      return null;
    }

    switch (platformId) {
      case PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT:
        return fincodeInsuranceCard;
      case PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT:
        return fincodeFreeCard;
      case PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT:
        return fincodeHtInsuranceCard;
      case PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT:
        return fincodeHtFreeCard;
      default:
        return fincodeInsuranceCard;
    }
  }

  async getCustomerPaymentDetail(customerId: number) {
    const customerPayments = await this.customerPayment
      .query()
      .where('customerId', customerId)
      .andWhere('isDeleted', IS_DELETED.FALSE);

    const insurancePlatformPayment = customerPayments.find(
      (payment) =>
        payment.platformId === PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
    );

    const freePlatformPayment = customerPayments.find(
      (payment) =>
        payment.platformId === PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
    );

    const insuranceHtPlatformPayment = customerPayments.find(
      (payment) =>
        payment.platformId === PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
    );

    const freeHtPlatformPayment = customerPayments.find(
      (payment) =>
        payment.platformId === PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
    );

    return {
      insurancePlatformPayment,
      freePlatformPayment,
      insuranceHtPlatformPayment,
      freeHtPlatformPayment,
    };
  }

  async getCustomerPayment(
    customerId: number,
  ): Promise<CustomerPaymentResponse | null> {
    const {
      insurancePlatformPayment,
      freePlatformPayment,
      insuranceHtPlatformPayment,
      freeHtPlatformPayment,
    } = await this.getCustomerPaymentDetail(customerId);

    if (
      insurancePlatformPayment &&
      freePlatformPayment &&
      insuranceHtPlatformPayment &&
      freeHtPlatformPayment
    ) {
      return {
        insurancePlatformPayment,
        freePlatformPayment,
        insuranceHtPlatformPayment,
        freeHtPlatformPayment,
      };
    }

    return null;
  }

  async deleteCustomer(
    fincodeCustomerId: string,
    platformId: PAYMENT_PLATFORM,
  ): Promise<DeleteCustomerResponse> {
    const httpRequest = this.getHttpRequestByPlatformId(platformId);
    const response = await deleteCustomer(httpRequest, fincodeCustomerId);
    return {
      success: response.delete_flag === '1',
    };
  }

  async getCardsByFincodeCustomerId(
    fincodeCustomerId: string,
    platformId: PAYMENT_PLATFORM,
  ): Promise<CardInfo[]> {
    const httpRequest = this.getHttpRequestByPlatformId(platformId);
    return await getCardsByCustomerId(httpRequest, fincodeCustomerId);
  }

  async deleteCardByFincodeCustomerId(
    fincodeCustomerId: string,
    cardId: string,
    platformId: PAYMENT_PLATFORM,
  ): Promise<DeleteCardResponse> {
    const httpRequest = this.getHttpRequestByPlatformId(platformId);
    const response = await deleteCustomersCustomerIdCardsId(
      httpRequest,
      fincodeCustomerId,
      cardId,
    );

    return {
      deleteFlag: response.delete_flag,
    };
  }

  private getHttpRequestByPlatformId(
    platformId: PAYMENT_PLATFORM,
  ): AxiosInstance {
    switch (platformId) {
      case PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT:
        return this._httpInsuranceRequest;
      case PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT:
        return this._httpFreeRequest;
      case PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT:
        return this._httpInsuranceHtRequest;
      case PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT:
        return this._httpFreeHtRequest;
      default:
        return this._httpInsuranceRequest;
    }
  }

  async registerCard(
    customerId: number,
    input: RegisterCardInput,
  ): Promise<RegisterCardResponse> {
    const {
      insuranceToken,
      freeToken,
      htInsuranceToken,
      htFreeToken,
      actionBeforeRegister,
    } = input;
    const customerPayment = await this.getCustomerPayment(customerId);
    if (!customerPayment) {
      throw new ClinicError(ERROR_TYPE.NOT_FOUND);
    }

    const key = `${uuidv4()}${customerId}`;

    let registerInsuranceCardRes: RegisterCardSuccess;
    let registerFreeCardRes: RegisterCardSuccess;
    let registerHtInsuranceCardRes: RegisterCardSuccess;
    let registerHtFreeCardRes: RegisterCardSuccess;

    // Register insurance card
    try {
      registerInsuranceCardRes = await registerCard({
        axiosService: this._httpInsuranceRequest,
        customerId: customerPayment.insurancePlatformPayment.fincodeCustomerId,
        token: insuranceToken,
        shouldVerifyCard: false,
      });

      if (registerInsuranceCardRes.status !== REGISTER_CARD_STATUS.ACTIVATED) {
        await this.deleteFincodeCardsIfExist(
          customerPayment.insurancePlatformPayment.fincodeCustomerId,
          [registerInsuranceCardRes.id],
          PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
        );

        this.logger.log(
          `Insurance card status is not expected: ${registerInsuranceCardRes.status}`,
        );
        throw new ClinicError(ERROR_TYPE.CARD_STATUS_NOT_EXPECTED);
      }
    } catch (err) {
      if (err instanceof ClinicError) {
        throw err;
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errData = (err as any).response?.data;

      // If error from fincode, throw error message
      if (isFincodeError(errData)) {
        throw new ClinicError(errData.errors[0].error_message);
      }

      throw new ClinicError(ERROR_TYPE.INTERNAL_SERVER_ERROR);
    }

    // Register free card
    try {
      registerFreeCardRes = await registerCard({
        axiosService: this._httpFreeRequest,
        customerId: customerPayment.freePlatformPayment.fincodeCustomerId,
        token: freeToken,
        shouldVerifyCard: false,
      });

      if (registerFreeCardRes.status !== REGISTER_CARD_STATUS.ACTIVATED) {
        this.logger.log(
          `Free card status is not expected: ${registerFreeCardRes.status}`,
        );

        // delete insurance card if register free card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.insurancePlatformPayment.fincodeCustomerId,
          [registerInsuranceCardRes.id],
          PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
        );

        // delete free card if register free card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.freePlatformPayment.fincodeCustomerId,
          [registerFreeCardRes.id],
          PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
        );
        throw new ClinicError(ERROR_TYPE.CARD_STATUS_NOT_EXPECTED);
      }
    } catch (err) {
      if (err instanceof ClinicError) {
        throw err;
      }

      // delete insurance card if register free card failed
      await this.deleteFincodeCardsIfExist(
        customerPayment.insurancePlatformPayment.fincodeCustomerId,
        [registerInsuranceCardRes.id],
        PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
      );

      // If error from fincode, throw error message
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errData = (err as any).response?.data;
      if (isFincodeError(errData)) {
        throw new ClinicError(errData.errors[0].error_message);
      }

      throw new ClinicError(ERROR_TYPE.INTERNAL_SERVER_ERROR);
    }

    // Register ht insurance card
    try {
      registerHtInsuranceCardRes = await registerCard({
        axiosService: this._httpInsuranceHtRequest,
        customerId:
          customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
        token: htInsuranceToken,
        successReturnUrl: `${process.env.API_URL}/fincode/insurance/success?key=${key}`,
        failedReturnUrl: `${process.env.API_URL}/fincode/insurance/failed?key=${key}`,
        shouldVerifyCard: true,
      });

      if (
        registerHtInsuranceCardRes.status !== REGISTER_CARD_STATUS.ACTIVATED &&
        registerHtInsuranceCardRes.status !==
          REGISTER_CARD_STATUS.AWAITING_CUSTOMER_ACTION
      ) {
        // delete insurance card if register ht insurance card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.insurancePlatformPayment.fincodeCustomerId,
          [registerInsuranceCardRes.id],
          PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
        );

        // delete free card if register ht insurance card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.freePlatformPayment.fincodeCustomerId,
          [registerFreeCardRes.id],
          PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
        );

        // delete ht insurance card if register ht insurance card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
          [registerHtInsuranceCardRes.id],
          PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
        );

        this.logger.log(
          `Ht insurance card status is not expected: ${registerHtInsuranceCardRes.status}`,
        );
        throw new ClinicError(ERROR_TYPE.CARD_STATUS_NOT_EXPECTED);
      }
    } catch (err) {
      if (err instanceof ClinicError) {
        throw err;
      }

      // delete insurance card if register ht insurance card failed
      await this.deleteFincodeCardsIfExist(
        customerPayment.insurancePlatformPayment.fincodeCustomerId,
        [registerInsuranceCardRes.id],
        PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
      );

      // delete free card if register ht insurance card failed
      await this.deleteFincodeCardsIfExist(
        customerPayment.freePlatformPayment.fincodeCustomerId,
        [registerFreeCardRes.id],
        PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
      );

      // If error from fincode, throw error message
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errData = (err as any).response?.data;
      if (isFincodeError(errData)) {
        throw new ClinicError(errData.errors[0].error_message);
      }

      throw new ClinicError(ERROR_TYPE.INTERNAL_SERVER_ERROR);
    }

    // Register ht free card
    try {
      registerHtFreeCardRes = await registerCard({
        axiosService: this._httpFreeHtRequest,
        customerId: customerPayment.freeHtPlatformPayment.fincodeCustomerId,
        token: htFreeToken,
        successReturnUrl: `${process.env.API_URL}/fincode/free/success?key=${key}`,
        failedReturnUrl: `${process.env.API_URL}/fincode/free/failed?key=${key}`,
        shouldVerifyCard: true,
      });

      if (
        registerHtFreeCardRes.status !== REGISTER_CARD_STATUS.ACTIVATED &&
        registerHtFreeCardRes.status !==
          REGISTER_CARD_STATUS.AWAITING_CUSTOMER_ACTION
      ) {
        // delete insurance card if register ht free card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.insurancePlatformPayment.fincodeCustomerId,
          [registerInsuranceCardRes.id],
          PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
        );

        // delete free card if register ht free card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.freePlatformPayment.fincodeCustomerId,
          [registerFreeCardRes.id],
          PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
        );

        // delete ht insurance card if register ht free card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
          [registerHtInsuranceCardRes.id],
          PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
        );

        // delete ht free card if register ht free card failed
        await this.deleteFincodeCardsIfExist(
          customerPayment.freeHtPlatformPayment.fincodeCustomerId,
          [registerHtFreeCardRes.id],
          PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
        );

        this.logger.log(
          `Ht free card status is not expected: ${registerHtFreeCardRes.status}`,
        );
        throw new ClinicError(ERROR_TYPE.CARD_STATUS_NOT_EXPECTED);
      }
    } catch (err) {
      if (err instanceof ClinicError) {
        throw err;
      }

      // delete insurance card if register ht free card failed
      await this.deleteFincodeCardsIfExist(
        customerPayment.insurancePlatformPayment.fincodeCustomerId,
        [registerInsuranceCardRes.id],
        PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
      );

      // delete free card if register ht free card failed
      await this.deleteFincodeCardsIfExist(
        customerPayment.freePlatformPayment.fincodeCustomerId,
        [registerFreeCardRes.id],
        PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
      );

      // delete ht insurance card if register ht free card failed
      await this.deleteFincodeCardsIfExist(
        customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
        [registerHtInsuranceCardRes.id],
        PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
      );

      // If error from fincode, throw error message
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const errData = (err as any).response?.data;
      if (isFincodeError(errData)) {
        throw new ClinicError(errData.errors[0].error_message);
      }

      throw new ClinicError(ERROR_TYPE.INTERNAL_SERVER_ERROR);
    }

    if (
      registerInsuranceCardRes &&
      registerHtInsuranceCardRes &&
      registerFreeCardRes &&
      registerHtFreeCardRes
    ) {
      const insertCardResult = await this.customerPaymentCard.query().insert({
        customerId,
        fincodeInsuranceCardId: registerInsuranceCardRes.id,
        fincodeFreeCardId: registerFreeCardRes.id,
        fincodeHtInsuranceCardId: registerHtInsuranceCardRes.id,
        fincodeHtFreeCardId: registerHtFreeCardRes.id,
      });

      await this.redisService.setRegisterCardState(key, {
        customerId,
        cardId: insertCardResult.cardId,
        fincodeInsuranceCard: registerInsuranceCardRes,
        fincodeFreeCard: registerFreeCardRes,
        fincodeHtInsuranceCard: registerHtInsuranceCardRes,
        fincodeHtFreeCard: registerHtFreeCardRes,
        actionBeforeRegister,
      });
    }

    let redirectUrl = '';

    if (
      registerHtFreeCardRes.status ===
        REGISTER_CARD_STATUS.AWAITING_CUSTOMER_ACTION &&
      registerHtFreeCardRes.redirect_url
    ) {
      redirectUrl = registerHtFreeCardRes.redirect_url;
    }

    if (
      registerHtInsuranceCardRes.status ===
        REGISTER_CARD_STATUS.AWAITING_CUSTOMER_ACTION &&
      registerHtInsuranceCardRes.redirect_url
    ) {
      redirectUrl = registerHtInsuranceCardRes.redirect_url;
    }

    return {
      redirectUrl,
    };
  }

  async handleAddInsuranceCardSuccess(key: string) {
    let state: RegisterCardState | null = null;
    try {
      state = await this.redisService.getRegisterCardState(key);
    } catch (err) {
      this.logger.error('Failed to get register card state', err);
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=0`,
      };
    }
    if (!state) {
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=0`,
      };
    }

    try {
      await execWithTx(async (tx) => {
        await this.customerPaymentCard
          .query(tx)
          .patch({
            isInsurance3dsVerified: true,
          })
          .where('cardId', state.cardId);

        if (state.fincodeHtFreeCard.status === REGISTER_CARD_STATUS.ACTIVATED) {
          await this.updateDataAfterChangeCard(
            tx,
            state.customerId,
            state.cardId,
          );
        }
      });

      if (
        state.fincodeHtFreeCard.status ===
        REGISTER_CARD_STATUS.AWAITING_CUSTOMER_ACTION
      ) {
        return { url: state.fincodeHtFreeCard.redirect_url };
      }

      await this.redisService.removeRegisterCardState(key);
      return {
        url: `${process.env.APP_URL}/credit-card/success?action=${state.actionBeforeRegister}`,
      };
    } catch (err) {
      this.logger.error('Failed to handle add insurance card success', err);
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=${state.actionBeforeRegister}`,
      };
    }
  }

  async handleAddCardFailed(key: string) {
    let state: RegisterCardState | null = null;
    try {
      state = await this.redisService.getRegisterCardState(key);
    } catch (err) {
      this.logger.error('Failed to get register card state', err);
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=0`,
      };
    }

    if (!state) {
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=0`,
      };
    }

    try {
      await this.deleteFincodeCardsIfExist(
        state.fincodeInsuranceCard.customer_id,
        [state.fincodeInsuranceCard.id],
        PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
      );

      await this.deleteFincodeCardsIfExist(
        state.fincodeFreeCard.customer_id,
        [state.fincodeFreeCard.id],
        PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
      );

      await this.deleteFincodeCardsIfExist(
        state.fincodeHtInsuranceCard.customer_id,
        [state.fincodeHtInsuranceCard.id],
        PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
      );

      await this.deleteFincodeCardsIfExist(
        state.fincodeHtFreeCard.customer_id,
        [state.fincodeHtFreeCard.id],
        PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
      );

      await this.customerPaymentCard
        .query()
        .patch({
          isDeleted: IS_DELETED.TRUE,
        })
        .where('cardId', state.cardId);

      await this.redisService.removeRegisterCardState(key);

      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=${state.actionBeforeRegister}`,
      };
    } catch (err) {
      this.logger.error('Failed to handle add card failed', err);
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=${state.actionBeforeRegister}`,
      };
    }
  }

  async handleAddFreeCardSuccess(key: string) {
    let state: RegisterCardState | null = null;
    try {
      state = await this.redisService.getRegisterCardState(key);
    } catch (err) {
      this.logger.error('Failed to get register card state', err);
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=0`,
      };
    }

    if (!state) {
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=0`,
      };
    }

    try {
      await execWithTx(async (tx) => {
        await this.customerPaymentCard
          .query(tx)
          .patch({
            isFree3dsVerified: true,
          })
          .where('cardId', state.cardId);

        await this.updateDataAfterChangeCard(
          tx,
          state.customerId,
          state.cardId,
        );
      });

      await this.redisService.removeRegisterCardState(key);

      return {
        url: `${process.env.APP_URL}/credit-card/success?action=${state.actionBeforeRegister}`,
      };
    } catch (err) {
      this.logger.error('Failed to handle add free card success', err);
      return {
        url: `${process.env.APP_URL}/credit-card/failed?action=${state.actionBeforeRegister}`,
      };
    }
  }

  async handleDeleteNotVerifiedCard(
    customerId: number,
    customerPayment: CustomerPaymentResponse,
  ) {
    const dbCardList = await this.customerPaymentCard.query().where({
      customerId,
      isDeleted: IS_DELETED.FALSE,
    });

    const fincodeInsuranceCardList = await this.getCardsByFincodeCustomerId(
      customerPayment.insurancePlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
    );

    const fincodeFreeCardList = await this.getCardsByFincodeCustomerId(
      customerPayment.freePlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
    );

    const fincodeInsuranceHtCardList = await this.getCardsByFincodeCustomerId(
      customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
    );

    const fincodeFreeHtCardList = await this.getCardsByFincodeCustomerId(
      customerPayment.freeHtPlatformPayment.fincodeCustomerId,
      PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
    );

    const {
      shouldDeleteFincodeCardIds: shouldDeleteFincodeInsuranceCardIds,
      shouldDeleteDbCardIds: shouldDeleteInsuranceDbCardIds,
    } = await this.getShouldDeleteFincodeCards(
      dbCardList,
      fincodeInsuranceCardList,
      PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
    );

    const {
      shouldDeleteFincodeCardIds: shouldDeleteFincodeFreeCardIds,
      shouldDeleteDbCardIds: shouldDeleteFreeDbCardIds,
    } = await this.getShouldDeleteFincodeCards(
      dbCardList,
      fincodeFreeCardList,
      PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
    );

    const {
      shouldDeleteFincodeCardIds: shouldDeleteFincodeHtInsuranceCardIds,
      shouldDeleteDbCardIds: shouldDeleteInsuranceHtDbCardIds,
    } = await this.getShouldDeleteFincodeCards(
      dbCardList,
      fincodeInsuranceHtCardList,
      PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
    );

    const {
      shouldDeleteFincodeCardIds: shouldDeleteFincodeHtFreeCardIds,
      shouldDeleteDbCardIds: shouldDeleteFreeHtDbCardIds,
    } = await this.getShouldDeleteFincodeCards(
      dbCardList,
      fincodeFreeHtCardList,
      PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
    );

    const shouldDeleteDBCardIds = [
      ...shouldDeleteInsuranceDbCardIds,
      ...shouldDeleteFreeDbCardIds,
      ...shouldDeleteInsuranceHtDbCardIds,
      ...shouldDeleteFreeHtDbCardIds,
    ];

    if (shouldDeleteFincodeInsuranceCardIds.length) {
      await this.deleteFincodeCardsIfExist(
        customerPayment.insurancePlatformPayment.fincodeCustomerId,
        shouldDeleteFincodeInsuranceCardIds,
        PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT,
      );
    }

    if (shouldDeleteFincodeFreeCardIds.length) {
      await this.deleteFincodeCardsIfExist(
        customerPayment.freePlatformPayment.fincodeCustomerId,
        shouldDeleteFincodeFreeCardIds,
        PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT,
      );
    }

    if (shouldDeleteFincodeHtInsuranceCardIds.length) {
      await this.deleteFincodeCardsIfExist(
        customerPayment.insuranceHtPlatformPayment.fincodeCustomerId,
        shouldDeleteFincodeHtInsuranceCardIds,
        PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT,
      );
    }

    if (shouldDeleteFincodeHtFreeCardIds.length) {
      await this.deleteFincodeCardsIfExist(
        customerPayment.freeHtPlatformPayment.fincodeCustomerId,
        shouldDeleteFincodeHtFreeCardIds,
        PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT,
      );
    }

    if (shouldDeleteDBCardIds.length) {
      await this.customerPaymentCard
        .query()
        .patch({
          isDeleted: IS_DELETED.TRUE,
        })
        .where('cardId', 'in', shouldDeleteDBCardIds);
    }
  }

  async getDefaultCard(
    fincodeCustomerId: string,
    platformId: PAYMENT_PLATFORM,
  ): Promise<CardInfo | undefined> {
    const httpRequest = this.getHttpRequestByPlatformId(platformId);

    const cardList = await getCardsByCustomerId(httpRequest, fincodeCustomerId);

    return cardList.find((card) => card.default_flag === DEFAULT_CARD_FLAG);
  }

  async deleteFincodeCardsIfExist(
    fincodeCustomerId: string,
    cardIds: string[],
    platformId: PAYMENT_PLATFORM,
  ) {
    const httpRequest = this.getHttpRequestByPlatformId(platformId);

    const fincodeCardList = await getCardsByCustomerId(
      httpRequest,
      fincodeCustomerId,
    );

    const shouldDeleteCardIds = fincodeCardList.filter((card) =>
      cardIds.includes(card.id),
    );

    if (shouldDeleteCardIds.length) {
      await Promise.all(
        shouldDeleteCardIds.map((card) =>
          deleteCustomersCustomerIdCardsId(
            httpRequest,
            fincodeCustomerId,
            card.id,
          ),
        ),
      );
    }
  }

  async getShouldDeleteFincodeCards(
    dbCardList: CustomerPaymentCard[],
    fincodeCardList: CardInfo[],
    platformId: PAYMENT_PLATFORM,
  ) {
    const shouldDeleteFincodeCardIds = [];
    const shouldDeleteDbCardIds = [];

    for (const card of fincodeCardList) {
      let dbCard: CustomerPaymentCard | undefined;

      switch (platformId) {
        case PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT:
          dbCard = dbCardList.find(
            (dbCard) => dbCard.fincodeInsuranceCardId === card.id,
          );
          break;
        case PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT:
          dbCard = dbCardList.find(
            (dbCard) => dbCard.fincodeFreeCardId === card.id,
          );
          break;
        case PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT:
          dbCard = dbCardList.find(
            (dbCard) => dbCard.fincodeHtInsuranceCardId === card.id,
          );
          break;
        case PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT:
          dbCard = dbCardList.find(
            (dbCard) => dbCard.fincodeHtFreeCardId === card.id,
          );
          break;
      }

      // If card exist in db but not verified, delete in db and fincode
      if (
        dbCard &&
        (!dbCard.isInsurance3dsVerified || !dbCard.isFree3dsVerified)
      ) {
        shouldDeleteFincodeCardIds.push(card.id);
        shouldDeleteDbCardIds.push(dbCard.cardId);
      }
    }

    for (const card of dbCardList) {
      let fincodeCard: CardInfo | undefined;

      switch (platformId) {
        case PAYMENT_PLATFORM.INSURANCE_MEDICAL_TREATMENT:
          fincodeCard = fincodeCardList.find(
            (fincodeCard) => fincodeCard.id === card.fincodeInsuranceCardId,
          );
          break;
        case PAYMENT_PLATFORM.FREE_MEDICAL_TREATMENT:
          fincodeCard = fincodeCardList.find(
            (fincodeCard) => fincodeCard.id === card.fincodeFreeCardId,
          );
          break;
        case PAYMENT_PLATFORM.HT_INSURANCE_MEDICAL_TREATMENT:
          fincodeCard = fincodeCardList.find(
            (fincodeCard) => fincodeCard.id === card.fincodeHtInsuranceCardId,
          );
          break;
        case PAYMENT_PLATFORM.HT_FREE_MEDICAL_TREATMENT:
          fincodeCard = fincodeCardList.find(
            (fincodeCard) => fincodeCard.id === card.fincodeHtFreeCardId,
          );
          break;
      }

      // If card exist in db but not exist in fincode, delete in db
      if (!fincodeCard) {
        shouldDeleteDbCardIds.push(card.cardId);
      }
    }

    return { shouldDeleteFincodeCardIds, shouldDeleteDbCardIds };
  }
}
