import { HttpModule } from '@nestjs/axios';
import { forwardRef, Module } from '@nestjs/common';

import { AgreeService } from '@/agree/agree.service';
import { MailService } from '@/common/service/mail.service';
import { SqsClientService } from '@/common/service/sqs-client.service';
import { TokenService } from '@/common/service/token.service';
import { PharmacyModule } from '@/pharmacy/pharmacy.module';
import { RedisService } from '@/redis/redis.service';
import { TemplateService } from '@/template/template.service';

import { FincodeController } from './fincode.controller';
import FincodeResolver from './fincode.resolver';
import { FincodeService } from './fincode.service';

@Module({
  imports: [HttpModule, forwardRef(() => PharmacyModule)],
  providers: [
    FincodeService,
    FincodeResolver,
    AgreeService,
    TokenService,
    MailService,
    TemplateService,
    FincodeResolver,
    SqsClientService,
    RedisService,
  ],
  controllers: [FincodeController],
  exports: [FincodeService],
})
export class FincodeModule {}
