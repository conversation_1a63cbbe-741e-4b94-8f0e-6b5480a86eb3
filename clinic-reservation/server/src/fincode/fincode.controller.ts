import { Controller, Post, Query, Redirect } from '@nestjs/common';

import { RedirectResponse } from './fincode.output';
import { FincodeService } from './fincode.service';

@Controller('fincode')
export class FincodeController {
  constructor(private fincodeService: FincodeService) {}

  @Post('insurance/success')
  @Redirect()
  async handleInsuranceSuccess(
    @Query() query: { key: string },
  ): Promise<RedirectResponse> {
    return this.fincodeService.handleAddInsuranceCardSuccess(query.key);
  }

  @Post('insurance/failed')
  @Redirect()
  async handleInsuranceFailed(
    @Query() query: { key: string },
  ): Promise<RedirectResponse> {
    return this.fincodeService.handleAddCardFailed(query.key);
  }

  @Post('free/success')
  @Redirect()
  async handleFreeSuccess(
    @Query() query: { key: string },
  ): Promise<RedirectResponse> {
    return this.fincodeService.handleAddFreeCardSuccess(query.key);
  }

  @Post('free/failed')
  @Redirect()
  async handleFreeFailed(
    @Query() query: { key: string },
  ): Promise<RedirectResponse> {
    return this.fincodeService.handleAddCardFailed(query.key);
  }
}
