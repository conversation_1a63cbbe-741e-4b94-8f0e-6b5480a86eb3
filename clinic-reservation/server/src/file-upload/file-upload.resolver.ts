import { UseGuards } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';

import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';

import { UploadFileInput } from './file-upload.input';
import { S3PresignedPutResponse } from './file-upload.output';
import { FileUploadService } from './file-upload.service';

@Resolver()
export class FileUploadResolver {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Query(() => S3PresignedPutResponse)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getPresignedPutUrl(
    @Args('param', { type: () => UploadFileInput })
    { fileName, fileType }: UploadFileInput,
  ): Promise<S3PresignedPutResponse> {
    return await this.fileUploadService.getPresignedPutUrl(fileName, fileType);
  }
}
