import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AgreeService } from '@/agree/agree.service';
import { TokenService } from '@/common/service/token.service';

import { FileUploadResolver } from './file-upload.resolver';
import { FileUploadService } from './file-upload.service';

@Module({
  imports: [HttpModule],
  providers: [
    FileUploadService,
    FileUploadResolver,
    AgreeService,
    TokenService,
  ],
  exports: [FileUploadService],
})
export class FileUploadModule {}
