import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

import { S3ClientService } from '@/common/service/s3-client.service';

import { S3PresignedPutResponse } from './file-upload.output';

@Injectable()
export class FileUploadService {
  private s3ClientService = new S3ClientService(
    process.env.AWS_S3_SURVEY_FILE_BUCKET || '',
  );

  async getPresignedPutUrl(
    filename: string,
    fileType: string,
  ): Promise<S3PresignedPutResponse> {
    const key = `${filename}${uuidv4()}`;

    const url = await this.s3ClientService.createSignedUrl(
      key,
      60 * 360,
      fileType,
    );

    return {
      key,
      url,
    };
  }

  async getPresignedUrl(s3Key: string): Promise<string> {
    return await this.s3ClientService.getS3ObjectURL(s3Key);
  }
}
