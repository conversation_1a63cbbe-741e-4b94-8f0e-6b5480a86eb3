import { Test, TestingModule } from '@nestjs/testing';

import { UploadFileInput } from './file-upload.input';
import { S3PresignedPutResponse } from './file-upload.output';
import { FileUploadResolver } from './file-upload.resolver';
import { FileUploadService } from './file-upload.service';

// FileUploadServiceのモック
jest.mock('./file-upload.service');

/**
 * FileUploadResolverのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. getPresignedPutUrlクエリの処理
 */
describe('FileUploadResolver', () => {
  let resolver: FileUploadResolver;
  let mockFileUploadService: jest.Mocked<FileUploadService>;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - リゾルバのインスタンス化
   */
  beforeEach(async () => {
    // テストモジュールの設定
    const module: TestingModule = await Test.createTestingModule({
      providers: [FileUploadResolver, FileUploadService],
    })
      // ガードをバイパス
      .overrideGuard(
        jest.requireActual('@/common/guards/agree/active-agree.guard')
          .ActiveAgreeGuard,
      )
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(
        jest.requireActual(
          '@/common/guards/customer/active-customer-login.guard',
        ).ActiveCustomerLoginGuard,
      )
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(
        jest.requireActual(
          '@/common/guards/customer/active-customer-login.guard',
        ).RegistrationCompletionGuard,
      )
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    resolver = module.get<FileUploadResolver>(FileUploadResolver);
    mockFileUploadService = module.get(
      FileUploadService,
    ) as jest.Mocked<FileUploadService>;

    // テスト前にモックをリセット
    jest.clearAllMocks();
  });

  // リゾルバが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  /**
   * getPresignedPutUrlメソッドのテスト
   */
  describe('getPresignedPutUrl', () => {
    /**
     * テストケース: 署名付きPUT URLを取得
     * 条件: ファイル名とファイルタイプを含む入力パラメータを指定
     * 想定結果: FileUploadServiceから返されたレスポンスがそのまま返される
     */
    it('should return presigned PUT URL from service', async () => {
      // テストデータの準備
      const input: UploadFileInput = {
        fileName: 'test-file.jpg',
        fileType: 'image/jpeg',
      };
      const expectedResponse: S3PresignedPutResponse = {
        key: 'test-file.jpg123e4567-e89b-12d3-a456-426614174000',
        url: 'https://example.com/presigned-url',
      };

      // モックの設定
      mockFileUploadService.getPresignedPutUrl.mockResolvedValue(
        expectedResponse,
      );

      // メソッドの実行
      const result = await resolver.getPresignedPutUrl(input);

      // 期待する結果の検証
      expect(result).toEqual(expectedResponse);
      expect(mockFileUploadService.getPresignedPutUrl).toHaveBeenCalledWith(
        input.fileName,
        input.fileType,
      );
    });
  });
});
