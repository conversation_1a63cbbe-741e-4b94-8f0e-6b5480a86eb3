import { Test, TestingModule } from '@nestjs/testing';
import * as uuid from 'uuid';

import { S3ClientService } from '@/common/service/s3-client.service';

import { FileUploadService } from './file-upload.service';

// uuidv4のモック
jest.mock('uuid', () => ({
  v4: jest.fn(),
}));

// S3ClientServiceのモック
jest.mock('@/common/service/s3-client.service', () => {
  return {
    S3ClientService: jest.fn().mockImplementation(() => ({
      createSignedUrl: jest.fn(),
      getS3ObjectURL: jest.fn(),
    })),
  };
});

/**
 * FileUploadServiceのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. S3へのファイルアップロード用の署名付きURLの取得
 * 2. S3からのファイル取得用の署名付きURLの取得
 */
describe('FileUploadService', () => {
  let service: FileUploadService;
  let mockS3ClientService: jest.Mocked<S3ClientService>;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - サービスのインスタンス化
   */
  beforeEach(async () => {
    // テストモジュールの設定
    const module: TestingModule = await Test.createTestingModule({
      providers: [FileUploadService],
    }).compile();

    service = module.get<FileUploadService>(FileUploadService);

    // S3ClientServiceのインスタンスを取得
    mockS3ClientService = (
      service as unknown as { s3ClientService: jest.Mocked<S3ClientService> }
    ).s3ClientService;

    // テスト前にモックをリセット
    jest.clearAllMocks();
  });

  // サービスが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * getPresignedPutUrlメソッドのテスト
   */
  describe('getPresignedPutUrl', () => {
    /**
     * テストケース: 署名付きPUT URLを取得
     * 条件: ファイル名とファイルタイプを指定
     * 想定結果: キーとURLを含むレスポンスが返される
     */
    it('should return presigned PUT URL', async () => {
      // テストデータの準備
      const filename = 'test-file.jpg';
      const fileType = 'image/jpeg';
      const mockUuid = '123e4567-e89b-12d3-a456-************';
      const mockUrl = 'https://example.com/presigned-url';
      const expectedKey = `${filename}${mockUuid}`;

      // モックの設定
      (uuid.v4 as jest.Mock).mockReturnValue(mockUuid);
      mockS3ClientService.createSignedUrl.mockResolvedValue(mockUrl);

      // メソッドの実行
      const result = await service.getPresignedPutUrl(filename, fileType);

      // 期待する結果の検証
      expect(result).toEqual({
        key: expectedKey,
        url: mockUrl,
      });
      expect(uuid.v4).toHaveBeenCalled();
      expect(mockS3ClientService.createSignedUrl).toHaveBeenCalledWith(
        expectedKey,
        60 * 360,
        fileType,
      );
    });
  });

  /**
   * getPresignedUrlメソッドのテスト
   */
  describe('getPresignedUrl', () => {
    /**
     * テストケース: 署名付きGET URLを取得
     * 条件: S3キーを指定
     * 想定結果: 署名付きURLが返される
     */
    it('should return presigned GET URL', async () => {
      // テストデータの準備
      const s3Key = 'test-file.jpg123e4567-e89b-12d3-a456-************';
      const mockUrl = 'https://example.com/get-presigned-url';

      // モックの設定
      (mockS3ClientService.getS3ObjectURL as jest.Mock).mockResolvedValue(
        mockUrl,
      );

      // メソッドの実行
      const result = await service.getPresignedUrl(s3Key);

      // 期待する結果の検証
      expect(result).toBe(mockUrl);
      expect(mockS3ClientService.getS3ObjectURL).toHaveBeenCalledWith(s3Key);
    });
  });
});
