import { CreateMeetingWithAttendeesCommandOutput } from '@aws-sdk/client-chime-sdk-meetings';
import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';

import { ERROR_TYPE } from '@/common/constants/error-type';
import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { CustomerService } from '@/customer/customer.service';
import { Meeting } from '@/models/Meeting';
import { RedisPubSubService } from '@/redis/redis.pubsub.service';

import { LoginCustomer } from '../customer/customer.output';
import MeetingService from './meeting.service';

@Controller('meetings')
export default class MeetingController {
  constructor(
    private readonly customerService: CustomerService,
    private readonly meetingService: MeetingService,
    private readonly redisPubSub: RedisPubSubService,
  ) {}

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Get(':id')
  async getMeeting(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Param('id') id: number,
  ): Promise<CreateMeetingWithAttendeesCommandOutput> {
    const meeting = await this.meetingService.getMeetingDetail(id);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new ForbiddenException(ERROR_TYPE.PERMISSION_DENIED);
    }

    return await this.meetingService.getMeeting(Number(id));
  }

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Post('join')
  async joinMeeting(
    @Body('meetingId') meetingId: number,
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<CreateMeetingWithAttendeesCommandOutput> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new ForbiddenException(ERROR_TYPE.PERMISSION_DENIED);
    }

    return await this.meetingService.joinMeeting(meetingId, loginCustomer);
  }

  @HttpCode(HttpStatus.ACCEPTED)
  @Post('notify')
  async notifyUpdatedMeetingStatus(
    @Body('meetingId') meetingId: number,
  ): Promise<void> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);

    await this.redisPubSub.publish('meetingPatientUpdated', {
      meetingPatientUpdated: meeting,
    });
  }

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Post('leave')
  async leaveMeeting(
    @Body('meetingId') meetingId: number,
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<void> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new ForbiddenException(ERROR_TYPE.PERMISSION_DENIED);
    }

    await this.meetingService.leaveMeeting(loginCustomer, meetingId);
  }

  private async checkPermission(
    meeting: Meeting,
    loginCustomer: LoginCustomer,
  ): Promise<boolean> {
    const hasPermission =
      meeting.patient.portalCustomer &&
      (await this.customerService.validateCustomersIsFamilyMember(
        loginCustomer,
        [meeting.patient.portalCustomer.customerId],
      ));

    return hasPermission;
  }
}
