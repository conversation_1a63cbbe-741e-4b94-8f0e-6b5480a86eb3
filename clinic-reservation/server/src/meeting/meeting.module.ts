import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AgreeService } from '@/agree/agree.service';
import { TokenService } from '@/common/service/token.service';
import { RedisPubSubService } from '@/redis/redis.pubsub.service';

import { CustomerModule } from '../customer/customer.module';
import MeetingController from './meeting.controller';
import MeetingResolver from './meeting.resolver';
import MeetingService from './meeting.service';

@Module({
  imports: [CustomerModule, HttpModule],
  controllers: [MeetingController],
  providers: [
    MeetingService,
    MeetingResolver,
    RedisPubSubService,
    AgreeService,
    TokenService,
  ],
})
export class MeetingModule {}
