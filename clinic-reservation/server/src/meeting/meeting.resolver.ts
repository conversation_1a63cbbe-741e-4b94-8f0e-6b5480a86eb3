import { HttpStatus, UseGuards } from '@nestjs/common';
import {
  Args,
  Int,
  Mutation,
  Query,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { GraphQLError } from 'graphql';

import { ERROR_TYPE } from '@/common/constants/error-type';
import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { LoginCustomer } from '@/customer/customer.output';
import { CustomerService } from '@/customer/customer.service';
import { Meeting } from '@/models/Meeting';
import { RedisPubSubService } from '@/redis/redis.pubsub.service';

import { UpdateReservationMeetingStatusRequest } from './meeting.input';
import MeetingService from './meeting.service';

@Resolver()
export default class MeetingResolver {
  constructor(
    private readonly customerService: CustomerService,
    private readonly meetingService: MeetingService,
    private readonly redisPubSub: RedisPubSubService,
  ) {}

  @Query(() => Meeting)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getMeetingDetail(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('meetingId', { type: () => Int }) meetingId: number,
  ): Promise<Meeting> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: { code: ERROR_TYPE.PERMISSION_DENIED },
      });
    }

    return meeting;
  }

  @Mutation(() => Meeting)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async updateReservationMeetingStatus(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('input', { type: () => UpdateReservationMeetingStatusRequest })
    { meetingId, status }: UpdateReservationMeetingStatusRequest,
  ): Promise<Meeting> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: { code: ERROR_TYPE.PERMISSION_DENIED },
      });
    }

    const result = await this.meetingService.updateMeetingStatus(
      meetingId,
      status,
    );

    await this.redisPubSub.publish('meetingPatientUpdated', {
      meetingPatientUpdated: result,
    });

    return result;
  }

  @Mutation(() => Int)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async notifyMeetingStatusUpdated(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('meetingId', { type: () => Int }) meetingId: number,
  ): Promise<number> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: { code: ERROR_TYPE.PERMISSION_DENIED },
      });
    }

    await this.redisPubSub.publish('meetingPatientUpdated', {
      meetingPatientUpdated: meeting,
    });

    return HttpStatus.ACCEPTED;
  }

  @Mutation(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async notifyUpdatedMeetingToDenkaru(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('meetingId', { type: () => Int }) meetingId: number,
  ): Promise<boolean> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: { code: ERROR_TYPE.PERMISSION_DENIED },
      });
    }

    await this.meetingService.notifyUpdatedMeetingToDenkaru(meetingId);
    return true;
  }

  @Subscription(() => Meeting, {
    name: 'meetingPatientUpdated',
    filter: (payload, variables) =>
      payload.meetingPatientUpdated.meetingId === variables.meetingId,
  })
  subscribeToMeetingStatusUpdated(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    @Args('meetingId', { type: () => Int }) _meetingId: number,
  ): AsyncIterator<Meeting, Meeting, undefined> {
    return this.redisPubSub.asyncIterator<Meeting>('meetingPatientUpdated');
  }

  @Mutation(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async startMeetingTranscription(
    @Args('meetingId', { type: () => Int }) meetingId: number,
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<boolean> {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: { code: ERROR_TYPE.PERMISSION_DENIED },
      });
    }

    await this.meetingService.startMeetingTranscription(meetingId);
    return true;
  }

  @Mutation(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async createMediaCapturePipeline(
    @Args('meetingId', { type: () => Int }) meetingId: number,
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ) {
    const meeting = await this.meetingService.getMeetingDetail(meetingId);
    const hasPermission = await this.checkPermission(meeting, loginCustomer);
    if (!hasPermission) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: { code: ERROR_TYPE.PERMISSION_DENIED },
      });
    }

    await this.meetingService.createMediaCapturePipeline(meetingId);
    return true;
  }

  private async checkPermission(
    meeting: Meeting,
    loginCustomer: LoginCustomer,
  ): Promise<boolean> {
    const hasPermission =
      meeting.patient.portalCustomer &&
      (await this.customerService.validateCustomersIsFamilyMember(
        loginCustomer,
        [meeting.patient.portalCustomer.customerId],
      ));

    return hasPermission;
  }
}
