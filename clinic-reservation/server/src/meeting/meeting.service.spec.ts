import { ChimeSDKMeetings } from '@aws-sdk/client-chime-sdk-meetings';
import { HttpService } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { ModelClass } from 'objection';
import { lastValueFrom } from 'rxjs';

import { MEETING_STATUS } from '@/common/constants/master-type';
import { Customer } from '@/models/Customer';
import { Meeting } from '@/models/Meeting';

import MeetingService from './meeting.service';

// AWS Chime SDKのモック
jest.mock('@aws-sdk/client-chime-sdk-meetings', () => ({
  ChimeSDKMeetings: jest.fn().mockImplementation(() => ({
    getMeeting: jest.fn(),
    listAttendees: jest.fn(),
    createAttendee: jest.fn(),
  })),
}));

// モック用のデータ
const mockMeeting = {
  meetingId: 1,
  chimeMeetingId: 'test-chime-meeting-id',
  hospitalId: 1,
  patientId: 1,
  status: MEETING_STATUS.RESERVED,
  isBothJoined: 0,
  isDeleted: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'test',
  updatedBy: 'test',
  $query: jest.fn().mockReturnThis(),
  $set: jest.fn().mockReturnThis(),
  $patch: jest.fn().mockReturnThis(),
  $patchAndFetch: jest.fn().mockReturnThis(),
};

// lastValueFromのモック
jest.mock('rxjs', () => {
  const originalModule = jest.requireActual('rxjs');
  return {
    ...originalModule,
    lastValueFrom: jest.fn().mockImplementation((observable) => observable),
  };
});

/**
 * MeetingServiceのユニットテスト
 */
describe('MeetingService', () => {
  let service: MeetingService;
  let meetingModel: ModelClass<Meeting>;
  let _httpService: HttpService;

  // モックオブジェクト
  const mockQuery = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    findById: jest.fn().mockReturnThis(),
    first: jest.fn().mockReturnThis(),
    withGraphFetched: jest.fn().mockReturnThis(),
    modifyGraph: jest.fn().mockReturnThis(),
    patch: jest.fn().mockReturnThis(),
    patchAndFetch: jest.fn().mockReturnThis(),
    leftJoinRelated: jest.fn().mockReturnThis(),
    throwIfNotFound: jest.fn().mockResolvedValue(mockMeeting),
  };

  // モックトランザクション
  const mockTrx = {
    commit: jest.fn().mockResolvedValue(null),
    rollback: jest.fn().mockResolvedValue(null),
  };

  // テスト前の準備
  beforeEach(async () => {
    // モックの設定
    jest.clearAllMocks();

    // Meeting.startTransactionのモック
    Meeting.startTransaction = jest.fn().mockResolvedValue(mockTrx);

    // AWS Chime SDKのモックを初期化
    const mockChime = {
      getMeeting: jest.fn(),
      listAttendees: jest.fn(),
      createAttendee: jest.fn(),
    };

    // テストモジュールの設定
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MeetingService,
        {
          provide: Meeting.name,
          useValue: {
            query: jest.fn().mockReturnValue(mockQuery),
          },
        },
        {
          provide: HttpService,
          useValue: {
            post: jest.fn(),
            axiosRef: {
              post: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    // サービスとモデルの取得
    service = module.get<MeetingService>(MeetingService);
    meetingModel = module.get<ModelClass<Meeting>>(Meeting.name);
    _httpService = module.get<HttpService>(HttpService);

    // AWS Chime SDKのモックを設定
    service['chime'] = mockChime as unknown as jest.Mocked<ChimeSDKMeetings>;
  });

  // サービスの初期化テスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // getChimeMeetingのテスト
  describe('getChimeMeeting', () => {
    it('should return a meeting by id', async () => {
      // throwIfNotFound メソッドが正しく呼び出されるようにモック設定
      mockQuery.throwIfNotFound.mockResolvedValue(mockMeeting);

      const result = await service['getChimeMeeting'](1);

      expect(meetingModel.query).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalledWith('isDeleted', 0);
      expect(mockQuery.findById).toHaveBeenCalledWith(1);
      expect(mockQuery.throwIfNotFound).toHaveBeenCalled();
      expect(result).toEqual(mockMeeting);
    });
  });

  // updateMeetingStatusのテスト
  describe('updateMeetingStatus', () => {
    it('should update meeting status successfully', async () => {
      // 必要なモックオブジェクトを作成
      const mockMeetingWithPharmacy = {
        ...mockMeeting,
        pharmacyReserve: {
          createdBy: 'test',
          $query: jest.fn().mockReturnValue({
            patch: jest.fn().mockResolvedValue(null),
          }),
        },
        $query: jest.fn().mockReturnValue({
          patchAndFetch: jest.fn().mockResolvedValue({
            ...mockMeeting,
            status: MEETING_STATUS.PATIENT_JOINED,
          }),
        }),
      };

      // first().throwIfNotFoundのモックを設定
      mockQuery.first.mockReturnValue({
        throwIfNotFound: jest.fn().mockResolvedValue(mockMeetingWithPharmacy),
      });

      // メソッド実行
      const result = await service.updateMeetingStatus(
        1,
        MEETING_STATUS.PATIENT_JOINED,
      );

      // 検証
      expect(meetingModel.query).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalledWith('meetingId', 1);
      expect(mockQuery.andWhere).toHaveBeenCalledWith('isDeleted', 0);
      expect(mockQuery.withGraphFetched).toHaveBeenCalledWith({
        pharmacyReserve: true,
      });
      expect(mockTrx.commit).toHaveBeenCalled();
      expect(result).toEqual({
        ...mockMeeting,
        status: MEETING_STATUS.PATIENT_JOINED,
      });
    });

    it('should handle errors and rollback transaction', async () => {
      // first().throwIfNotFoundのモックを設定し、エラーをスローするようにする
      mockQuery.first.mockReturnValue({
        throwIfNotFound: jest.fn().mockImplementation(() => {
          throw new Error('テストエラー');
        }),
      });

      // メソッド実行と検証
      await expect(
        service.updateMeetingStatus(1, MEETING_STATUS.PATIENT_JOINED),
      ).rejects.toThrow('テストエラー');
      expect(mockTrx.rollback).not.toHaveBeenCalled(); // エラーが早い段階で発生するためロールバックは呼ばれない
    });
  });

  // getMeetingDetailのテスト
  describe('getMeetingDetail', () => {
    it('should return meeting details', async () => {
      // first().throwIfNotFoundのモックを設定
      mockQuery.first.mockReturnValue({
        throwIfNotFound: jest.fn().mockResolvedValue(mockMeeting),
      });

      // メソッド実行
      const result = await service.getMeetingDetail(1);

      // 検証
      expect(meetingModel.query).toHaveBeenCalled();
      expect(mockQuery.where).toHaveBeenCalledWith('meetingId', 1);
      expect(mockQuery.andWhere).toHaveBeenCalledWith('meeting.isDeleted', 0);
      expect(mockQuery.withGraphFetched).toHaveBeenCalled();
      expect(result).toEqual(mockMeeting);
    });
  });

  // getMeetingのテスト
  describe('getMeeting', () => {
    it('should return meeting details with attendees', async () => {
      // モックの設定
      const mockChimeMeeting = {
        Meeting: {
          MeetingId: 'test-chime-meeting-id',
          MeetingArn: 'test-arn',
        },
      };

      const mockAttendees = {
        Attendees: [
          {
            AttendeeId: 'test-attendee-id',
            ExternalUserId: 'test-user',
          },
        ],
      };

      // chime.getMeetingのモック
      service['chime'].getMeeting = jest
        .fn()
        .mockResolvedValue(mockChimeMeeting);
      // chime.listAttendeesのモック
      service['chime'].listAttendees = jest
        .fn()
        .mockResolvedValue(mockAttendees);

      // メソッド実行
      const result = await service.getMeeting(1);

      // 検証
      expect(service['chime'].getMeeting).toHaveBeenCalledWith({
        MeetingId: mockMeeting.chimeMeetingId,
      });
      expect(service['chime'].listAttendees).toHaveBeenCalledWith({
        MeetingId: mockChimeMeeting.Meeting.MeetingId,
      });
      expect(result).toEqual({
        ...mockChimeMeeting,
        Attendees: mockAttendees.Attendees,
      });
    });

    it('should throw NotFoundException when meeting has no chimeMeetingId', async () => {
      // chimeMeetingIdがないミーティングのモック
      const mockMeetingWithoutChimeId = {
        ...mockMeeting,
        chimeMeetingId: null,
      };

      // throwIfNotFound メソッドが正しく呼び出されるようにモック設定
      mockQuery.throwIfNotFound.mockResolvedValue(mockMeetingWithoutChimeId);

      // メソッド実行と検証
      await expect(service.getMeeting(1)).rejects.toThrow('Not Found');
    });
  });

  // joinMeetingのテスト
  describe('joinMeeting', () => {
    const mockCustomer: Customer = {
      customerId: 1,
      name: 'Test Customer',
      kanaName: 'テストカスタマー',
      birthday: new Date(),
      gender: 1,
      telephone: '090-0000-0000',
      status: 1,
      startDate: new Date(),
      endDate: new Date(),
      parentId: null,
      isSameParentAddress: 0,
      insuredOrgCode: null,
      insuredCustomerCode: null,
      insuredBranchCode: null,
      insureCode: null,
      medicalCertPayerCode: null,
      medicalCertReceiptCode: null,
      isReceiveNotifications: false,
      aid: null,
      bid: null,
      cid: null,
      isDeleted: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'test',
      updatedBy: 'test',
      $query: jest.fn().mockReturnThis(),
      $set: jest.fn().mockReturnThis(),
    } as unknown as Customer;

    it('should throw BadRequestException when meeting status is END', async () => {
      // ステータスがENDのミーティングのモック
      const mockEndedMeeting = {
        ...mockMeeting,
        status: MEETING_STATUS.END,
      };

      // throwIfNotFound メソッドが正しく呼び出されるようにモック設定
      mockQuery.throwIfNotFound.mockResolvedValue(mockEndedMeeting);

      // メソッド実行と検証
      await expect(service.joinMeeting(1, mockCustomer)).rejects.toThrow(
        'URLの有効期間が切れています。',
      );
    });
  });

  // notifyUpdatedMeetingToDenkaruのテスト
  describe('notifyUpdatedMeetingToDenkaru', () => {
    it('should notify denkaru successfully', async () => {
      // モックの設定
      const mockResponse = {
        data: {
          success: true,
        },
      };

      // httpService.postのモック
      _httpService.post = jest.fn().mockReturnValue(mockResponse);

      // メソッド実行
      await service.notifyUpdatedMeetingToDenkaru(1);

      // 検証
      expect(_httpService.post).toHaveBeenCalledWith(
        `${process.env.DENKARU_API_ENDPOINT}/meetings/subscribe/1`,
      );
      expect(lastValueFrom).toHaveBeenCalledWith(mockResponse);
    });

    it('should throw error when denkaru returns error', async () => {
      // モックの設定
      const mockErrorResponse = {
        data: {
          errors: [
            {
              message: 'テストエラー',
            },
          ],
        },
      };

      // httpService.postのモック
      _httpService.post = jest.fn().mockReturnValue(mockErrorResponse);

      // メソッド実行と検証
      await expect(service.notifyUpdatedMeetingToDenkaru(1)).rejects.toThrow(
        'テストエラー',
      );
      expect(lastValueFrom).toHaveBeenCalledWith(mockErrorResponse);
    });
  });
});
