import {
  ChimeSDKMediaPipelinesClient,
  CreateMediaCapturePipelineCommand,
  ResourceLimitExceededException,
} from '@aws-sdk/client-chime-sdk-media-pipelines';
import {
  ChimeSDKMeetings,
  CreateMeetingWithAttendeesCommandOutput,
} from '@aws-sdk/client-chime-sdk-meetings';
import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ModelClass } from 'objection';
import { lastValueFrom } from 'rxjs';

import { MEETING_ATTENDEE_TYPE, MEETING_ERROR_MSG } from '@/common/constants';
import {
  DESIRED_DATE_STATUS,
  IS_DELETED,
  MEETING_STATUS,
  PHARMACY_RESERVE_VIDEO_CALL_STATUS,
} from '@/common/constants/master-type';
import { Customer } from '@/models/Customer';
import { Meeting } from '@/models/Meeting';

@Injectable()
export default class MeetingService {
  private chime: ChimeSDKMeetings;
  private chimeMediaPipeline: ChimeSDKMediaPipelinesClient;
  private logger = new Logger();

  constructor(
    @Inject(Meeting.name) private meeting: ModelClass<Meeting>,
    private readonly httpService: HttpService,
  ) {
    const configs = {
      credentials:
        process.env.NODE_ENV === 'development'
          ? {
              accessKeyId: process.env.LOCAL_ACCESS_KEY_ID || '',
              secretAccessKey: process.env.LOCAL_SECRET_ACCESS_KEY || '',
            }
          : undefined,
      region: 'ap-northeast-1',
    };

    this.chime = new ChimeSDKMeetings(configs);
    this.chimeMediaPipeline = new ChimeSDKMediaPipelinesClient(configs);
  }

  private async getChimeMeeting(meetingId: number): Promise<Meeting> {
    return await this.meeting
      .query()
      .where('isDeleted', IS_DELETED.FALSE)
      .findById(meetingId)
      .throwIfNotFound();
  }

  async getMeeting(
    meetingId: number,
  ): Promise<CreateMeetingWithAttendeesCommandOutput> {
    const meeting = await this.getChimeMeeting(meetingId);

    if (meeting.chimeMeetingId) {
      const meetingResource = await this.chime.getMeeting({
        MeetingId: meeting.chimeMeetingId,
      });

      const { Attendees } = await this.chime.listAttendees({
        MeetingId: meetingResource.Meeting?.MeetingId,
      });

      return {
        ...meetingResource,
        Attendees,
      };
    }

    throw new NotFoundException();
  }

  async joinMeeting(
    meetingId: number,
    customer: Customer,
  ): Promise<CreateMeetingWithAttendeesCommandOutput> {
    const meeting = await this.getChimeMeeting(meetingId);

    if (meeting.status !== MEETING_STATUS.END && meeting.chimeMeetingId) {
      const meetingResource = await this.chime.getMeeting({
        MeetingId: meeting.chimeMeetingId,
      });

      const { Attendee } = await this.chime.createAttendee({
        MeetingId: meetingResource.Meeting?.MeetingId,
        ExternalUserId: `${MEETING_ATTENDEE_TYPE.PATIENT}_${customer.customerId}`,
      });

      if (meeting.status === MEETING_STATUS.RESERVED) {
        await this.updateMeetingStatus(
          meetingId,
          MEETING_STATUS.PATIENT_JOINED,
        );
      }

      if (meeting.status === MEETING_STATUS.DOCTOR_JOINED) {
        await this.updateMeetingStatus(meetingId, MEETING_STATUS.BOTH_JOINED);
      }

      return { ...meetingResource, Attendees: Attendee ? [Attendee] : [] };
    }

    throw new BadRequestException(MEETING_ERROR_MSG.MEETING_SESSION_TIMEOUT);
  }

  async leaveMeeting(customer: Customer, meetingId: number): Promise<void> {
    const meeting = await this.getChimeMeeting(meetingId);

    if (meeting.chimeMeetingId && meeting.status !== MEETING_STATUS.END) {
      const attendees = await this.chime.listAttendees({
        MeetingId: meeting.chimeMeetingId,
      });

      const selfChimeAttendee = attendees.Attendees?.find(
        ({ ExternalUserId }) => {
          const patientAttendeeRegex = `${MEETING_ATTENDEE_TYPE.PATIENT}_${customer.customerId}`;
          return ExternalUserId === patientAttendeeRegex;
        },
      );

      if (selfChimeAttendee) {
        await this.chime.deleteAttendee({
          MeetingId: meeting.chimeMeetingId,
          AttendeeId: selfChimeAttendee.AttendeeId,
        });
      }
    } else {
      // chimeはまだ始まっていない場合
      if (meeting.status === MEETING_STATUS.PATIENT_JOINED) {
        await this.updateMeetingStatus(meetingId, MEETING_STATUS.RESERVED);
      }
    }
  }

  async getMeetingDetail(meetingId: number): Promise<Meeting> {
    const query = this.meeting
      .query()
      .leftJoinRelated('reservation.reservationDetails.calendarTreatment')
      .where('meetingId', meetingId)
      .andWhere('meeting.isDeleted', IS_DELETED.FALSE)
      .andWhere((builder) => {
        builder
          .where(
            'reservation:reservationDetails:calendar_treatment.isDeleted',
            IS_DELETED.FALSE,
          )
          .orWhereNull('meeting.reserveId');
      })
      .withGraphFetched({
        patient: {
          hospital: {
            portalHospital: true,
          },
          portalCustomer: true,
        },
        reservation: {
          patient: true,
          reservationDetails: {
            examTimeSlot: true,
            calendarTreatment: {
              treatmentDepartment: true,
            },
            pharmacyReserveDetail: {
              pharmacyReserve: true,
            },
          },
        },
        pharmacyReserve: {
          pharmacyDesiredDates: true,
        },
      })
      .modifyGraph(
        'reservation.reservationDetails.pharmacyReserveDetail.pharmacyReserve',
        (builder) => {
          builder
            .where('isDeleted', IS_DELETED.FALSE)
            .whereIn('desiredDateStatus', [
              DESIRED_DATE_STATUS.CONFIRMING, // 確認中
              DESIRED_DATE_STATUS.BEFORE_CONFIGURE, // 設定前
            ]);
        },
      )
      .modifyGraph('pharmacyReserve', (builder) => {
        builder
          .where('isDeleted', IS_DELETED.FALSE)
          .whereIn('desiredDateStatus', [
            DESIRED_DATE_STATUS.CONFIRMING,
            DESIRED_DATE_STATUS.CONFIGURED,
          ]);
      })
      .modifyGraph('pharmacyReserve.pharmacyDesiredDates', (builder) => {
        builder.where('isDeleted', IS_DELETED.FALSE);
      });

    return await query.first().throwIfNotFound();
  }

  async updateMeetingStatus(
    meetingId: number,
    status: MEETING_STATUS,
  ): Promise<Meeting> {
    const meeting = await this.meeting
      .query()
      .where('meetingId', meetingId)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .withGraphFetched({
        pharmacyReserve: true,
      })
      .modifyGraph('pharmacyReserve', (builder) => {
        builder
          .where('isDeleted', IS_DELETED.FALSE)
          .whereIn('desiredDateStatus', [
            DESIRED_DATE_STATUS.CONFIRMING,
            DESIRED_DATE_STATUS.CONFIGURED,
          ]);
      })
      .first()
      .throwIfNotFound();

    const trx = await Meeting.startTransaction();
    try {
      if (status === MEETING_STATUS.PATIENT_JOINED && meeting.pharmacyReserve) {
        await meeting.pharmacyReserve.$query(trx).patch({
          videocallStatus: PHARMACY_RESERVE_VIDEO_CALL_STATUS.PATIENT_JOINED,
          createdBy: meeting.pharmacyReserve.createdBy,
        });
      }

      if (status === MEETING_STATUS.RESERVED && meeting.pharmacyReserve) {
        await meeting.pharmacyReserve.$query(trx).patch({
          videocallStatus: PHARMACY_RESERVE_VIDEO_CALL_STATUS.RESERVED,
          createdBy: meeting.pharmacyReserve.createdBy,
        });
      }

      const result = await meeting.$query(trx).patchAndFetch({
        status,
        createdBy: meeting.createdBy,
      });

      await trx.commit();

      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  async notifyUpdatedMeetingToDenkaru(meetingId: number): Promise<void> {
    const { data } = await lastValueFrom(
      this.httpService.post(
        `${process.env.DENKARU_API_ENDPOINT}/meetings/subscribe/${meetingId}`,
      ),
    );

    this.logger.debug('notifyUpdatedMeetingToDenkaru: ', { data });

    if (data?.errors && data.errors?.length) {
      throw new Error(
        data.errors[0]?.message || '電カルサーバへアクセスできません。',
      );
    }
  }

  async startMeetingTranscription(meetingId: number): Promise<void> {
    const meeting = await this.getChimeMeeting(meetingId);

    if (meeting.chimeMeetingId) {
      await this.chime.startMeetingTranscription({
        MeetingId: meeting.chimeMeetingId,
        TranscriptionConfiguration: {
          EngineTranscribeSettings: {
            LanguageCode: 'ja-JP',
            PartialResultsStability: 'medium',
            Region: 'ap-northeast-1',
          },
        },
      });

      return;
    }

    throw new NotFoundException();
  }

  async createMediaCapturePipeline(meetingId: number) {
    const meeting = await this.getChimeMeeting(meetingId);

    if (meeting.chimeMeetingId) {
      const meetingResource = await this.chime.getMeeting({
        MeetingId: meeting.chimeMeetingId,
      });

      try {
        await this.chimeMediaPipeline.send(
          new CreateMediaCapturePipelineCommand({
            SourceType: 'ChimeSdkMeeting',
            SourceArn: meetingResource.Meeting?.MeetingArn,
            SinkType: 'S3Bucket',
            SinkArn: 'arn:aws:s3:::clinic-map-test', // TODO: update later
            ChimeSdkMeetingConfiguration: {
              ArtifactsConfiguration: {
                Audio: { MuxType: 'AudioOnly' },
                Video: { State: 'Disabled' },
                Content: { State: 'Disabled' },
              },
            },
          }),
        );

        return;
      } catch (error) {
        // Already exists, skip error
        if (error instanceof ResourceLimitExceededException) {
          return;
        }

        throw error;
      }
    }

    throw new NotFoundException();
  }
}
