import { Inject, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { ModelClass, ref } from 'objection';

import { AGREE_STATUS, IS_DELETED } from '@/common/constants/master-type';
import { Agree } from '@/models/Agree';
import { AgreeLog } from '@/models/AgreeLog';

import { AgreeRegisterInput } from './agree.input';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export class AgreeService {
  constructor(
    @Inject(Agree.name)
    private agree: ModelClass<Agree>,
    @Inject(AgreeLog.name)
    private agreeLog: ModelClass<AgreeLog>,
  ) {}

  async getUncheckedAgreements(customerId: number): Promise<Agree[]> {
    const today = dayjs().tz('Asia/Tokyo').toDate();
    const queryBuilder = this.agree
      .query()
      .where('isDeleted', AGREE_STATUS.STATUS_DEFAULT)
      .andWhere('agreeStartDatetime', '<=', today)
      .whereNotExists(
        this.agreeLog
          .query()
          .where(
            'portal_agree_log.agreementId',
            ref('portal_m_agree.agreementId'),
          )
          .andWhere('customerId', customerId)
          .andWhere('portal_agree_log.isDeleted', IS_DELETED.FALSE)
          .andWhere('portal_m_agree.isDeleted', IS_DELETED.FALSE),
      );

    return await queryBuilder;
  }

  async registerAgreements(input: AgreeRegisterInput[]): Promise<boolean> {
    const trx = await AgreeLog.startTransaction();

    try {
      await this.agreeLog.query(trx).insert(input);
      await trx.commit();

      return true;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
