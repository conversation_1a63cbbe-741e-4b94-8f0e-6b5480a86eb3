import { Test, TestingModule } from '@nestjs/testing';

import { LoginCustomer } from '@/customer/customer.output';
import { Agree } from '@/models/Agree';

import { AgreeRegisterInput } from './agree.input';
import { AgreeResolver } from './agree.resolver';
import { AgreeService } from './agree.service';

jest.mock('@/common/guards/customer/active-customer-login.guard');

describe('AgreeResolver', () => {
  let resolver: AgreeResolver;
  let service: AgreeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgreeResolver,
        {
          provide: AgreeService,
          useValue: {
            getUncheckedAgreements: jest.fn(),
            registerAgreements: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<AgreeResolver>(AgreeResolver);
    service = module.get<AgreeService>(AgreeService);
  });

  describe('getUncheckedAgreements', () => {
    it('should return unchecked agreements', async () => {
      const loginCustomer = {
        customerId: 123,
      } as LoginCustomer;
      const mockAgreements: Partial<Agree>[] = [
        { agreementId: 1, title: 'Agreement 1' },
      ];

      (service.getUncheckedAgreements as jest.Mock).mockResolvedValue(
        mockAgreements,
      );

      const result = await resolver.getUncheckedAgreements(loginCustomer);
      expect(result).toEqual(mockAgreements);
      expect(service.getUncheckedAgreements).toHaveBeenCalledWith(123);
    });
  });

  describe('registerAgreements', () => {
    it('should register agreements and return true', async () => {
      const loginCustomer: LoginCustomer = { customerId: 123 } as LoginCustomer;
      const params: AgreeRegisterInput[] = [
        { agreementId: 1, agreeDatetime: new Date() },
      ];
      const expectedParams = [{ ...params[0], customerId: 123 }];

      (service.registerAgreements as jest.Mock).mockResolvedValue(true);

      const result = await resolver.registerAgreements(loginCustomer, params);
      expect(result).toBe(true);
      expect(service.registerAgreements).toHaveBeenCalledWith(expectedParams);
    });
  });
});
