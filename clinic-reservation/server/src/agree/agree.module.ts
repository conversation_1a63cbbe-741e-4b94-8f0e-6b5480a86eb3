import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import { TokenService } from '@/common/service/token.service';

import { AgreeResolver } from './agree.resolver';
import { AgreeService } from './agree.service';

@Module({
  imports: [HttpModule],
  providers: [AgreeResolver, AgreeService, ActiveAgreeGuard, TokenService],
  exports: [AgreeService],
})
export class AgreeModule {}
