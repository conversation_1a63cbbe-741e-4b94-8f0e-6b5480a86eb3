import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';

import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { ActiveCustomerLoginGuard } from '@/common/guards/customer/active-customer-login.guard';
import { LoginCustomer } from '@/customer/customer.output';
import { Agree } from '@/models/Agree';

import { AgreeRegisterInput } from './agree.input';
import { AgreeService } from './agree.service';

@Resolver()
export class AgreeResolver {
  constructor(private agreeService: AgreeService) {}

  @UseGuards(ActiveCustomerLoginGuard)
  @Query(() => [Agree])
  async getUncheckedAgreements(
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<Agree[]> {
    return await this.agreeService.getUncheckedAgreements(
      loginCustomer.customerId,
    );
  }

  @UseGuards(ActiveCustomerLoginGuard)
  @Mutation(() => Boolean)
  async registerAgreements(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('params', { type: () => [AgreeRegisterInput] })
    params: AgreeRegisterInput[],
  ): Promise<boolean> {
    const updatedParams = params.map((input) => {
      return { ...input, customerId: loginCustomer.customerId };
    });

    return await this.agreeService.registerAgreements(updatedParams);
  }
}
