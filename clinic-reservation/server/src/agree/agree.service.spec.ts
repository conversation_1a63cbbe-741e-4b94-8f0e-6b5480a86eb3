import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { ModelClass } from 'objection';

import { AGREE_STATUS } from '@/common/constants/master-type';
import { Agree } from '@/models/Agree';
import { AgreeLog } from '@/models/AgreeLog';

import { AgreeRegisterInput } from './agree.input';
import { AgreeService } from './agree.service';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * AgreeServiceのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. 未確認の同意事項の取得
 * 2. 同意事項の登録
 */
describe('AgreeService', () => {
  let service: AgreeService;
  let mockAgree: jest.Mocked<Partial<ModelClass<Agree>>>;
  let mockAgreeLog: jest.Mocked<Partial<ModelClass<AgreeLog>>>;

  // クエリビルダーのモック
  // whereやinsertなどの各メソッドをモック化し、メソッドチェーンを可能にする
  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    whereNotExists: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
  };

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - サービスのインスタンス化
   */
  beforeEach(async () => {
    // Agreeモデルのモック作成
    mockAgree = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
    } as jest.Mocked<Partial<ModelClass<Agree>>>;

    // AgreeLogモデルのモック作成
    mockAgreeLog = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
    } as jest.Mocked<Partial<ModelClass<AgreeLog>>>;

    // トランザクション処理のモック化
    AgreeLog.startTransaction = jest.fn().mockResolvedValue({
      commit: jest.fn(),
      rollback: jest.fn(),
    });

    // テストモジュールの設定
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgreeService,
        {
          provide: Agree.name,
          useValue: mockAgree,
        },
        {
          provide: AgreeLog.name,
          useValue: mockAgreeLog,
        },
      ],
    }).compile();

    service = module.get<AgreeService>(AgreeService);
  });

  // サービスが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * getUncheckedAgreementsメソッドのテスト
   * 未確認の同意事項を正しく取得できることを確認
   */
  describe('getUncheckedAgreements', () => {
    it('should return unchecked agreements', async () => {
      // テストデータの準備
      const customerId = 1;
      const mockAgreements = [
        { agreementId: 1, title: 'Agreement 1' },
        { agreementId: 2, title: 'Agreement 2' },
      ];

      // モックの戻り値を設定
      mockQueryBuilder.whereNotExists.mockResolvedValue(mockAgreements);

      // メソッドの実行
      const result = await service.getUncheckedAgreements(customerId);

      // 期待する結果の検証
      expect(result).toEqual(mockAgreements);
      expect(mockAgree.query).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'isDeleted',
        AGREE_STATUS.STATUS_DEFAULT,
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'agreeStartDatetime',
        '<=',
        expect.any(Date),
      );
    });
  });

  /**
   * registerAgreementsメソッドのテスト
   * 同意事項の登録処理が正しく動作することを確認
   */
  describe('registerAgreements', () => {
    // 正常系: 登録が成功するケース
    it('should register agreements successfully', async () => {
      const input: AgreeRegisterInput[] = [
        {
          agreementId: 1,
          agreeDatetime: new Date(),
        },
      ];

      mockQueryBuilder.insert.mockResolvedValue(true);

      const result = await service.registerAgreements(input);

      expect(result).toBe(true);
      expect(mockAgreeLog.query).toHaveBeenCalled();
      expect(mockQueryBuilder.insert).toHaveBeenCalledWith(input);
    });

    // 異常系: データベースエラーが発生するケース
    it('should handle transaction failure', async () => {
      const input: AgreeRegisterInput[] = [
        {
          agreementId: 1,
          agreeDatetime: new Date(),
        },
      ];

      mockQueryBuilder.insert.mockRejectedValue(new Error('Database error'));

      await expect(service.registerAgreements(input)).rejects.toThrow(
        'Database error',
      );
    });
  });
});
