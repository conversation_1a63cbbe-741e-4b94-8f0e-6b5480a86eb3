import { Test, TestingModule } from '@nestjs/testing';
import { GraphQLError } from 'graphql';
import { ModelClass } from 'objection';

import { ERROR_TYPE } from '@/common/constants/error-type';
import { IS_DELETED } from '@/common/constants/master-type';
import { HospitalFavoriteService } from '@/hospital-favorite/hospital-favorite.service';
import { Examination } from '@/models/Examination';
import { ImportHospital } from '@/models/ImportHospital';
import { ImportOperatingHour } from '@/models/ImportOperatingHour';
import { PortalHospital } from '@/models/PortalHospital';

import { ImportBusinessTime } from './hospital.output';
import { HospitalService } from './hospital.service';

// S3ClientServiceのモック
jest.mock('@/common/service/s3-client.service', () => {
  return {
    S3ClientService: jest.fn().mockImplementation(() => {
      return {
        getSignedUrl: jest
          .fn()
          .mockResolvedValue('https://example.com/image.jpg'),
      };
    }),
  };
});

describe('HospitalService', () => {
  let service: HospitalService;
  let portalHospitalModelMock: jest.Mocked<Partial<ModelClass<PortalHospital>>>;
  let importHospitalModelMock: jest.Mocked<Partial<ModelClass<ImportHospital>>>;
  let importOperatingHourModelMock: jest.Mocked<
    Partial<ModelClass<ImportOperatingHour>>
  >;
  let examinationModelMock: jest.Mocked<Partial<ModelClass<Examination>>>;
  let hospitalFavoriteServiceMock: jest.Mocked<
    Partial<HospitalFavoriteService>
  >;

  beforeEach(async () => {
    // モックの作成
    portalHospitalModelMock = {
      query: jest.fn().mockReturnThis(),
      findById: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      withGraphFetched: jest.fn().mockReturnThis(),
      modifyGraph: jest.fn().mockReturnThis(),
      first: jest.fn(),
    } as unknown as jest.Mocked<Partial<ModelClass<PortalHospital>>>;

    importHospitalModelMock = {
      query: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      withGraphFetched: jest.fn().mockReturnThis(),
      modifyGraph: jest.fn().mockReturnThis(),
      first: jest.fn(),
    } as unknown as jest.Mocked<Partial<ModelClass<ImportHospital>>>;

    importOperatingHourModelMock = {
      query: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      min: jest.fn().mockReturnThis(),
      max: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockResolvedValue([
        {
          serviceDay: '月',
          earliestStartTime: '09:00',
          latestEndTime: '17:00',
        },
      ]),
    } as unknown as jest.Mocked<Partial<ModelClass<ImportOperatingHour>>>;

    examinationModelMock = {
      query: jest
        .fn()
        .mockResolvedValue([{ name: '健康診断' }, { name: '人間ドック' }]),
      select: jest.fn().mockReturnThis(),
    } as unknown as jest.Mocked<Partial<ModelClass<Examination>>>;

    // HospitalFavoriteServiceのモックを追加
    hospitalFavoriteServiceMock = {
      getHospitalFavorites: jest.fn().mockResolvedValue([]),
      isHospitalFavorite: jest.fn().mockResolvedValue(false),
      toggleHospitalFavorite: jest.fn().mockResolvedValue(true),
    } as unknown as jest.Mocked<Partial<HospitalFavoriteService>>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HospitalService,
        {
          provide: PortalHospital.name,
          useValue: portalHospitalModelMock,
        },
        {
          provide: ImportHospital.name,
          useValue: importHospitalModelMock,
        },
        {
          provide: ImportOperatingHour.name,
          useValue: importOperatingHourModelMock,
        },
        {
          provide: Examination.name,
          useValue: examinationModelMock,
        },
        {
          provide: HospitalFavoriteService,
          useValue: hospitalFavoriteServiceMock,
        },
      ],
    }).compile();

    service = module.get<HospitalService>(HospitalService);

    // processPicturesメソッドをモック
    jest
      .spyOn(
        service as unknown as { processPictures: jest.Mock },
        'processPictures',
      )
      .mockImplementation((hospitals) => {
        return hospitals;
      });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getHospital', () => {
    it('should return a hospital by id', async () => {
      const hospitalId = 1;
      const mockHospital = {
        hospitalId,
        name: 'Test Hospital',
        pictures: [],
        hospitalStaffs: [],
        hospitalFavorites: [],
        isFavorite: false,
      };

      (
        portalHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);

      const result = await service.getHospital(hospitalId);

      expect(portalHospitalModelMock.query).toHaveBeenCalled();
      expect(
        (portalHospitalModelMock as unknown as { where: jest.Mock }).where,
      ).toHaveBeenCalledWith('isActive', true);
      expect(
        (portalHospitalModelMock as unknown as { where: jest.Mock }).where,
      ).toHaveBeenCalledWith('hospitalId', hospitalId);
      expect(
        (portalHospitalModelMock as unknown as { andWhere: jest.Mock })
          .andWhere,
      ).toHaveBeenCalledWith('portalHospital.isDeleted', IS_DELETED.FALSE);
      expect(
        (portalHospitalModelMock as unknown as { withGraphFetched: jest.Mock })
          .withGraphFetched,
      ).toHaveBeenCalled();
      expect(
        (portalHospitalModelMock as unknown as { modifyGraph: jest.Mock })
          .modifyGraph,
      ).toHaveBeenCalled();
      expect(result).toEqual(mockHospital);
    });

    it('should throw GraphQLError if hospital not found', async () => {
      const hospitalId = 999;

      (
        portalHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(null);

      await expect(service.getHospital(hospitalId)).rejects.toThrow(
        GraphQLError,
      );
      await expect(service.getHospital(hospitalId)).rejects.toThrow(
        ERROR_TYPE.HOSPITAL_NOT_FOUND,
      );
    });

    it('should handle favorite logic with customerId', async () => {
      const hospitalId = 1;
      const customerId = 123;
      const mockHospital = {
        hospitalId,
        name: 'Test Hospital',
        pictures: [],
        hospitalStaffs: [],
        hospitalFavorites: [{ customerId }],
        isFavorite: true,
      };
      (
        portalHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      const result = await service.getHospital(hospitalId, customerId);
      expect(result).toEqual(mockHospital);
      expect(result.isFavorite).toBe(true);
    });
    it('should handle favorite logic with sessionId', async () => {
      const hospitalId = 1;
      const sessionId = 'session-abc';
      const mockHospital = {
        hospitalId,
        name: 'Test Hospital',
        pictures: [],
        hospitalStaffs: [],
        hospitalFavorites: [{ sessionId }],
        isFavorite: true,
      };
      (
        portalHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      const result = await service.getHospital(
        hospitalId,
        undefined,
        sessionId,
      );
      expect(result).toEqual(mockHospital);
      expect(result.isFavorite).toBe(true);
    });
    it('should handle hospitalFavorites undefined', async () => {
      const hospitalId = 2;
      const mockHospital = {
        hospitalId,
        name: 'Test Hospital',
        pictures: [],
        hospitalStaffs: [],
        // hospitalFavorites is undefined
      };
      (
        portalHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      const result = await service.getHospital(hospitalId);
      expect(result.hospitalFavorites).toEqual([]);
      expect(result.isFavorite).toBe(false);
    });
    it('should handle hospitalFavorites as empty array', async () => {
      const hospitalId = 3;
      const mockHospital = {
        hospitalId,
        name: 'Test Hospital',
        pictures: [],
        hospitalStaffs: [],
        hospitalFavorites: [],
      };
      (
        portalHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      const result = await service.getHospital(hospitalId);
      expect(result.hospitalFavorites).toEqual([]);
      expect(result.isFavorite).toBe(false);
    });
    it('should prefer customerId over sessionId for favorite logic', async () => {
      const hospitalId = 4;
      const customerId = 123;
      const sessionId = 'session-abc';
      const mockHospital = {
        hospitalId,
        name: 'Test Hospital',
        pictures: [],
        hospitalStaffs: [],
        hospitalFavorites: [{ customerId }],
        isFavorite: true,
      };
      (
        portalHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      const result = await service.getHospital(
        hospitalId,
        customerId,
        sessionId,
      );
      expect(result).toEqual(mockHospital);
      expect(result.isFavorite).toBe(true);
    });
  });

  describe('getImportHospital', () => {
    it('should return import hospital and business times', async () => {
      const scuelId = 'test-scuel-id';
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        hospitalFavorites: [],
      };
      const mockBusinessTimes: ImportBusinessTime[] = [
        {
          serviceDay: '月',
          earliestStartTime: '09:00',
          latestEndTime: '17:00',
        },
      ];

      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);

      // operatingHoursの処理をモック
      jest
        .spyOn(
          service as unknown as { getImportHospital: jest.Mock },
          'getImportHospital',
        )
        .mockImplementation(() => {
          return Promise.resolve({
            importHospital: mockHospital,
            importBusinessTimes: mockBusinessTimes,
          });
        });

      const result = await service.getImportHospital(scuelId);

      expect(result).toEqual({
        importHospital: mockHospital,
        importBusinessTimes: mockBusinessTimes,
      });
    });

    it('should throw GraphQLError if hospital not found', async () => {
      const scuelId = 'non-existent-id';

      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(null);

      await expect(service.getImportHospital(scuelId)).rejects.toThrow(
        GraphQLError,
      );
      await expect(service.getImportHospital(scuelId)).rejects.toThrow(
        ERROR_TYPE.HOSPITAL_NOT_FOUND,
      );
    });

    it('should handle favorite logic with customerId', async () => {
      const scuelId = 'test-scuel-id';
      const customerId = 123;
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        hospitalFavorites: [{ customerId }],
        isFavorite: true,
      };
      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      jest
        .spyOn(service as any, 'processPictures')
        .mockImplementation((hospitals) => hospitals);
      (importOperatingHourModelMock as any).query.mockReturnValue({
        select: () => ({
          min: () => ({
            max: () => ({
              where: () => ({
                groupBy: () => [
                  {
                    serviceDay: '月',
                    earliestStartTime: '09:00',
                    latestEndTime: '17:00',
                  },
                ],
              }),
            }),
          }),
        }),
      });
      const result = await service.getImportHospital(scuelId, customerId);
      expect(result.importHospital).toEqual(mockHospital);
      expect(result.importHospital.isFavorite).toBe(true);
    });
    it('should handle favorite logic with sessionId', async () => {
      const scuelId = 'test-scuel-id';
      const sessionId = 'session-abc';
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        hospitalFavorites: [{ sessionId }],
        isFavorite: true,
      };
      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      jest
        .spyOn(service as any, 'processPictures')
        .mockImplementation((hospitals) => hospitals);
      (importOperatingHourModelMock as any).query.mockReturnValue({
        select: () => ({
          min: () => ({
            max: () => ({
              where: () => ({
                groupBy: () => [
                  {
                    serviceDay: '月',
                    earliestStartTime: '09:00',
                    latestEndTime: '17:00',
                  },
                ],
              }),
            }),
          }),
        }),
      });
      const result = await service.getImportHospital(
        scuelId,
        undefined,
        sessionId,
      );
      expect(result.importHospital).toEqual(mockHospital);
      expect(result.importHospital.isFavorite).toBe(true);
    });
    it('should handle hospitalFavorites undefined', async () => {
      const scuelId = 'test-scuel-id-2';
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        // hospitalFavorites is undefined
      };
      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      jest
        .spyOn(service as any, 'processPictures')
        .mockImplementation((hospitals) => hospitals);
      (importOperatingHourModelMock as any).query.mockReturnValue({
        select: () => ({
          min: () => ({
            max: () => ({
              where: () => ({
                groupBy: () => [
                  {
                    serviceDay: '月',
                    earliestStartTime: '09:00',
                    latestEndTime: '17:00',
                  },
                ],
              }),
            }),
          }),
        }),
      });
      const result = await service.getImportHospital(scuelId);
      expect(result.importHospital.hospitalFavorites).toEqual([]);
      expect(result.importHospital.isFavorite).toBe(false);
    });
    it('should handle hospitalFavorites as empty array', async () => {
      const scuelId = 'test-scuel-id-3';
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        hospitalFavorites: [],
      };
      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      jest
        .spyOn(service as any, 'processPictures')
        .mockImplementation((hospitals) => hospitals);
      (importOperatingHourModelMock as any).query.mockReturnValue({
        select: () => ({
          min: () => ({
            max: () => ({
              where: () => ({
                groupBy: () => [
                  {
                    serviceDay: '月',
                    earliestStartTime: '09:00',
                    latestEndTime: '17:00',
                  },
                ],
              }),
            }),
          }),
        }),
      });
      const result = await service.getImportHospital(scuelId);
      expect(result.importHospital.hospitalFavorites).toEqual([]);
      expect(result.importHospital.isFavorite).toBe(false);
    });
    it('should handle no operatingHours returned', async () => {
      const scuelId = 'test-scuel-id-4';
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        hospitalFavorites: [],
      };
      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      jest
        .spyOn(service as any, 'processPictures')
        .mockImplementation((hospitals) => hospitals);
      (importOperatingHourModelMock as any).query.mockReturnValue({
        select: () => ({
          min: () => ({
            max: () => ({
              where: () => ({
                groupBy: () => [],
              }),
            }),
          }),
        }),
      });
      const result = await service.getImportHospital(scuelId);
      expect(result.importBusinessTimes).toEqual([]);
    });
    it('should prefer customerId over sessionId for favorite logic', async () => {
      const scuelId = 'test-scuel-id-5';
      const customerId = 123;
      const sessionId = 'session-abc';
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        hospitalFavorites: [{ customerId }],
        isFavorite: true,
      };
      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      jest
        .spyOn(service as any, 'processPictures')
        .mockImplementation((hospitals) => hospitals);
      (importOperatingHourModelMock as any).query.mockReturnValue({
        select: () => ({
          min: () => ({
            max: () => ({
              where: () => ({
                groupBy: () => [
                  {
                    serviceDay: '月',
                    earliestStartTime: '09:00',
                    latestEndTime: '17:00',
                  },
                ],
              }),
            }),
          }),
        }),
      });
      const result = await service.getImportHospital(
        scuelId,
        customerId,
        sessionId,
      );
      expect(result.importHospital).toEqual(mockHospital);
      expect(result.importHospital.isFavorite).toBe(true);
    });
    it('should handle empty examinations array', async () => {
      const scuelId = 'test-scuel-id-6';
      (examinationModelMock as any).query.mockResolvedValue([]);
      const mockHospital = {
        scuelId,
        name: 'Import Test Hospital',
        examinations: [],
        hospitalFavorites: [],
      };
      (
        importHospitalModelMock as unknown as { first: jest.Mock }
      ).first.mockResolvedValue(mockHospital);
      jest
        .spyOn(service as any, 'processPictures')
        .mockImplementation((hospitals) => hospitals);
      (importOperatingHourModelMock as any).query.mockReturnValue({
        select: () => ({
          min: () => ({
            max: () => ({
              where: () => ({
                groupBy: () => [
                  {
                    serviceDay: '月',
                    earliestStartTime: '09:00',
                    latestEndTime: '17:00',
                  },
                ],
              }),
            }),
          }),
        }),
      });
      const result = await service.getImportHospital(scuelId);
      expect(result.importHospital).toEqual(mockHospital);
      expect(result.importBusinessTimes.length).toBe(1);
    });
  });

  describe('processPictures', () => {
    it('should update picture filepaths for hospitals and staff', () => {
      process.env.APP_URL = 'http://localhost';
      const hospitals = [
        {
          pictures: [{ pictureDetail: { pictId: 1, filepath: 'img1.jpg' } }],
          hospitalStaffs: [
            {
              pictures: [
                { pictureDetail: { pictId: 2, filepath: 'img2.jpg' } },
              ],
            },
          ],
        },
      ];
      const serviceInstance = new HospitalService();
      // @ts-expect-error test hospital object is missing required properties for PortalHospital
      const result = serviceInstance['processPictures'](hospitals);
      expect(result[0].pictures[0].pictureDetail.filepath).toContain(
        'http://localhost/img1.jpg',
      );
      expect(
        result[0].hospitalStaffs[0].pictures[0].pictureDetail.filepath,
      ).toContain('http://localhost/img2.jpg');
    });
    it('should handle empty hospitals array', () => {
      const serviceInstance = new HospitalService();
      const result = serviceInstance['processPictures']([]);
      expect(result).toEqual([]);
    });
    it('should handle duplicate pictId', () => {
      process.env.APP_URL = 'http://localhost';
      const hospitals = [
        {
          pictures: [
            { pictureDetail: { pictId: 1, filepath: 'img1.jpg' } },
            { pictureDetail: { pictId: 1, filepath: 'img1.jpg' } },
          ],
          hospitalStaffs: [
            {
              pictures: [
                { pictureDetail: { pictId: 1, filepath: 'img1.jpg' } },
              ],
            },
          ],
        },
      ];
      const serviceInstance = new HospitalService();
      // @ts-expect-error test hospital object is missing required properties for PortalHospital
      const result = serviceInstance['processPictures'](hospitals);
      expect(result[0].pictures.length).toBe(2);
      expect(result[0].pictures[0].pictureDetail.filepath).toContain(
        'http://localhost/img1.jpg',
      );
      expect(result[0].pictures[1].pictureDetail.filepath).toContain(
        'http://localhost/img1.jpg',
      );
      expect(
        result[0].hospitalStaffs[0].pictures[0].pictureDetail.filepath,
      ).toContain('http://localhost/img1.jpg');
    });
    it('should handle hospitalStaffs as empty array', () => {
      process.env.APP_URL = 'http://localhost';
      const hospitals = [
        {
          pictures: [{ pictureDetail: { pictId: 1, filepath: 'img1.jpg' } }],
          hospitalStaffs: [],
        },
      ];
      const serviceInstance = new HospitalService();
      // @ts-expect-error test hospital object is missing required properties for PortalHospital
      const result = serviceInstance['processPictures'](hospitals);
      expect(result[0].pictures[0].pictureDetail.filepath).toContain(
        'http://localhost/img1.jpg',
      );
      expect(result[0].hospitalStaffs.length).toBe(0);
    });
    it('should handle pictures as empty array', () => {
      process.env.APP_URL = 'http://localhost';
      const hospitals = [
        {
          pictures: [],
          hospitalStaffs: [
            {
              pictures: [
                { pictureDetail: { pictId: 2, filepath: 'img2.jpg' } },
              ],
            },
          ],
        },
      ];
      const serviceInstance = new HospitalService();
      // @ts-expect-error test hospital object is missing required properties for PortalHospital
      const result = serviceInstance['processPictures'](hospitals);
      expect(result[0].pictures.length).toBe(0);
      expect(
        result[0].hospitalStaffs[0].pictures[0].pictureDetail.filepath,
      ).toContain('http://localhost/img2.jpg');
    });
  });
});
