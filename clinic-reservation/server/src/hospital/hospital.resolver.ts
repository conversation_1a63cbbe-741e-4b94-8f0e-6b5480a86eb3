import { UseGuards } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';

import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { SessionId } from '@/common/decorators/session-id.decorator';
import { CustomerLoginGuard } from '@/common/guards/customer/customer-login.guard';
import { LoginCustomer } from '@/customer/customer.output';
import { PortalHospital } from '@/models/PortalHospital';

import { ImportHospitalResponse } from './hospital.output';
import { HospitalService } from './hospital.service';
@Resolver()
export class HospitalResolver {
  constructor(private hospitalService: HospitalService) {}

  @UseGuards(CustomerLoginGuard)
  @Query(() => PortalHospital)
  async getHospitalById(
    @Args('id', { type: () => Number }) id: number,
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @SessionId() sessionId: string,
  ): Promise<PortalHospital | null> {
    const results = await this.hospitalService.getHospital(
      id,
      loginCustomer?.customerId,
      sessionId,
    );
    return results;
  }

  @UseGuards(CustomerLoginGuard)
  @Query(() => ImportHospitalResponse)
  async getImportHospital(
    @Args('scuelId', { type: () => String }) scuelId: string,
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @SessionId() sessionId: string,
  ): Promise<ImportHospitalResponse | null> {
    const result = await this.hospitalService.getImportHospital(
      scuelId,
      loginCustomer?.customerId,
      sessionId,
    );
    return result;
  }
}
