import { Inject, Injectable } from '@nestjs/common';
import { GraphQLError } from 'graphql';
import { ModelClass, QueryBuilder } from 'objection';

import { ERROR_MSG, ERROR_TYPE } from '@/common/constants/error-type';
import { IS_DELETED } from '@/common/constants/master-type';
import { Examination } from '@/models/Examination';
import { ImportHospital } from '@/models/ImportHospital';
import { ImportOperatingHour } from '@/models/ImportOperatingHour';
import { PortalHospital } from '@/models/PortalHospital';

import { ImportBusinessTime, ImportHospitalResponse } from './hospital.output';

@Injectable()
export class HospitalService {
  @Inject(PortalHospital.name)
  private hospital: ModelClass<PortalHospital>;

  @Inject(ImportHospital.name)
  private importHospital: ModelClass<ImportHospital>;

  @Inject(ImportOperatingHour.name)
  private importOperatingHour: ModelClass<ImportOperatingHour>;

  @Inject(Examination.name)
  private examination: ModelClass<Examination>;

  constructor() {}

  async getHospital(
    hospitalId: number,
    customerId?: number,
    sessionId?: string,
  ): Promise<PortalHospital> {
    const hospitalQuery: QueryBuilder<PortalHospital, PortalHospital[]> =
      this.hospital
        .query()
        .where('isActive', true)
        .where('hospitalId', hospitalId)
        .andWhere('portalHospital.isDeleted', IS_DELETED.FALSE)
        .modifyGraph('notifications', (builder) => {
          builder.modify('filterByStatus');
        })
        .modifyGraph('treatmentDepartments', (builder) => {
          builder.modify('isActive');
        });

    const hasFavoriteCondition = !!(customerId || sessionId);

    if (hasFavoriteCondition) {
      let where = {};
      if (customerId) {
        where = {
          customerId,
        };
      } else if (sessionId) {
        where = {
          sessionId,
        };
      }
      hospitalQuery.modifyGraph('hospitalFavorites', (builder) => {
        builder
          .where(where)
          .where('portalHospitalId', hospitalId)
          .where('isDeleted', IS_DELETED.FALSE);
      });
    }

    const relations = {
      treatmentCategories: true,
      treatmentDepartments: true,
      stations: {
        stationDetail: {
          railline: true,
        },
      },
      businessTimes: true,
      pictures: {
        pictureDetail: true,
      },
      tags: true,
      examinations: true,
      specialists: true,
      hospitalStaffs: {
        pictures: {
          pictureDetail: true,
        },
      },
      notifications: true,
      ...(hasFavoriteCondition && { hospitalFavorites: true }),
    };

    const hospital = await hospitalQuery
      .withGraphFetched(relations)
      .modifyGraph('hospitalStaffs', (builder) => {
        builder.orderBy('order');
      })
      .first();

    if (!hospital)
      throw new GraphQLError(ERROR_TYPE.HOSPITAL_NOT_FOUND, {
        extensions: {
          code: ERROR_TYPE.HOSPITAL_NOT_FOUND,
          userMessage: ERROR_MSG.HOSPITAL_NOT_FOUND,
        },
      });

    if (!hospital.hospitalFavorites) {
      hospital.hospitalFavorites = [];
    }

    hospital.isFavorite = hospital.hospitalFavorites.length > 0;

    const results = this.processPictures([hospital]);

    return results[0];
  }

  async getImportHospital(
    scuelId: string,
    customerId?: number,
    sessionId?: string,
  ): Promise<ImportHospitalResponse> {
    const examinations = await this.examination.query();
    const examList = examinations.map((exam) => exam.name);
    const hospitalQuery: QueryBuilder<ImportHospital, ImportHospital[]> =
      this.importHospital
        .query()
        .where('scuelId', scuelId)
        .andWhere('isDeleted', IS_DELETED.FALSE)
        .modifyGraph('examinations', (builder) => {
          builder.modify('filterByExaminations', {
            examinations: examList,
          });
        });

    const hasFavoriteCondition = !!(customerId || sessionId);

    if (hasFavoriteCondition) {
      let where = {};
      if (customerId) {
        where = {
          customerId,
        };
      } else if (sessionId) {
        where = {
          sessionId,
        };
      }

      hospitalQuery.modifyGraph('hospitalFavorites', (builder) => {
        builder
          .where(where)
          .where('scuelId', scuelId)
          .where('isDeleted', IS_DELETED.FALSE);
      });
    }

    const relations = {
      cashless: true,
      carpark: true,
      examinations: true,
      specialists: true,
      tags: true,
      barrierFree: true,
      reservations: true,
      services: {
        operatingHours: true,
      },
      ...(hasFavoriteCondition && { hospitalFavorites: true }),
    };

    const hospital = await hospitalQuery
      .withGraphFetched(relations)
      .modifyGraph('services.operatingHours', (builder) => {
        builder.where('scuelId', scuelId);
      })
      .first();

    if (!hospital)
      throw new GraphQLError(ERROR_TYPE.HOSPITAL_NOT_FOUND, {
        extensions: {
          code: ERROR_TYPE.HOSPITAL_NOT_FOUND,
          userMessage: ERROR_MSG.HOSPITAL_NOT_FOUND,
        },
      });

    if (!hospital.hospitalFavorites) {
      hospital.hospitalFavorites = [];
    }

    hospital.isFavorite = hospital.hospitalFavorites.length > 0;

    const operatingHours = await this.importOperatingHour
      .query()
      .select('serviceDay')
      .min('start_time as earliest_start_time')
      .max('end_time as latest_end_time')
      .where('portal_m_import_operating_hour.scuelId', scuelId)
      .groupBy('serviceDay');

    const importBusinessTimes: ImportBusinessTime[] = operatingHours.map(
      (opH) => ({
        serviceDay: opH.serviceDay,
        earliestStartTime: opH.earliestStartTime,
        latestEndTime: opH.latestEndTime,
      }),
    );

    return {
      importHospital: hospital,
      importBusinessTimes: importBusinessTimes,
    };
  }

  private processPictures(hospitals: PortalHospital[]): PortalHospital[] {
    const allPictures = hospitals.flatMap(({ pictures, hospitalStaffs }) => [
      ...pictures,
      ...hospitalStaffs.flatMap((staff) => staff.pictures),
    ]);

    const updatedPictures = allPictures.map(({ pictureDetail }) => ({
      ...pictureDetail,
      filepath: `${process.env.APP_URL}/${pictureDetail.filepath}`,
    }));

    const picturesMap = new Map(
      updatedPictures.map((picture) => [picture.pictId, picture]),
    );

    return hospitals.map((hospital) => ({
      ...hospital,
      pictures: hospital.pictures.map((picture) => ({
        ...picture,
        pictureDetail: picturesMap.get(picture.pictureDetail.pictId),
      })),
      hospitalStaffs: hospital.hospitalStaffs.map((staff) => ({
        ...staff,
        pictures: staff.pictures.map((picture) => ({
          ...picture,
          pictureDetail: picturesMap.get(picture.pictureDetail.pictId),
        })),
      })),
    })) as PortalHospital[];
  }
}
