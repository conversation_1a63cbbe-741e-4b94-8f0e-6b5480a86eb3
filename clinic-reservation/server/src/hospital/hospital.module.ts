import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { TokenService } from '@/common/service/token.service';
import { HospitalFavoriteModule } from '@/hospital-favorite/hospital-favorite.module';

import { HospitalResolver } from './hospital.resolver';
import { HospitalService } from './hospital.service';

@Module({
  imports: [HospitalFavoriteModule, HttpModule],
  providers: [HospitalService, HospitalResolver, TokenService],
  exports: [HospitalService],
})
export class HospitalModule {}
