import { Field, ObjectType } from '@nestjs/graphql';

import { ImportHospital } from '@/models/ImportHospital';
import { PortalHospital } from '@/models/PortalHospital';

@ObjectType()
export class TreatCatgWithHospitalCount {
  @Field(() => Number, { nullable: false })
  treatmentCategoryId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => Number, { nullable: false })
  hospitalCount: number;
}

@ObjectType()
export class TagWithHospitalCount {
  @Field(() => Number, { nullable: false })
  tagId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => Number, { nullable: false })
  hospitalCount: number;
}

@ObjectType()
export class CityWithHospitalCount {
  @Field(() => Number, { nullable: false })
  cityId: number;

  @Field(() => String, { nullable: false })
  name: string;

  @Field(() => Number, { nullable: false })
  hospitalCount: number;

  @Field(() => String, { nullable: false })
  prefectureName: string;
}

@ObjectType()
class LocationBase {
  @Field(() => Number)
  lat: number;

  @Field(() => Number)
  lon: number;
}

@ObjectType()
export class OSHospitalDoc extends PortalHospital {
  @Field(() => [String])
  sort: string[] | number[];

  @Field(() => LocationBase, { nullable: true })
  location?: LocationBase;

  @Field(() => String, { nullable: true })
  scuelId?: string;

  @Field(() => String, { nullable: true })
  homepage?: string;

  @Field(() => String, { nullable: true })
  reservationUrl?: string;
}

@ObjectType()
export class HospitalWithTotal {
  @Field(() => [OSHospitalDoc], { nullable: false })
  hospitals: OSHospitalDoc[];

  @Field(() => Number, { nullable: false })
  total: number;
}

@ObjectType()
export class ImportBusinessTime {
  @Field(() => String, { nullable: false })
  serviceDay: string;

  @Field(() => String, { nullable: true })
  earliestStartTime: string;

  @Field(() => String, { nullable: true })
  latestEndTime: string;
}

@ObjectType()
export class ImportHospitalResponse {
  @Field(() => ImportHospital, { nullable: true })
  importHospital: ImportHospital;

  @Field(() => [ImportBusinessTime], { nullable: false })
  importBusinessTimes: ImportBusinessTime[];
}
