import { Test, TestingModule } from '@nestjs/testing';

import { LoginCustomer } from '@/customer/customer.output';
import { ImportHospital } from '@/models/ImportHospital';
import { PortalHospital } from '@/models/PortalHospital';

import { ImportBusinessTime, ImportHospitalResponse } from './hospital.output';
import { HospitalResolver } from './hospital.resolver';
import { HospitalService } from './hospital.service';

describe('HospitalResolver', () => {
  let resolver: HospitalResolver;
  let hospitalService: HospitalService;
  const mockLoginCustomer: LoginCustomer = {
    customerId: 123,
    name: 'Test User',
    email: '<EMAIL>',
  } as LoginCustomer;
  const mockSessionId = 'test-session-id';

  beforeEach(async () => {
    const hospitalServiceMock = {
      getHospital: jest.fn(),
      getImportHospital: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HospitalResolver,
        {
          provide: HospitalService,
          useValue: hospitalServiceMock,
        },
      ],
    }).compile();

    resolver = module.get<HospitalResolver>(HospitalResolver);
    hospitalService = module.get<HospitalService>(HospitalService);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('getHospitalById', () => {
    it('should return a hospital by id', async () => {
      const hospitalId = 1;
      const mockHospital: Partial<PortalHospital> = {
        hospitalId,
        name: 'Test Hospital',
        hpInfId: 123,
        postCode: '123-4567',
        address1: 'Tokyo',
        descriptionTitle: 'Test Description',
        paymentDetails: 'Cash only',
        isCarpark: false,
        isActive: true,
      };

      jest
        .spyOn(hospitalService, 'getHospital')
        .mockResolvedValue(mockHospital as PortalHospital);

      const result = await resolver.getHospitalById(
        hospitalId,
        mockLoginCustomer,
        mockSessionId,
      );

      expect(hospitalService.getHospital).toHaveBeenCalledWith(
        hospitalId,
        mockLoginCustomer.customerId,
        mockSessionId,
      );
      expect(result).toEqual(mockHospital);
    });

    it('should return null if hospital not found', async () => {
      const hospitalId = 999;

      jest
        .spyOn(hospitalService, 'getHospital')
        .mockResolvedValue(null as unknown as PortalHospital);

      const result = await resolver.getHospitalById(
        hospitalId,
        mockLoginCustomer,
        mockSessionId,
      );

      expect(hospitalService.getHospital).toHaveBeenCalledWith(
        hospitalId,
        mockLoginCustomer.customerId,
        mockSessionId,
      );
      expect(result).toBeNull();
    });

    it('should handle missing loginCustomer gracefully', async () => {
      const hospitalId = 1;
      const mockHospital: Partial<PortalHospital> = {
        hospitalId,
        name: 'Test Hospital',
      };
      jest
        .spyOn(hospitalService, 'getHospital')
        .mockResolvedValue(mockHospital as PortalHospital);
      const result = await resolver.getHospitalById(
        hospitalId,
        undefined as any,
        mockSessionId,
      );
      expect(hospitalService.getHospital).toHaveBeenCalledWith(
        hospitalId,
        undefined,
        mockSessionId,
      );
      expect(result).toEqual(mockHospital);
    });
    it('should handle missing sessionId gracefully', async () => {
      const hospitalId = 1;
      const mockHospital: Partial<PortalHospital> = {
        hospitalId,
        name: 'Test Hospital',
      };
      jest
        .spyOn(hospitalService, 'getHospital')
        .mockResolvedValue(mockHospital as PortalHospital);
      const result = await resolver.getHospitalById(
        hospitalId,
        mockLoginCustomer,
        undefined as any,
      );
      expect(hospitalService.getHospital).toHaveBeenCalledWith(
        hospitalId,
        mockLoginCustomer.customerId,
        undefined,
      );
      expect(result).toEqual(mockHospital);
    });
  });

  describe('getImportHospital', () => {
    it('should return import hospital and business times', async () => {
      const scuelId = 'test-scuel-id';
      const mockResponse: ImportHospitalResponse = {
        importHospital: {
          scuelId,
          name: 'Import Test Hospital',
        } as unknown as ImportHospital,
        importBusinessTimes: [
          {
            serviceDay: '月',
            earliestStartTime: '09:00',
            latestEndTime: '17:00',
          },
        ] as ImportBusinessTime[],
      };

      jest
        .spyOn(hospitalService, 'getImportHospital')
        .mockResolvedValue(mockResponse);

      const result = await resolver.getImportHospital(
        scuelId,
        mockLoginCustomer,
        mockSessionId,
      );

      expect(hospitalService.getImportHospital).toHaveBeenCalledWith(
        scuelId,
        mockLoginCustomer.customerId,
        mockSessionId,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should propagate errors from service', async () => {
      const scuelId = 'non-existent-id';
      const mockError = new Error('Hospital not found');

      jest
        .spyOn(hospitalService, 'getImportHospital')
        .mockRejectedValue(mockError);

      await expect(
        resolver.getImportHospital(scuelId, mockLoginCustomer, mockSessionId),
      ).rejects.toThrow('Hospital not found');
      expect(hospitalService.getImportHospital).toHaveBeenCalledWith(
        scuelId,
        mockLoginCustomer.customerId,
        mockSessionId,
      );
    });

    it('should handle missing loginCustomer gracefully', async () => {
      const scuelId = 'test-scuel-id';
      const mockResponse: ImportHospitalResponse = {
        importHospital: {
          scuelId,
          name: 'Import Test Hospital',
        } as unknown as ImportHospital,
        importBusinessTimes: [],
      };
      jest
        .spyOn(hospitalService, 'getImportHospital')
        .mockResolvedValue(mockResponse);
      const result = await resolver.getImportHospital(
        scuelId,
        undefined as any,
        mockSessionId,
      );
      expect(hospitalService.getImportHospital).toHaveBeenCalledWith(
        scuelId,
        undefined,
        mockSessionId,
      );
      expect(result).toEqual(mockResponse);
    });
    it('should handle missing sessionId gracefully', async () => {
      const scuelId = 'test-scuel-id';
      const mockResponse: ImportHospitalResponse = {
        importHospital: {
          scuelId,
          name: 'Import Test Hospital',
        } as unknown as ImportHospital,
        importBusinessTimes: [],
      };
      jest
        .spyOn(hospitalService, 'getImportHospital')
        .mockResolvedValue(mockResponse);
      const result = await resolver.getImportHospital(
        scuelId,
        mockLoginCustomer,
        undefined as any,
      );
      expect(hospitalService.getImportHospital).toHaveBeenCalledWith(
        scuelId,
        mockLoginCustomer.customerId,
        undefined,
      );
      expect(result).toEqual(mockResponse);
    });
    it('should handle empty importBusinessTimes', async () => {
      const scuelId = 'test-scuel-id';
      const mockResponse: ImportHospitalResponse = {
        importHospital: {
          scuelId,
          name: 'Import Test Hospital',
        } as unknown as ImportHospital,
        importBusinessTimes: [],
      };
      jest
        .spyOn(hospitalService, 'getImportHospital')
        .mockResolvedValue(mockResponse);
      const result = await resolver.getImportHospital(
        scuelId,
        mockLoginCustomer,
        mockSessionId,
      );
      if (result) {
        expect(result.importBusinessTimes).toEqual([]);
      }
    });
  });
});
