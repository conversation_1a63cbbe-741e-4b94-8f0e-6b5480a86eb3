import { Test, TestingModule } from '@nestjs/testing';
import { ModelClass } from 'objection';

import { EXAMINATION_TYPE } from '@/common/constants/master-type';
import { Examination } from '@/models/Examination';

import { GetExaminationInput } from './examination.input';
import ExaminationService from './examination.service';

/**
 * ExaminationServiceのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. 検査情報の取得
 */
describe('ExaminationService', () => {
  let service: ExaminationService;
  let examinationModelMock: jest.Mocked<Partial<ModelClass<Examination>>>;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - サービスのインスタンス化
   */
  beforeEach(async () => {
    // クエリモックを作成
    examinationModelMock = {
      query: jest.fn().mockImplementation(() => ({
        where: jest.fn().mockReturnThis(),
        then: jest.fn(),
      })),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExaminationService,
        {
          provide: 'Examination',
          useValue: examinationModelMock,
        },
      ],
    }).compile();

    service = module.get<ExaminationService>(ExaminationService);
  });

  // サービスが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * getExaminationメソッドのテスト
   */
  describe('getExamination', () => {
    /**
     * テストケース: パラメータなしで検査情報を取得
     * 条件: パラメータを指定しない
     * 想定結果: すべての検査情報が返される
     */
    it('should return all examinations when no params are provided', async () => {
      const mockExaminations = [
        { examinationId: 1, name: '健康診断', type: EXAMINATION_TYPE.FACILITY },
        {
          examinationId: 2,
          name: '人間ドック',
          type: EXAMINATION_TYPE.MEDICAL_CHECKUP,
        },
      ];

      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        then: jest
          .fn()
          .mockImplementation((callback) => callback(mockExaminations)),
      };

      (
        examinationModelMock as unknown as { query: jest.Mock }
      ).query.mockReturnValue(queryBuilder);

      const result = await service.getExamination();

      expect(examinationModelMock.query).toHaveBeenCalled();
      expect(queryBuilder.where).not.toHaveBeenCalled();
      expect(result).toEqual(mockExaminations);
    }, 10000);

    /**
     * テストケース: 検査IDを指定して検査情報を取得
     * 条件: 検査IDを指定
     * 想定結果: 指定した検査IDに一致する検査情報が返される
     */
    it('should filter by examinationId when provided', async () => {
      const examinationId = 1;
      const mockExaminations = [
        { examinationId: 1, name: '健康診断', type: EXAMINATION_TYPE.FACILITY },
      ];
      const params: GetExaminationInput = { examinationId };

      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        then: jest
          .fn()
          .mockImplementation((callback) => callback(mockExaminations)),
      };

      (
        examinationModelMock as unknown as { query: jest.Mock }
      ).query.mockReturnValue(queryBuilder);

      const result = await service.getExamination(params);

      expect(examinationModelMock.query).toHaveBeenCalled();
      expect(queryBuilder.where).toHaveBeenCalledWith(
        'examinationId',
        examinationId,
      );
      expect(result).toEqual(mockExaminations);
    }, 10000);

    /**
     * テストケース: 検査タイプを指定して検査情報を取得
     * 条件: 検査タイプを指定
     * 想定結果: 指定した検査タイプに一致する検査情報が返される
     */
    it('should filter by examType when provided', async () => {
      const examType = EXAMINATION_TYPE.MEDICAL_CHECKUP;
      const mockExaminations = [
        {
          examinationId: 2,
          name: '人間ドック',
          type: EXAMINATION_TYPE.MEDICAL_CHECKUP,
        },
      ];
      const params: GetExaminationInput = { examType };

      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        then: jest
          .fn()
          .mockImplementation((callback) => callback(mockExaminations)),
      };

      (
        examinationModelMock as unknown as { query: jest.Mock }
      ).query.mockReturnValue(queryBuilder);

      const result = await service.getExamination(params);

      expect(examinationModelMock.query).toHaveBeenCalled();
      expect(queryBuilder.where).toHaveBeenCalledWith('type', examType);
      expect(result).toEqual(mockExaminations);
    }, 10000);

    /**
     * テストケース: 検査IDと検査タイプを指定して検査情報を取得
     * 条件: 検査IDと検査タイプを指定
     * 想定結果: 指定した検査IDと検査タイプに一致する検査情報が返される
     */
    it('should apply multiple filters when both examinationId and examType are provided', async () => {
      const examinationId = 2;
      const examType = EXAMINATION_TYPE.MEDICAL_CHECKUP;
      const mockExaminations = [
        {
          examinationId: 2,
          name: '人間ドック',
          type: EXAMINATION_TYPE.MEDICAL_CHECKUP,
        },
      ];
      const params: GetExaminationInput = { examinationId, examType };

      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        then: jest
          .fn()
          .mockImplementation((callback) => callback(mockExaminations)),
      };

      (
        examinationModelMock as unknown as { query: jest.Mock }
      ).query.mockReturnValue(queryBuilder);

      const result = await service.getExamination(params);

      expect(examinationModelMock.query).toHaveBeenCalled();
      expect(queryBuilder.where).toHaveBeenCalledWith(
        'examinationId',
        examinationId,
      );
      expect(queryBuilder.where).toHaveBeenCalledWith('type', examType);
      expect(result).toEqual(mockExaminations);
    }, 10000);
  });
});
