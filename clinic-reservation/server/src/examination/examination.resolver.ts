import { Args, Query, Resolver } from '@nestjs/graphql';

import { Examination } from '@/models/Examination';

import { GetExaminationInput } from './examination.input';
import ExaminationService from './examination.service';

@Resolver()
export default class ExaminationResolver {
  constructor(private examinationService: ExaminationService) {}

  @Query(() => [Examination])
  async getExamination(
    @Args('params', { type: () => GetExaminationInput, nullable: true })
    params?: GetExaminationInput,
  ): Promise<Examination[]> {
    return this.examinationService.getExamination(params);
  }
}
