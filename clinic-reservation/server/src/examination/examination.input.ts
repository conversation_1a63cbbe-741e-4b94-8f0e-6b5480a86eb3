import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsOptional } from 'class-validator';

import { EXAMINATION_TYPE } from '@/common/constants/master-type';

@InputType()
export class GetExaminationInput {
  @Field(() => Int, { nullable: true })
  examinationId?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsEnum(EXAMINATION_TYPE)
  examType?: EXAMINATION_TYPE;
}
