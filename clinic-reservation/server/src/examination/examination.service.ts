import { Inject, Injectable } from '@nestjs/common';
import { ModelClass } from 'objection';

import { Examination } from '@/models/Examination';

import { GetExaminationInput } from './examination.input';

@Injectable()
export default class ExaminationService {
  constructor(
    @Inject('Examination')
    private examination: ModelClass<Examination>,
  ) {}

  async getExamination(params?: GetExaminationInput): Promise<Examination[]> {
    const query = this.examination.query();

    if (params?.examinationId) {
      query.where('examinationId', params?.examinationId);
    }
    if (params?.examType) {
      query.where('type', params?.examType);
    }
    return await query;
  }
}
