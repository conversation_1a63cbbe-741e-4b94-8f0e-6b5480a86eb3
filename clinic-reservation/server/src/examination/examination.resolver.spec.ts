import { Test, TestingModule } from '@nestjs/testing';

import { EXAMINATION_TYPE } from '@/common/constants/master-type';
import { Examination } from '@/models/Examination';

import { GetExaminationInput } from './examination.input';
import ExaminationResolver from './examination.resolver';
import ExaminationService from './examination.service';

describe('ExaminationResolver', () => {
  let resolver: ExaminationResolver;
  let service: ExaminationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExaminationResolver,
        {
          provide: ExaminationService,
          useValue: {
            getExamination: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<ExaminationResolver>(ExaminationResolver);
    service = module.get<ExaminationService>(ExaminationService);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('getExamination', () => {
    it('should return examinations without filters when no params are provided', async () => {
      const mockExaminations: Examination[] = [
        {
          examinationId: 1,
          name: '健康診断',
          type: EXAMINATION_TYPE.FACILITY,
        } as Examination,
        {
          examinationId: 2,
          name: '人間ドック',
          type: EXAMINATION_TYPE.MEDICAL_CHECKUP,
        } as Examination,
      ];

      jest.spyOn(service, 'getExamination').mockResolvedValue(mockExaminations);

      const result = await resolver.getExamination();

      expect(service.getExamination).toHaveBeenCalledWith(undefined);
      expect(result).toEqual(mockExaminations);
    });

    it('should return examinations filtered by examinationId', async () => {
      const params: GetExaminationInput = { examinationId: 1 };
      const mockExaminations: Examination[] = [
        {
          examinationId: 1,
          name: '健康診断',
          type: EXAMINATION_TYPE.FACILITY,
        } as Examination,
      ];

      jest.spyOn(service, 'getExamination').mockResolvedValue(mockExaminations);

      const result = await resolver.getExamination(params);

      expect(service.getExamination).toHaveBeenCalledWith(params);
      expect(result).toEqual(mockExaminations);
    });

    it('should return examinations filtered by examType', async () => {
      const params: GetExaminationInput = {
        examType: EXAMINATION_TYPE.MEDICAL_CHECKUP,
      };
      const mockExaminations: Examination[] = [
        {
          examinationId: 2,
          name: '人間ドック',
          type: EXAMINATION_TYPE.MEDICAL_CHECKUP,
        } as Examination,
      ];

      jest.spyOn(service, 'getExamination').mockResolvedValue(mockExaminations);

      const result = await resolver.getExamination(params);

      expect(service.getExamination).toHaveBeenCalledWith(params);
      expect(result).toEqual(mockExaminations);
    });

    it('should return examinations filtered by both examinationId and examType', async () => {
      const params: GetExaminationInput = {
        examinationId: 2,
        examType: EXAMINATION_TYPE.MEDICAL_CHECKUP,
      };
      const mockExaminations: Examination[] = [
        {
          examinationId: 2,
          name: '人間ドック',
          type: EXAMINATION_TYPE.MEDICAL_CHECKUP,
        } as Examination,
      ];

      jest.spyOn(service, 'getExamination').mockResolvedValue(mockExaminations);

      const result = await resolver.getExamination(params);

      expect(service.getExamination).toHaveBeenCalledWith(params);
      expect(result).toEqual(mockExaminations);
    });

    it('should return empty array when no examinations match the criteria', async () => {
      const params: GetExaminationInput = { examinationId: 999 };
      const mockExaminations: Examination[] = [];

      jest.spyOn(service, 'getExamination').mockResolvedValue(mockExaminations);

      const result = await resolver.getExamination(params);

      expect(service.getExamination).toHaveBeenCalledWith(params);
      expect(result).toEqual([]);
    });
  });
});
