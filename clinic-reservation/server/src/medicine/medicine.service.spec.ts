import { Test, TestingModule } from '@nestjs/testing';

import { DeliveryStatus } from '@/models/Medicine';

import { MedicineSearchInput } from './medicine.input';
import { MedicineService } from './medicine.service';

/**
 * MedicineServiceのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. 医薬品配送情報の検索
 */
describe('MedicineService', () => {
  let service: MedicineService;

  /**
   * 各テストケース実行前の準備
   * - テストモジュールの構築
   * - サービスのインスタンス化
   */
  beforeEach(async () => {
    // テストモジュールの構築
    const module: TestingModule = await Test.createTestingModule({
      providers: [MedicineService],
    }).compile();

    service = module.get<MedicineService>(MedicineService);
  });

  // サービスが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * searchMedicineDeliveryメソッドのテスト
   */
  describe('searchMedicineDelivery', () => {
    /**
     * テストケース: 正常系 - 医薬品配送情報を検索できる
     * 条件: 検索条件を指定
     * 想定結果: 医薬品配送情報の配列が返される
     */
    it('should return medicine delivery information', async () => {
      // テストデータの準備
      const searchInput: MedicineSearchInput = {
        searchKey: 'test',
        examinationDate: '2023-01-01',
        medicalSpecialty: 'dermatology',
      };

      // メソッドの実行
      const result = await service.searchMedicineDelivery(searchInput);

      // 期待する結果の検証
      expect(result).toHaveLength(2);

      // 最初の結果を検証
      expect(result[0]).toHaveProperty('prescriptMedicines');
      expect(result[0].prescriptMedicines).toHaveLength(1);
      expect(result[0].prescriptMedicines[0].name).toBe('美白内服セットA');
      expect(result[0].prescriptMedicines[0].medicines).toHaveLength(3);
      expect(result[0].deliveryStatus).toBe(DeliveryStatus.shipping);
      expect(result[0].deliveryMethod).toBe('ゆうパケット');

      // 2番目の結果を検証
      expect(result[1]).toHaveProperty('prescriptMedicines');
      expect(result[1].prescriptMedicines).toHaveLength(2);
      expect(result[1].prescriptMedicines[0].name).toBe('マーベロン28');
      expect(result[1].prescriptMedicines[0].medicines).toBeNull();
      expect(result[1].deliveryStatus).toBe(DeliveryStatus.deliverd);
      expect(result[1].deliveryMethod).toBe('ゆうパケット');
    });

    /**
     * テストケース: 正常系 - 検索条件が空の場合
     * 条件: 空の検索条件を指定
     * 想定結果: すべての医薬品配送情報が返される
     */
    it('should return all medicine delivery information when search input is empty', async () => {
      // テストデータの準備
      const emptySearchInput: Partial<MedicineSearchInput> = {};

      // メソッドの実行
      const result = await service.searchMedicineDelivery(emptySearchInput);

      // 期待する結果の検証
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('deliveryStatus');
      expect(result[1]).toHaveProperty('deliveryStatus');
    });

    /**
     * テストケース: 正常系 - 配送ステータスの検証
     * 条件: 検索条件を指定
     * 想定結果: 指定した配送ステータスの医薬品配送情報が返される
     */
    it('should return medicine delivery with correct delivery status', async () => {
      // テストデータの準備
      const searchInput: MedicineSearchInput = {
        searchKey: 'test',
        examinationDate: null,
        medicalSpecialty: null,
      };

      // メソッドの実行
      const result = await service.searchMedicineDelivery(searchInput);

      // 期待する結果の検証
      expect(result[0].deliveryStatus).toBe(DeliveryStatus.shipping);
      expect(result[1].deliveryStatus).toBe(DeliveryStatus.deliverd);
    });

    /**
     * テストケース: 正常系 - 内部実装の確認
     * 条件: 実装が変更された場合
     * 想定結果: 適切なデフォルト値を返す
     */
    it('should handle implementation changes gracefully', async () => {
      // 現在のメソッドのモック
      const originalMethod = service.searchMedicineDelivery;

      // メソッドのモック化
      service.searchMedicineDelivery = jest
        .fn()
        .mockImplementation(async () => {
          return [
            {
              prescriptMedicines: [
                { name: 'テスト薬', time: '10日分', medicines: null },
              ],
              deliveryStatus: DeliveryStatus.inPrepare,
              deliveryMethod: 'テスト配送',
              deliveryPhone: '00000000000',
              deliveryDestination: 'テスト先',
            },
          ];
        });

      // 検索条件の準備
      const searchInput: Partial<MedicineSearchInput> = {
        searchKey: 'test',
      };

      // メソッドの実行
      const result = await service.searchMedicineDelivery(searchInput);

      // 期待する結果の検証
      expect(result).toHaveLength(1);
      expect(result[0].prescriptMedicines[0].name).toBe('テスト薬');
      expect(result[0].deliveryStatus).toBe(DeliveryStatus.inPrepare);

      // 元のメソッドに戻す
      service.searchMedicineDelivery = originalMethod;
    });
  });
});
