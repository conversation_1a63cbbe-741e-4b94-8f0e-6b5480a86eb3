import { Injectable } from '@nestjs/common';

import { DeliveryStatus, MedicineDelivery } from '@/models/Medicine';

import { MedicineSearchInput } from './medicine.input';

@Injectable()
export class MedicineService {
  async searchMedicineDelivery(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _query: Partial<MedicineSearchInput>,
  ): Promise<MedicineDelivery[]> {
    /**
     * TODO: Query from DB the medicine delivery list
     */

    const DEFAULT_PRESCIPT_MEDICINES_A = [
      {
        name: '美白内服セットA',
        time: '30日分',
        medicines: [
          {
            name: 'トラネキサム酸',
            frequency: '1日2回、1回1錠内服',
          },
          {
            name: 'シナール',
            frequency: '1日2回、1回1錠内服',
          },
          {
            name: 'ユベラ (カプセル)',
            frequency: '1日2回、1回1錠内服',
          },
        ],
      },
    ];

    const DEFAULT_PRESCIPT_MEDICINES_B = [
      { name: 'マーベロン28', time: '1シート (28日分)', medicines: null },
      { name: '低用量ピル', time: null, medicines: null },
    ];
    const DEFAULT_MEDICINE_DELIVERY_A = {
      prescriptMedicines: DEFAULT_PRESCIPT_MEDICINES_A,
      deliveryStatus: DeliveryStatus.shipping,
      deliveryMethod: 'ゆうパケット',
      deliveryPhone: '99999999999',
      deliveryDestination: 'ご自宅',
    };

    const DEFAULT_MEDICINE_DELIVERY_B = {
      prescriptMedicines: DEFAULT_PRESCIPT_MEDICINES_B,
      deliveryStatus: DeliveryStatus.deliverd,
      deliveryMethod: 'ゆうパケット',
      deliveryPhone: '99999999999',
      deliveryDestination: 'ご自宅',
    };

    return [DEFAULT_MEDICINE_DELIVERY_A, DEFAULT_MEDICINE_DELIVERY_B];
  }
}
