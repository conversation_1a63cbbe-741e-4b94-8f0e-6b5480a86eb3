import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AgreeService } from '@/agree/agree.service';
import { TokenService } from '@/common/service/token.service';

import { MedicineResolver } from './medicine.resolver';
import { MedicineService } from './medicine.service';

@Module({
  imports: [HttpModule],
  providers: [MedicineResolver, MedicineService, AgreeService, TokenService],
})
export class MedicineModule {}
