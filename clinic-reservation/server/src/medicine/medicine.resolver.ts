import { UseGuards } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';

import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { MedicineDelivery } from '@/models/Medicine';

import { MedicineSearchInput } from './medicine.input';
import { MedicineService } from './medicine.service';

@Resolver()
export class MedicineResolver {
  constructor(private readonly searchService: MedicineService) {}

  @Query(() => [MedicineDelivery])
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async searchMedicineDelivery(
    @Args({ name: 'query', type: () => MedicineSearchInput })
    query: MedicineSearchInput,
  ): Promise<MedicineDelivery[]> {
    return this.searchService.searchMedicineDelivery(query);
  }
}
