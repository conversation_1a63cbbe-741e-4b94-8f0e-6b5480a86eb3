import { HealthCheckError } from '@nestjs/terminus';
import { Test, TestingModule } from '@nestjs/testing';
import { ClusterHealthResponse } from '@opensearch-project/opensearch/api/types';

import { OpenSearchService } from '@/opensearch/opensearch.service';

import { OpenSearchHealthIndicator } from './opensearch.indicator';

/**
 * OpenSearchHealthIndicatorのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. OpenSearchクラスターのヘルスチェック
 */
describe('OpenSearchHealthIndicator', () => {
  let indicator: OpenSearchHealthIndicator;
  let openSearchService: OpenSearchService;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - インジケーターのインスタンス化
   */
  beforeEach(async () => {
    // OpenSearchServiceのモック
    const mockOpenSearchService = {
      getClusterHealth: jest.fn(),
    };

    // テストモジュールの構築
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OpenSearchHealthIndicator,
        {
          provide: OpenSearchService,
          useValue: mockOpenSearchService,
        },
      ],
    }).compile();

    indicator = module.get<OpenSearchHealthIndicator>(
      OpenSearchHealthIndicator,
    );
    openSearchService = module.get<OpenSearchService>(OpenSearchService);
  });

  // インジケーターが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(indicator).toBeDefined();
  });

  /**
   * isHealthyメソッドのテスト
   */
  describe('isHealthy', () => {
    /**
     * テストケース: 正常系 - クラスターのステータスが緑色（正常）
     * 条件: OpenSearchのクラスターステータスが'green'
     * 想定結果: ステータスがupのヘルスチェック結果が返される
     */
    it('should return up status when cluster status is green', async () => {
      // getClusterHealthの戻り値を設定
      jest.spyOn(openSearchService, 'getClusterHealth').mockResolvedValue({
        body: { status: 'green' },
      } as unknown as ClusterHealthResponse);

      // メソッドの実行
      const result = await indicator.isHealthy('open-search');

      // 期待する結果の検証
      expect(result).toEqual({
        'open-search': {
          status: 'up',
        },
      });
      expect(openSearchService.getClusterHealth).toHaveBeenCalled();
    });

    /**
     * テストケース: 正常系 - クラスターのステータスが黄色（警告）
     * 条件: OpenSearchのクラスターステータスが'yellow'
     * 想定結果: ステータスがupのヘルスチェック結果が返される（yellowは許容）
     */
    it('should return up status when cluster status is yellow', async () => {
      // getClusterHealthの戻り値を設定
      jest.spyOn(openSearchService, 'getClusterHealth').mockResolvedValue({
        body: { status: 'yellow' },
      } as unknown as ClusterHealthResponse);

      // メソッドの実行
      const result = await indicator.isHealthy('open-search');

      // 期待する結果の検証
      expect(result).toEqual({
        'open-search': {
          status: 'up',
        },
      });
      expect(openSearchService.getClusterHealth).toHaveBeenCalled();
    });

    /**
     * テストケース: 異常系 - クラスターのステータスが赤色（異常）
     * 条件: OpenSearchのクラスターステータスが'red'
     * 想定結果: HealthCheckErrorがスローされる
     */
    it('should throw HealthCheckError when cluster status is red', async () => {
      // getClusterHealthの戻り値を設定
      jest.spyOn(openSearchService, 'getClusterHealth').mockResolvedValue({
        body: { status: 'red' },
      } as unknown as ClusterHealthResponse);

      // メソッドの実行と結果の検証
      await expect(indicator.isHealthy('open-search')).rejects.toThrow(
        HealthCheckError,
      );

      try {
        await indicator.isHealthy('open-search');
      } catch (error) {
        if (error instanceof HealthCheckError) {
          expect(error.causes).toEqual({
            'open-search': {
              status: 'down',
              error: new Error('OpenSearch is not healthy'),
            },
          });
        }
      }
    });

    /**
     * テストケース: 異常系 - OpenSearchサービスの呼び出しが失敗
     * 条件: OpenSearchServiceのgetClusterHealth()がエラーをスロー
     * 想定結果: HealthCheckErrorがスローされる
     */
    it('should throw HealthCheckError when OpenSearch service call fails', async () => {
      // getClusterHealthがエラーをスローするように設定
      const serviceError = new Error('Connection failed');
      jest
        .spyOn(openSearchService, 'getClusterHealth')
        .mockRejectedValue(serviceError);

      // メソッドの実行と結果の検証
      await expect(indicator.isHealthy('open-search')).rejects.toThrow(
        HealthCheckError,
      );
      await expect(indicator.isHealthy('open-search')).rejects.toThrow(
        'OpenSearch health check failed',
      );

      try {
        await indicator.isHealthy('open-search');
      } catch (error) {
        if (error instanceof HealthCheckError) {
          expect(error.causes).toHaveProperty('open-search');
          expect(error.causes['open-search'].status).toBe('down');
          expect(error.causes['open-search'].error).toEqual(serviceError);
        }
      }
    });

    /**
     * テストケース: 異常系 - 取得件数が0件の場合
     * 条件: OpenSearchから返されるデータが空
     * 想定結果: 正常にハンドリングされステータスはupのまま
     */
    it('should handle empty response data correctly', async () => {
      // getClusterHealthの戻り値を設定（空のbody）
      jest.spyOn(openSearchService, 'getClusterHealth').mockResolvedValue({
        body: {},
      } as unknown as ClusterHealthResponse);

      // メソッドの実行と結果の検証
      await expect(indicator.isHealthy('open-search')).rejects.toThrow(
        HealthCheckError,
      );

      try {
        await indicator.isHealthy('open-search');
      } catch (error) {
        if (error instanceof HealthCheckError) {
          // エラーの詳細がOpenSearch is not healthyの場合とhealth check failedの両方を許容する
          expect(error.causes['open-search'].status).toBe('down');
        }
      }
    });
  });
});
