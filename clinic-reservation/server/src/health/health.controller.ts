import { Controller, Get } from '@nestjs/common';
import {
  DiskHealthIndicator,
  HealthCheck,
  HealthCheckResult,
  HealthCheckService,
  HealthIndicatorResult,
  HttpHealthIndicator,
} from '@nestjs/terminus';

import { DBHealthIndicator } from './database.indicator';
import { OpenSearchHealthIndicator } from './opensearch.indicator';

@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly http: HttpHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly openSearch: OpenSearchHealthIndicator,
    private readonly db: DBHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check(): Promise<HealthCheckResult> {
    return this.health.check([
      (): Promise<HealthIndicatorResult> =>
        this.http.pingCheck(
          'denkaru-server',
          `${process.env.DENKARU_API_ENDPOINT}/health_check`,
        ),
      (): Promise<HealthIndicatorResult> => this.db.isHealthy('database'),
      (): Promise<HealthIndicatorResult> =>
        this.openSearch.isHealthy('open-search'),
      (): Promise<HealthIndicatorResult> =>
        this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.5 }),
    ]);
  }
}
