import { Injectable } from '@nestjs/common';
import {
  HealthCheckError,
  HealthIndicator,
  HealthIndicatorResult,
} from '@nestjs/terminus';
import { Model } from 'objection';

import { ERROR_TYPE } from '@/common/constants/error-type';

@Injectable()
export class DBHealthIndicator extends HealthIndicator {
  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      await Model.knex().raw('SELECT 1');
      return this.getStatus(key, true);
    } catch {
      throw new HealthCheckError(
        'Database connection failed',
        this.getStatus(key, false, {
          message: ERROR_TYPE.INTERNAL_SERVER_ERROR,
        }),
      );
    }
  }
}
