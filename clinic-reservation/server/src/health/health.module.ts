import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';

import { OpenSearchService } from '@/opensearch/opensearch.service';

import { DBHealthIndicator } from './database.indicator';
import { HealthController } from './health.controller';
import { OpenSearchHealthIndicator } from './opensearch.indicator';

@Module({
  imports: [TerminusModule],
  controllers: [HealthController],
  providers: [DBHealthIndicator, OpenSearchHealthIndicator, OpenSearchService],
})
export class HealthModule {}
