import {
  DiskHealthIndicator,
  HealthCheckResult,
  HealthCheckService,
  HealthCheckStatus,
  HealthIndicatorFunction,
  HealthIndicatorResult,
  HttpHealthIndicator,
} from '@nestjs/terminus';
import { Test, TestingModule } from '@nestjs/testing';

import { DBHealthIndicator } from './database.indicator';
import { HealthController } from './health.controller';
import { OpenSearchHealthIndicator } from './opensearch.indicator';

/**
 * HealthControllerのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. システムの各種ヘルスチェック機能
 * 2. 複数の依存サービスの状態確認（DB、OpenSearch、HTTP、ディスク）
 */
describe('HealthController', () => {
  let controller: HealthController;
  let healthService: HealthCheckService;
  let httpHealthIndicator: HttpHealthIndicator;
  let diskHealthIndicator: DiskHealthIndicator;
  let openSearchHealthIndicator: OpenSearchHealthIndicator;
  let dbHealthIndicator: DBHealthIndicator;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - コントローラーのインスタンス化
   */
  beforeEach(async () => {
    // 環境変数の設定
    process.env.DENKARU_API_ENDPOINT = 'http://localhost:3000';

    // モックオブジェクトの作成
    const mockHealthService = {
      check: jest.fn(),
    };

    const mockHttpHealthIndicator = {
      pingCheck: jest.fn(),
    };

    const mockDiskHealthIndicator = {
      checkStorage: jest.fn(),
    };

    const mockOpenSearchHealthIndicator = {
      isHealthy: jest.fn(),
    };

    const mockDbHealthIndicator = {
      isHealthy: jest.fn(),
    };

    // テストモジュールの構築
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [
        {
          provide: HealthCheckService,
          useValue: mockHealthService,
        },
        {
          provide: HttpHealthIndicator,
          useValue: mockHttpHealthIndicator,
        },
        {
          provide: DiskHealthIndicator,
          useValue: mockDiskHealthIndicator,
        },
        {
          provide: OpenSearchHealthIndicator,
          useValue: mockOpenSearchHealthIndicator,
        },
        {
          provide: DBHealthIndicator,
          useValue: mockDbHealthIndicator,
        },
      ],
    }).compile();

    controller = module.get<HealthController>(HealthController);
    healthService = module.get<HealthCheckService>(HealthCheckService);
    httpHealthIndicator = module.get<HttpHealthIndicator>(HttpHealthIndicator);
    diskHealthIndicator = module.get<DiskHealthIndicator>(DiskHealthIndicator);
    openSearchHealthIndicator = module.get<OpenSearchHealthIndicator>(
      OpenSearchHealthIndicator,
    );
    dbHealthIndicator = module.get<DBHealthIndicator>(DBHealthIndicator);
  });

  afterEach(() => {
    // 環境変数のクリーンアップ
    delete process.env.DENKARU_API_ENDPOINT;
  });

  // コントローラーが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /**
   * checkメソッドのテスト
   */
  describe('check', () => {
    /**
     * テストケース: 正常系 - すべてのヘルスチェックが成功
     * 条件: 各ヘルスインジケーターが成功を返す
     * 想定結果: 全体のヘルスチェック結果が成功として返される
     */
    it('should return successful health check when all checks pass', async () => {
      // テストデータの準備
      const mockHealthCheckResult: HealthCheckResult = {
        status: 'ok' as HealthCheckStatus,
        info: {
          'denkaru-server': { status: 'up' },
          database: { status: 'up' },
          'open-search': { status: 'up' },
          storage: { status: 'up' },
        },
        error: {},
        details: {
          'denkaru-server': { status: 'up' },
          database: { status: 'up' },
          'open-search': { status: 'up' },
          storage: { status: 'up' },
        },
      };

      // モックの戻り値を設定
      jest
        .spyOn(healthService, 'check')
        .mockResolvedValue(mockHealthCheckResult);

      // メソッドの実行
      const result = await controller.check();

      // 期待する結果の検証
      expect(result).toEqual(mockHealthCheckResult);
      expect(healthService.check).toHaveBeenCalledWith(expect.any(Array));
    });

    /**
     * テストケース: 正常系 - 各ヘルスインジケーターが正しく呼び出される
     * 条件: controller.check()メソッドを呼び出す
     * 想定結果: 各種ヘルスインジケーターが適切なパラメータで呼び出される
     */
    it('should call all health indicators with correct parameters', async () => {
      // healthServiceのcheck関数の実装をカスタマイズし、渡されたヘルスインジケーターを実行
      jest
        .spyOn(healthService, 'check')
        .mockImplementation(async (indicators: HealthIndicatorFunction[]) => {
          // 渡された関数を実行
          for (const indicator of indicators) {
            await indicator();
          }
          return {
            status: 'ok' as HealthCheckStatus,
            info: {},
            error: {},
            details: {},
          };
        });

      // モックの戻り値を設定
      jest.spyOn(httpHealthIndicator, 'pingCheck').mockResolvedValue({
        'denkaru-server': { status: 'up' },
      });
      jest.spyOn(dbHealthIndicator, 'isHealthy').mockResolvedValue({
        database: { status: 'up' },
      });
      jest.spyOn(openSearchHealthIndicator, 'isHealthy').mockResolvedValue({
        'open-search': { status: 'up' },
      });
      jest.spyOn(diskHealthIndicator, 'checkStorage').mockResolvedValue({
        storage: { status: 'up' },
      });

      // メソッドの実行
      await controller.check();

      // 各ヘルスインジケーターが適切なパラメータで呼び出されたことを検証
      expect(httpHealthIndicator.pingCheck).toHaveBeenCalledWith(
        'denkaru-server',
        'http://localhost:3000/health_check',
      );
      expect(dbHealthIndicator.isHealthy).toHaveBeenCalledWith('database');
      expect(openSearchHealthIndicator.isHealthy).toHaveBeenCalledWith(
        'open-search',
      );
      expect(diskHealthIndicator.checkStorage).toHaveBeenCalledWith('storage', {
        path: '/',
        thresholdPercent: 0.5,
      });
    });

    /**
     * テストケース: 異常系 - HTTPヘルスチェックが失敗
     * 条件: HTTPヘルスインジケーターが例外をスロー
     * 想定結果: ヘルスチェックサービスにエラー情報が渡される
     */
    it('should handle HTTP health check failure', async () => {
      // healthServiceのcheck関数の実装をカスタマイズし、エラーハンドリングをテスト
      jest
        .spyOn(healthService, 'check')
        .mockImplementation(async (indicators: HealthIndicatorFunction[]) => {
          try {
            // 最初のインジケーター（HTTP）を実行
            await indicators[0]();
          } catch {
            // エラーをキャッチして結果に含める
            const errorResult: HealthIndicatorResult = {
              'denkaru-server': {
                status: 'down',
                message: 'Connection refused',
              },
            };
            return {
              status: 'error' as HealthCheckStatus,
              info: {},
              error: errorResult,
              details: errorResult,
            };
          }
          return {
            status: 'ok' as HealthCheckStatus,
            info: {},
            error: {},
            details: {},
          };
        });

      // HTTPインジケーターが例外をスローするようにモック
      jest
        .spyOn(httpHealthIndicator, 'pingCheck')
        .mockRejectedValue(new Error('Connection refused'));

      // メソッドの実行
      const result = await controller.check();

      // 期待する結果の検証
      expect(result.status).toBe('error');
      expect(result.error).toBeDefined();
      expect(result.error?.['denkaru-server'].status).toBe('down');
    });
  });
});
