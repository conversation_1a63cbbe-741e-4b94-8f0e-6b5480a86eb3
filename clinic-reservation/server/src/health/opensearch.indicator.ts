import { Injectable } from '@nestjs/common';
import {
  HealthCheckError,
  HealthIndicator,
  HealthIndicatorResult,
} from '@nestjs/terminus';

import { OpenSearchService } from '@/opensearch/opensearch.service';

@Injectable()
export class OpenSearchHealthIndicator extends HealthIndicator {
  constructor(private readonly openSearchService: OpenSearchService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const health = await this.openSearchService.getClusterHealth();
      const isHealthy = ['green', 'yellow'].includes(health.body.status);

      if (isHealthy) {
        return this.getStatus(key, true);
      }

      throw new HealthCheckError(
        'OpenSearch is not healthy',
        this.getStatus(key, false, health),
      );
    } catch (error) {
      throw new HealthCheckError(
        'OpenSearch health check failed',
        this.getStatus(key, false, { error }),
      );
    }
  }
}
