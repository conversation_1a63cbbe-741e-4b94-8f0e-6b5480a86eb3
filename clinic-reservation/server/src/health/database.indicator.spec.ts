import { HealthCheckError } from '@nestjs/terminus';
import { Test, TestingModule } from '@nestjs/testing';
import { Model } from 'objection';

import { ERROR_TYPE } from '@/common/constants/error-type';

import { DBHealthIndicator } from './database.indicator';

/**
 * DBHealthIndicatorのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. データベース接続のヘルスチェック
 */
describe('DBHealthIndicator', () => {
  let indicator: DBHealthIndicator;

  /**
   * 各テストケース実行前の準備
   * - テストモジュールの構築
   * - インジケーターのインスタンス化
   */
  beforeEach(async () => {
    // テストモジュールの構築
    const module: TestingModule = await Test.createTestingModule({
      providers: [DBHealthIndicator],
    }).compile();

    indicator = module.get<DBHealthIndicator>(DBHealthIndicator);

    // Model.knex()をモック
    Model.knex = jest.fn().mockReturnValue({
      raw: jest.fn(),
    });
  });

  // インジケーターが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(indicator).toBeDefined();
  });

  /**
   * isHealthyメソッドのテスト
   */
  describe('isHealthy', () => {
    /**
     * テストケース: 正常系 - データベース接続が成功
     * 条件: データベースが正常に接続可能な状態
     * 想定結果: ステータスがupのヘルスチェック結果が返される
     */
    it('should return up status when database connection is successful', async () => {
      // Model.knex().rawのモック
      const mockRaw = jest.fn().mockResolvedValue([{ '1': 1 }]);
      (Model.knex() as unknown as { raw: jest.Mock }).raw = mockRaw;

      // メソッドの実行
      const result = await indicator.isHealthy('database');

      // 期待する結果の検証
      expect(result).toEqual({
        database: {
          status: 'up',
        },
      });
      expect(mockRaw).toHaveBeenCalledWith('SELECT 1');
    });

    /**
     * テストケース: 異常系 - データベース接続が失敗
     * 条件: データベースに接続できない状態
     * 想定結果: HealthCheckErrorがスローされ、ステータスがdownの結果が含まれる
     */
    it('should throw HealthCheckError when database connection fails', async () => {
      // Model.knex().rawのモック（エラーをスロー）
      const mockRaw = jest
        .fn()
        .mockRejectedValue(new Error('Connection failed'));
      (Model.knex() as unknown as { raw: jest.Mock }).raw = mockRaw;

      // メソッドの実行と結果の検証
      await expect(indicator.isHealthy('database')).rejects.toThrow(
        HealthCheckError,
      );

      try {
        await indicator.isHealthy('database');
      } catch (error) {
        if (error instanceof HealthCheckError) {
          expect(error.message).toBe('Database connection failed');
          expect(error.causes).toEqual({
            database: {
              status: 'down',
              message: ERROR_TYPE.INTERNAL_SERVER_ERROR,
            },
          });
        }
      }
    });
  });
});
