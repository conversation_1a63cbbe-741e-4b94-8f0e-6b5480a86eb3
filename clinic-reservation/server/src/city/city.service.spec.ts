import { Test, TestingModule } from '@nestjs/testing';
import { GraphQLError } from 'graphql';
import { ModelClass } from 'objection';

import { ERROR_TYPE } from '@/common/constants/error-type';
import { IS_DELETED } from '@/common/constants/master-type';
import { City } from '@/models/City';

import { CityService } from './city.service';

/**
 * CityServiceのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. 都道府県名に基づく市区町村の取得
 */
describe('CityService', () => {
  let service: CityService;
  let mockCity: jest.Mocked<Partial<ModelClass<City>>>;

  // クエリビルダーのモック
  const mockQueryBuilder = {
    query: jest.fn().mockReturnThis(),
    joinRelated: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
  };

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - サービスのインスタンス化
   */
  beforeEach(async () => {
    // Cityモデルのモック作成
    mockCity = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
    } as jest.Mocked<Partial<ModelClass<City>>>;

    // テストモジュールの設定
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CityService,
        {
          provide: City.name,
          useValue: mockCity,
        },
      ],
    }).compile();

    service = module.get<CityService>(CityService);
  });

  // サービスが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * getCitiesメソッドのテスト
   */
  describe('getCities', () => {
    /**
     * Given: 有効な都道府県名を指定
     * When: getCitiesメソッドを呼び出す
     * Then: 指定した都道府県に属する市区町村のリストが返される
     */
    it('正常系 - 都道府県名に基づいて市区町村のリストを取得できる', async () => {
      // テストデータの準備
      const prefName = '東京都';
      const mockCities = [
        { cityId: 1, name: '千代田区', groupCode: 'A' },
        { cityId: 2, name: '中央区', groupCode: 'A' },
        { cityId: 3, name: '港区', groupCode: 'B' },
      ];

      // モックの戻り値を設定
      mockQueryBuilder.orderBy.mockResolvedValue(mockCities);

      // メソッドの実行
      const result = await service.getCities(prefName);

      // 期待する結果の検証
      expect(result).toEqual(mockCities);
      expect(mockCity.query).toHaveBeenCalled();
      expect(mockQueryBuilder.joinRelated).toHaveBeenCalledWith('prefecture');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'prefecture.name',
        prefName,
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'portal_m_city.isDeleted',
        IS_DELETED.FALSE,
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('groupCode', 'ASC');
    });

    /**
     * Given: 有効な都道府県名を指定したが、該当する市区町村が存在しない
     * When: getCitiesメソッドを呼び出す
     * Then: 空の配列が返される
     */
    it('正常系 - 該当する市区町村が存在しない場合は空配列を返す', async () => {
      // テストデータの準備
      const prefName = '存在しない県';
      const mockCities: City[] = [];

      // モックの戻り値を設定（空配列）
      mockQueryBuilder.orderBy.mockResolvedValue(mockCities);

      // メソッドの実行
      const result = await service.getCities(prefName);

      // 期待する結果の検証
      expect(result).toEqual([]);
      expect(result.length).toBe(0);
      expect(mockCity.query).toHaveBeenCalled();
      expect(mockQueryBuilder.joinRelated).toHaveBeenCalledWith('prefecture');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'prefecture.name',
        prefName,
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'portal_m_city.isDeleted',
        IS_DELETED.FALSE,
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('groupCode', 'ASC');
    });

    /**
     * Given: データベース操作中にエラーが発生する状況
     * When: getCitiesメソッドを呼び出す
     * Then: GraphQLErrorがスローされる
     */
    it('異常系 - データベースエラー時にGraphQLErrorがスローされる', async () => {
      // テストデータの準備
      const prefName = '東京都';

      // モックがエラーをスローするように設定
      mockQueryBuilder.orderBy.mockRejectedValue(new Error('Database error'));

      // メソッドの実行と結果の検証
      await expect(service.getCities(prefName)).rejects.toThrow(GraphQLError);
      await expect(service.getCities(prefName)).rejects.toThrow(
        ERROR_TYPE.INTERNAL_SERVER_ERROR,
      );
    });

    /**
     * Given: 無効なパラメータ（空文字列）を渡す
     * When: getCitiesメソッドを呼び出す
     * Then: 適切なエラーメッセージを含むGraphQLErrorがスローされる
     */
    it('異常系 - 無効なパラメータでエラーハンドリングが正しく行われる', async () => {
      // テストデータの準備（無効なパラメータ）
      const prefName = '';

      // モックがエラーをスローするように設定
      mockQueryBuilder.orderBy.mockRejectedValue(
        new Error('Invalid parameters'),
      );

      // メソッドの実行と結果の検証
      await expect(service.getCities(prefName)).rejects.toThrow(GraphQLError);
      await expect(service.getCities(prefName)).rejects.toThrow(
        ERROR_TYPE.INTERNAL_SERVER_ERROR,
      );
    });

    /**
     * Given: ネットワークタイムアウトが発生する状況
     * When: getCitiesメソッドを呼び出す
     * Then: 適切なエラーメッセージを含むGraphQLErrorがスローされる
     */
    it('異常系 - ネットワークタイムアウト時に適切なエラーがスローされる', async () => {
      // テストデータの準備
      const prefName = '東京都';

      // ネットワークタイムアウトエラーをシミュレート
      const timeoutError = new Error('Network timeout');
      timeoutError.name = 'TimeoutError';
      mockQueryBuilder.orderBy.mockRejectedValue(timeoutError);

      // メソッドの実行と結果の検証
      await expect(service.getCities(prefName)).rejects.toThrow(GraphQLError);
      await expect(service.getCities(prefName)).rejects.toThrow(
        ERROR_TYPE.INTERNAL_SERVER_ERROR,
      );
    });
  });
});
