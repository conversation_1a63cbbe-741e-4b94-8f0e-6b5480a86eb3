import { Args, Query, Resolver } from '@nestjs/graphql';

import { City } from '@/models/City';

import { CityService } from './city.service';

@Resolver()
export class CityResolver {
  constructor(private cityService: CityService) {}

  @Query(() => [City])
  async getCities(
    @Args('prefName', { type: () => String }) prefName: string,
  ): Promise<City[]> {
    return await this.cityService.getCities(prefName);
  }
}
