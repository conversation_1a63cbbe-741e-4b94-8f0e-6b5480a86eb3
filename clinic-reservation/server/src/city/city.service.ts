import { Inject, Injectable } from '@nestjs/common';
import { GraphQLError } from 'graphql';
import { ModelClass } from 'objection';

import { ERROR_TYPE } from '@/common/constants/error-type';
import { IS_DELETED } from '@/common/constants/master-type';
import { City } from '@/models/City';

@Injectable()
export class CityService {
  @Inject(City.name)
  private city: ModelClass<City>;

  async getCities(prefName: string): Promise<City[]> {
    try {
      return await this.city
        .query()
        .joinRelated('prefecture')
        .where('prefecture.name', prefName)
        .andWhere('portal_m_city.isDeleted', IS_DELETED.FALSE)
        .orderBy('groupCode', 'ASC');
    } catch {
      throw new GraphQLError(ERROR_TYPE.INTERNAL_SERVER_ERROR, {
        extensions: {
          code: ERROR_TYPE.INTERNAL_SERVER_ERROR,
        },
      });
    }
  }
}
