import { UseGuards } from '@nestjs/common';
import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';

import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { SessionId } from '@/common/decorators/session-id.decorator';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { CustomerLoginGuard } from '@/common/guards/customer/customer-login.guard';
import { Customer } from '@/models/Customer';

import { FavoriteHospitalInput } from './hospital-favorite.input';
import { PaginatedHospitalFavorite } from './hospital-favorite.output';
import { CategoryInfo } from './hospital-favorite.output';
import { HospitalFavoriteService } from './hospital-favorite.service';

@Resolver()
export class HospitalFavoriteResolver {
  constructor(
    private readonly hospitalFavoriteService: HospitalFavoriteService,
  ) {}

  @Mutation(() => Boolean)
  @UseGuards(CustomerLoginGuard)
  async toggleFavoriteHospital(
    @CurrentCustomer() loginCustomer: Customer,
    @SessionId() sessionId: string,
    @Args('input') input: FavoriteHospitalInput,
  ): Promise<boolean> {
    return this.hospitalFavoriteService.toggleFavoriteHospital(
      input,
      loginCustomer?.customerId,
      sessionId,
    );
  }

  @Query(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getHospitalFavorite(
    @CurrentCustomer() loginCustomer: Customer,
    @Args('hospitalId', { type: () => Number, nullable: true })
    hospitalId: number,
    @Args('scuelId', { type: () => String, nullable: true }) scuelId: string,
  ): Promise<boolean> {
    const favorite = await this.hospitalFavoriteService.getHospitalFavorite(
      loginCustomer.customerId,
      hospitalId,
      scuelId,
    );
    return !!favorite;
  }

  @Query(() => PaginatedHospitalFavorite)
  @UseGuards(CustomerLoginGuard)
  async getHospitalFavorites(
    @CurrentCustomer() loginCustomer: Customer,
    @SessionId() sessionId: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 })
    limit?: number,
    @Args('lastId', { type: () => Int, nullable: true })
    lastId?: number,
    @Args('treatmentCategoryIds', {
      type: () => [Int],
      nullable: true,
    })
    treatmentCategoryIds?: number[],
    @Args('serviceIds', { type: () => [Int], nullable: true })
    serviceIds?: number[],
  ): Promise<PaginatedHospitalFavorite> {
    return this.hospitalFavoriteService.getHospitalFavorites(
      limit,
      lastId,
      treatmentCategoryIds,
      serviceIds,
      loginCustomer.customerId,
      sessionId,
    );
  }

  @Query(() => [CategoryInfo])
  @UseGuards(CustomerLoginGuard)
  async getAllCategories(
    @CurrentCustomer() loginCustomer: Customer,
    @SessionId() sessionId: string,
  ): Promise<CategoryInfo[]> {
    return this.hospitalFavoriteService.getAllCategories(
      loginCustomer.customerId,
      sessionId,
    );
  }

  @Mutation(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async updateHospitalFavorite(
    @CurrentCustomer() loginCustomer: Customer,
    @SessionId() sessionId: string,
  ): Promise<boolean> {
    if (!loginCustomer?.customerId || !sessionId) {
      return false;
    }

    return this.hospitalFavoriteService.updateHospitalFavorite(
      loginCustomer.customerId,
      sessionId,
    );
  }
}
