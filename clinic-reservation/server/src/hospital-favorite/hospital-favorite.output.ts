import { Field, Int, ObjectType } from '@nestjs/graphql';

import { HospitalFavorite } from '@/models/HospitalFavorite';

@ObjectType()
export class PaginationMetaData {
  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  lastId: number;
}

@ObjectType()
export class PaginatedHospitalFavorite {
  @Field(() => [HospitalFavorite])
  result: HospitalFavorite[];

  @Field(() => PaginationMetaData)
  metaData: PaginationMetaData;
}

@ObjectType()
export class FavoriteImportBusinessTime {
  @Field(() => String)
  serviceDay: string;

  @Field(() => String)
  earliestStartTime: string;

  @Field(() => String)
  latestEndTime: string;
}

@ObjectType()
export class CategoryInfo {
  @Field(() => Int, { nullable: true })
  treatmentCategoryId?: number;

  @Field(() => Int, { nullable: true })
  importServiceId?: number;

  @Field()
  name: string;
}
