import { Inject, Injectable } from '@nestjs/common';
import { GraphQLError } from 'graphql';
import { ModelClass, raw } from 'objection';

import { ERROR_MSG, ERROR_TYPE } from '@/common/constants/error-type';
import { IS_DELETED } from '@/common/constants/master-type';
import { HospitalFavorite } from '@/models/HospitalFavorite';
import { ImportHospital } from '@/models/ImportHospital';
import { ImportOperatingHour } from '@/models/ImportOperatingHour';
import { PortalHospital } from '@/models/PortalHospital';

import { FavoriteHospitalInput } from './hospital-favorite.input';
import { PaginatedHospitalFavorite } from './hospital-favorite.output';

interface CategoryInfo {
  treatmentCategoryId?: number;
  importServiceId?: number;
  name: string;
}

@Injectable()
export class HospitalFavoriteService {
  constructor(
    @Inject(HospitalFavorite.name)
    private hospitalFavorite: ModelClass<HospitalFavorite>,
    @Inject(ImportOperatingHour.name)
    private importOperatingHour: ModelClass<ImportOperatingHour>,
    @Inject(PortalHospital.name)
    private hospital: ModelClass<PortalHospital>,
    @Inject(ImportHospital.name)
    private importHospital: ModelClass<ImportHospital>,
  ) {}

  async getHospitalFavorite(
    customerId: number,
    hospitalId?: number,
    scuelId?: string,
  ): Promise<HospitalFavorite | null> {
    if (!customerId) return null;

    const where = {
      customerId,
      ...(scuelId ? { scuelId } : { portalHospitalId: hospitalId }),
    };

    const favorite = await this.hospitalFavorite.query().where(where).first();

    return favorite || null;
  }

  async toggleFavoriteHospital(
    input: FavoriteHospitalInput,
    customerId?: number,
    sessionId?: string,
  ): Promise<boolean> {
    if (!customerId && !sessionId) {
      throw new GraphQLError(ERROR_TYPE.INVALID_HOSPITAL_FAVORITE_SESSION_ID, {
        extensions: {
          code: ERROR_TYPE.INVALID_HOSPITAL_FAVORITE_SESSION_ID,
          userMessage: ERROR_MSG.INVALID_HOSPITAL_FAVORITE_SESSION_ID,
        },
      });
    }

    if (input.scuelId) {
      const importHospital = await this.importHospital
        .query()
        .where('scuelId', input.scuelId)
        .andWhere('isDeleted', IS_DELETED.FALSE)
        .first();
      if (!importHospital) {
        throw new GraphQLError(ERROR_TYPE.HOSPITAL_NOT_FOUND, {
          extensions: {
            code: ERROR_TYPE.HOSPITAL_NOT_FOUND,
            userMessage: ERROR_MSG.HOSPITAL_NOT_FOUND,
          },
        });
      }
    } else if (input.hospitalId) {
      const hospital = await this.hospital
        .query()
        .where('hospitalId', input.hospitalId)
        .andWhere('isDeleted', IS_DELETED.FALSE)
        .andWhere('isActive', true)
        .first();
      if (!hospital) {
        throw new GraphQLError(ERROR_TYPE.HOSPITAL_NOT_FOUND, {
          extensions: {
            code: ERROR_TYPE.HOSPITAL_NOT_FOUND,
            userMessage: ERROR_MSG.HOSPITAL_NOT_FOUND,
          },
        });
      }
    }

    let where = {};
    if (customerId) {
      where = {
        customerId,
        ...(input.scuelId
          ? { scuelId: input.scuelId }
          : { portalHospitalId: input.hospitalId }),
      };
    } else if (sessionId) {
      where = {
        sessionId,
        ...(input.scuelId
          ? { scuelId: input.scuelId }
          : { portalHospitalId: input.hospitalId }),
      };
    }

    const existingFavorite = await this.hospitalFavorite
      .query()
      .where(where)
      .first();

    if (existingFavorite) {
      if (input.isFavorite) {
        return true;
      } else {
        await this.hospitalFavorite.query().where(where).delete();
        return false;
      }
    } else {
      if (input.isFavorite) {
        await this.hospitalFavorite.query().insert(where);
        return true;
      } else {
        return false;
      }
    }
  }

  async getFavoriteHospitalsByIds(
    hospitalIds: number[],
    scuelIds: string[],
    customerId?: number,
    sessionId?: string,
  ): Promise<HospitalFavorite[]> {
    if (!customerId && !sessionId) {
      return [];
    }

    let where = {};
    if (customerId) {
      where = {
        customerId,
      };
    } else if (sessionId) {
      where = {
        sessionId,
      };
    } else {
      return [];
    }
    const favorites = await this.hospitalFavorite
      .query()
      .where(where)
      .whereIn(
        hospitalIds.length > 0 ? 'portalHospitalId' : 'scuelId',
        hospitalIds.length > 0 ? hospitalIds : scuelIds,
      );

    return favorites;
  }

  async getHospitalFavorites(
    limit: number = 10,
    lastId?: number,
    treatmentCategoryIds?: number[],
    serviceIds?: number[],
    customerId?: number,
    sessionId?: string,
  ): Promise<PaginatedHospitalFavorite> {
    if (!customerId && !sessionId) {
      return {
        result: [],
        metaData: {
          limit,
          total: 0,
          lastId: -1,
        },
      };
    }

    let where = {};
    if (customerId) {
      where = {
        customerId,
      };
    } else if (sessionId) {
      where = {
        sessionId,
      };
    }
    const query = this.hospitalFavorite
      .query()
      .where(where)
      .where('portal_hospital_favorite.isDeleted', IS_DELETED.FALSE)
      .where((builder) => {
        if (treatmentCategoryIds && treatmentCategoryIds.length > 0) {
          builder.whereExists(
            this.hospitalFavorite
              .relatedQuery('portalHospital')
              .where('isActive', true)
              .where('isDeleted', IS_DELETED.FALSE)
              .whereExists(function () {
                this.select('m_treatment_category.name')
                  .from('m_treatment_category')
                  .whereIn(
                    'm_treatment_category.treatment_category_id',
                    treatmentCategoryIds,
                  )
                  .where('m_treatment_category.is_deleted', IS_DELETED.FALSE)
                  .whereExists(function () {
                    this.select('ka_mst.hp_id')
                      .from('ka_mst')
                      .whereRaw('ka_mst.ka_name = m_treatment_category.name')
                      .whereRaw('ka_mst.hp_id = portal_hospital.hp_inf_id')
                      .where('ka_mst.is_deleted', IS_DELETED.FALSE);
                  });
              }),
          );
        } else {
          builder.whereExists(
            this.hospitalFavorite
              .relatedQuery('portalHospital')
              .where('isActive', true)
              .where('isDeleted', IS_DELETED.FALSE),
          );
        }

        if (serviceIds && serviceIds.length > 0) {
          builder.orWhereExists(
            this.hospitalFavorite
              .relatedQuery('importHospital')
              .where('isDeleted', IS_DELETED.FALSE)
              .whereExists(function () {
                this.select('portal_m_import_service.scuel_id')
                  .from('portal_m_import_service')
                  .whereIn('import_service_id', serviceIds)
                  .whereRaw(
                    'portal_m_import_service.scuel_id = import_hospital.scuel_id',
                  );
              }),
          );
        } else {
          builder.orWhereExists(
            this.hospitalFavorite
              .relatedQuery('importHospital')
              .where('isDeleted', IS_DELETED.FALSE),
          );
        }
      })
      .orderBy('hospitalFavoriteId', 'desc')
      .withGraphFetched({
        portalHospital: {
          treatmentCategories: true,
          stations: {
            stationDetail: {
              railline: true,
            },
          },
          businessTimes: true,
          pictures: {
            pictureDetail: true,
          },
          tags: true,
        },
        importHospital: {
          tags: true,
          services: true,
          reservations: true,
          cashless: true,
          carpark: true,
          barrierFree: true,
        },
      });

    if (lastId) {
      query.where('hospitalFavoriteId', '<', lastId);
    }

    const totalQuery = this.hospitalFavorite
      .query()
      .where(where)
      .where('portal_hospital_favorite.isDeleted', IS_DELETED.FALSE)
      .where((builder) => {
        if (treatmentCategoryIds && treatmentCategoryIds.length > 0) {
          builder.whereExists(
            this.hospitalFavorite
              .relatedQuery('portalHospital')
              .where('isActive', true)
              .where('isDeleted', IS_DELETED.FALSE)
              .whereExists(function () {
                this.select('m_treatment_category.name')
                  .from('m_treatment_category')
                  .whereIn(
                    'm_treatment_category.treatment_category_id',
                    treatmentCategoryIds,
                  )
                  .where('m_treatment_category.is_deleted', IS_DELETED.FALSE)
                  .whereExists(function () {
                    this.select('ka_mst.hp_id')
                      .from('ka_mst')
                      .whereRaw('ka_mst.ka_name = m_treatment_category.name')
                      .whereRaw('ka_mst.hp_id = portal_hospital.hp_inf_id')
                      .where('ka_mst.is_deleted', IS_DELETED.FALSE);
                  });
              }),
          );
        } else {
          builder.whereExists(
            this.hospitalFavorite
              .relatedQuery('portalHospital')
              .where('isActive', true)
              .where('isDeleted', IS_DELETED.FALSE),
          );
        }

        if (serviceIds && serviceIds.length > 0) {
          builder.orWhereExists(
            this.hospitalFavorite
              .relatedQuery('importHospital')
              .where('isDeleted', IS_DELETED.FALSE)
              .whereExists(function () {
                this.select('portal_m_import_service.scuel_id')
                  .from('portal_m_import_service')
                  .whereIn('import_service_id', serviceIds)
                  .whereRaw(
                    'portal_m_import_service.scuel_id = import_hospital.scuel_id',
                  );
              }),
          );
        } else {
          builder.orWhereExists(
            this.hospitalFavorite
              .relatedQuery('importHospital')
              .where('isDeleted', IS_DELETED.FALSE),
          );
        }
      });

    const [favorites, total] = await Promise.all([
      query.limit(limit),
      totalQuery.resultSize(),
    ]);

    const promises = favorites.map(async (favorite) => {
      if (favorite.portalHospital?.pictures?.length) {
        favorite.portalHospital.pictures[0].pictureDetail.filepath = `${process.env.APP_URL}/${favorite.portalHospital.pictures[0].pictureDetail.filepath}`;
        favorite.portalHospital.pictures = [
          favorite.portalHospital.pictures[0],
        ];
      }

      if (favorite.importHospital?.scuelId) {
        const operatingHours = await this.importOperatingHour
          .query()
          .select('serviceDay')
          .min('start_time as earliest_start_time')
          .max('end_time as latest_end_time')
          .where(
            'portal_m_import_operating_hour.scuelId',
            favorite.importHospital.scuelId,
          )
          .groupBy('serviceDay');

        favorite.importHospital.importBusinessTimes = operatingHours.map(
          (opH) => ({
            serviceDay: opH.serviceDay,
            earliestStartTime: opH.earliestStartTime,
            latestEndTime: opH.latestEndTime,
          }),
        );
      }
      return favorite;
    });

    await Promise.allSettled(promises);

    const nextLastId =
      favorites.length > 0
        ? favorites[favorites.length - 1].hospitalFavoriteId
        : -1;

    return {
      result: favorites,
      metaData: {
        limit,
        total,
        lastId: nextLastId,
      },
    };
  }

  async getAllCategories(
    customerId?: number,
    sessionId?: string,
  ): Promise<CategoryInfo[]> {
    if (!customerId && !sessionId) {
      return [];
    }

    let where = {};
    if (customerId) {
      where = {
        customerId,
      };
    } else if (sessionId) {
      where = {
        sessionId,
      };
    }

    const favorites = await this.hospitalFavorite
      .query()
      .where(where)
      .where('portal_hospital_favorite.isDeleted', IS_DELETED.FALSE)
      .orderBy('hospitalFavoriteId', 'desc')
      .withGraphFetched({
        portalHospital: {
          treatmentCategories: true,
        },
        importHospital: {
          services: true,
        },
      })
      .modifyGraph('portalHospital', (builder) => {
        builder.where('isActive', true).where('isDeleted', IS_DELETED.FALSE);
      })
      .modifyGraph('importHospital', (builder) => {
        builder.where('isDeleted', IS_DELETED.FALSE);
      });

    const categories: CategoryInfo[] = [];

    favorites.forEach((favorite) => {
      if (favorite.portalHospital?.treatmentCategories) {
        favorite.portalHospital.treatmentCategories.forEach((category) => {
          categories.push({
            treatmentCategoryId: category.treatmentCategoryId,
            name: category.name,
          });
        });
      }

      if (favorite.importHospital?.services) {
        favorite.importHospital.services.forEach((service) => {
          categories.push({
            importServiceId: service.importServiceId,
            name: service.typeName,
          });
        });
      }
    });

    return categories;
  }

  async updateHospitalFavorite(
    customerId: number,
    sessionId: string,
  ): Promise<boolean> {
    const sessionFavorites = await this.hospitalFavorite
      .query()
      .where({ sessionId });

    if (!sessionFavorites.length) {
      return true;
    }

    const trx = await this.hospitalFavorite.startTransaction();

    try {
      const portalFavorites = sessionFavorites.filter(
        (item) => item.portalHospitalId,
      );
      const scuelFavorites = sessionFavorites.filter((item) => item.scuelId);

      const portalIds = portalFavorites.map((item) => item.portalHospitalId);
      const scuelIds = scuelFavorites.map((item) => item.scuelId);

      const [existingPortalRecords, existingScuelRecords] = await Promise.all([
        portalIds.length > 0
          ? this.hospitalFavorite
              .query(trx)
              .where({ customerId, sessionId: null })
              .whereIn('portalHospitalId', portalIds)
          : [],
        scuelIds.length > 0
          ? this.hospitalFavorite
              .query(trx)
              .where({ customerId, sessionId: null })
              .whereIn('scuelId', scuelIds)
          : [],
      ]);

      const recordsToDelete = [
        ...existingPortalRecords,
        ...existingScuelRecords,
      ];

      if (recordsToDelete.length > 0) {
        await this.hospitalFavorite
          .query(trx)
          .whereIn(
            'hospitalFavoriteId',
            recordsToDelete.map((item) => item.hospitalFavoriteId),
          )
          .delete();
      }

      await Promise.all(
        sessionFavorites.map((item) =>
          this.hospitalFavorite
            .query(trx)
            .findById(item.hospitalFavoriteId)
            .patch({
              customerId,
              sessionId: raw('NULL'),
            }),
        ),
      );

      await trx.commit();
      return true;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
