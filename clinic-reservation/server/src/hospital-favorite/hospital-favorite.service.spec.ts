import { Test, TestingModule } from '@nestjs/testing';
import { GraphQLError } from 'graphql';

import { ERROR_MSG, ERROR_TYPE } from '@/common/constants/error-type';
import { HospitalFavorite } from '@/models/HospitalFavorite';
import { ImportHospital } from '@/models/ImportHospital';
import { ImportOperatingHour } from '@/models/ImportOperatingHour';
import { PortalHospital } from '@/models/PortalHospital';

import { FavoriteHospitalInput } from './hospital-favorite.input';
import { HospitalFavoriteService } from './hospital-favorite.service';

describe('HospitalFavoriteService', () => {
  let service: HospitalFavoriteService;

  const mockHospitalFavorite = {
    query: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    first: jest.fn(),
    insert: jest.fn(),
    delete: jest.fn(),
    whereIn: jest.fn(),
    relatedQuery: jest.fn(),
  };

  const mockImportHospital = {
    query: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    first: jest.fn(),
  };

  const mockHospital = {
    query: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    first: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HospitalFavoriteService,
        {
          provide: HospitalFavorite.name,
          useValue: mockHospitalFavorite,
        },
        {
          provide: ImportOperatingHour.name,
          useValue: {},
        },
        {
          provide: PortalHospital.name,
          useValue: mockHospital,
        },
        {
          provide: ImportHospital.name,
          useValue: mockImportHospital,
        },
      ],
    }).compile();

    service = module.get<HospitalFavoriteService>(HospitalFavoriteService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getHospitalFavorite', () => {
    it('should return null if no customerId is provided', async () => {
      const result = await service.getHospitalFavorite(0);
      expect(result).toBeNull();
    });

    it('should return favorite hospital by customerId and hospitalId', async () => {
      const mockFavorite = { id: 1, customerId: 1, portalHospitalId: 1 };
      mockHospitalFavorite.first.mockResolvedValueOnce(mockFavorite);

      const result = await service.getHospitalFavorite(1, 1);
      expect(result).toEqual(mockFavorite);
      expect(mockHospitalFavorite.where).toHaveBeenCalledWith({
        customerId: 1,
        portalHospitalId: 1,
      });
    });

    it('should return favorite hospital by customerId and scuelId', async () => {
      const mockFavorite = { id: 1, customerId: 1, scuelId: 'SCUEL123' };
      mockHospitalFavorite.first.mockResolvedValueOnce(mockFavorite);

      const result = await service.getHospitalFavorite(
        1,
        undefined,
        'SCUEL123',
      );
      expect(result).toEqual(mockFavorite);
      expect(mockHospitalFavorite.where).toHaveBeenCalledWith({
        customerId: 1,
        scuelId: 'SCUEL123',
      });
    });
  });

  describe('toggleFavoriteHospital', () => {
    const mockInput: FavoriteHospitalInput = {
      hospitalId: 1,
      isFavorite: true,
    };

    it('should throw error if neither customerId nor sessionId is provided', async () => {
      await expect(service.toggleFavoriteHospital(mockInput)).rejects.toThrow(
        new GraphQLError(ERROR_TYPE.INVALID_HOSPITAL_FAVORITE_SESSION_ID, {
          extensions: {
            code: ERROR_TYPE.INVALID_HOSPITAL_FAVORITE_SESSION_ID,
            userMessage: ERROR_MSG.INVALID_HOSPITAL_FAVORITE_SESSION_ID,
          },
        }),
      );
    });

    it('should throw error if hospital not found', async () => {
      mockHospital.first.mockResolvedValueOnce(null);

      await expect(
        service.toggleFavoriteHospital(mockInput, 1),
      ).rejects.toThrow(
        new GraphQLError(ERROR_TYPE.HOSPITAL_NOT_FOUND, {
          extensions: {
            code: ERROR_TYPE.HOSPITAL_NOT_FOUND,
            userMessage: ERROR_MSG.HOSPITAL_NOT_FOUND,
          },
        }),
      );
    });

    it('should add hospital to favorites if not already favorited', async () => {
      mockHospital.first.mockResolvedValueOnce({ id: 1 });
      mockHospitalFavorite.first.mockResolvedValueOnce(null);
      mockHospitalFavorite.insert.mockResolvedValueOnce({ id: 1 });

      const result = await service.toggleFavoriteHospital(mockInput, 1);
      expect(result).toBe(true);
      expect(mockHospitalFavorite.insert).toHaveBeenCalled();
    });

    it('should remove hospital from favorites if already favorited', async () => {
      mockHospital.first.mockResolvedValueOnce({ id: 1 });
      mockHospitalFavorite.first.mockResolvedValueOnce({ id: 1 });

      const result = await service.toggleFavoriteHospital(
        { ...mockInput, isFavorite: false },
        1,
      );
      expect(result).toBe(false);
      expect(mockHospitalFavorite.delete).toHaveBeenCalled();
    });
  });

  describe('getFavoriteHospitalsByIds', () => {
    it('should return empty array if neither customerId nor sessionId is provided', async () => {
      const result = await service.getFavoriteHospitalsByIds([1], []);
      expect(result).toEqual([]);
    });

    it('should return favorite hospitals by customerId and hospitalIds', async () => {
      const mockFavorites = [
        { id: 1, customerId: 1, portalHospitalId: 1 },
        { id: 2, customerId: 1, portalHospitalId: 2 },
      ];
      mockHospitalFavorite.whereIn.mockResolvedValueOnce(mockFavorites);

      const result = await service.getFavoriteHospitalsByIds([1, 2], [], 1);
      expect(result).toEqual(mockFavorites);
      expect(mockHospitalFavorite.where).toHaveBeenCalledWith({
        customerId: 1,
      });
      expect(mockHospitalFavorite.whereIn).toHaveBeenCalledWith(
        'portalHospitalId',
        [1, 2],
      );
    });

    it('should return favorite hospitals by sessionId and scuelIds', async () => {
      const mockFavorites = [
        { id: 1, sessionId: 'session1', scuelId: 'SCUEL1' },
        { id: 2, sessionId: 'session1', scuelId: 'SCUEL2' },
      ];
      mockHospitalFavorite.whereIn.mockResolvedValueOnce(mockFavorites);

      const result = await service.getFavoriteHospitalsByIds(
        [],
        ['SCUEL1', 'SCUEL2'],
        undefined,
        'session1',
      );
      expect(result).toEqual(mockFavorites);
      expect(mockHospitalFavorite.where).toHaveBeenCalledWith({
        sessionId: 'session1',
      });
      expect(mockHospitalFavorite.whereIn).toHaveBeenCalledWith('scuelId', [
        'SCUEL1',
        'SCUEL2',
      ]);
    });
  });
});
