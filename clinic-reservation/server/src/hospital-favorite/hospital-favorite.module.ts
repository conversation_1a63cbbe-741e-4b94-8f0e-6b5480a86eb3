import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AgreeModule } from '@/agree/agree.module';
import { TokenService } from '@/common/service/token.service';

import { HospitalFavoriteResolver } from './hospital-favorite.resolver';
import { HospitalFavoriteService } from './hospital-favorite.service';

@Module({
  imports: [ConfigModule, AgreeModule, HttpModule],
  providers: [HospitalFavoriteResolver, HospitalFavoriteService, TokenService],
  exports: [HospitalFavoriteService],
})
export class HospitalFavoriteModule {}
