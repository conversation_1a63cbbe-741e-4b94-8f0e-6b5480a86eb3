 
import { INestApplication } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Test, TestingModule } from '@nestjs/testing';
import supertest from 'supertest';

import { AppModule } from '@/app.module';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { CustomerLoginGuard } from '@/common/guards/customer/customer-login.guard';
import { Customer } from '@/models/Customer';
import { HospitalFavorite } from '@/models/HospitalFavorite';
import { PortalHospital } from '@/models/PortalHospital';
import { RedisPubSubService } from '@/redis/redis.pubsub.service';
import { RedisService } from '@/redis/redis.service';

import { PaginatedHospitalFavorite } from './hospital-favorite.output';
import { HospitalFavoriteService } from './hospital-favorite.service';

// Mock winston to prevent actual logging during tests
jest.mock('winston', () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  }),
  format: {
    combine: jest.fn(),
    colorize: jest.fn(),
    simple: jest.fn(),
  },
  transports: {
    Console: jest.fn(),
  },
}));

// Mock RedisPubSub
jest.mock('@/redis/redis.pubsub.service', () => ({
  RedisPubSubService: jest.fn().mockImplementation(() => ({
    publish: jest.fn(),
    subscribe: jest.fn(),
    unsubscribe: jest.fn(),
    asyncIterator: jest.fn(),
  })),
}));

describe('HospitalFavoriteResolver', () => {
  let app: INestApplication;
  let mockCustomer: Partial<Customer>;
  let hospitalFavoriteService: HospitalFavoriteService;
  let redisService: RedisService;
  let _redisPubSubService: RedisPubSubService;

  const mockGuard = {
    canActivate: jest.fn().mockImplementation((context) => {
      const gqlContext = GqlExecutionContext.create(context);
      const { req } = gqlContext.getContext();
      req.user = mockCustomer;
      req.cookies = { session_id: 'test-session-id' };
      return true;
    }),
  };

  const mockPortalHospital: Partial<PortalHospital> = {
    hospitalId: 1,
    name: 'Test Hospital',
    isActive: true,
  };

  const mockFavorite: Partial<HospitalFavorite> = {
    hospitalFavoriteId: 1,
    portalHospitalId: 1,
    customerId: 1,
    portalHospital: mockPortalHospital as PortalHospital,
  };

  beforeAll(async () => {
    mockCustomer = {
      customerId: 1,
      name: 'Test Customer',
      status: 1,
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(HospitalFavoriteService)
      .useValue({
        toggleFavoriteHospital: jest.fn(),
        getHospitalFavorite: jest.fn(),
        getHospitalFavorites: jest.fn(),
        getAllCategories: jest.fn(),
        updateHospitalFavorite: jest.fn(),
      })
      .overrideProvider(RedisPubSubService)
      .useValue({
        publish: jest.fn(),
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
        asyncIterator: jest.fn(),
      })
      .overrideGuard(CustomerLoginGuard)
      .useValue(mockGuard)
      .overrideGuard(ActiveCustomerLoginGuard)
      .useValue(mockGuard)
      .overrideGuard(ActiveAgreeGuard)
      .useValue(mockGuard)
      .overrideGuard(RegistrationCompletionGuard)
      .useValue(mockGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    hospitalFavoriteService = moduleFixture.get<HospitalFavoriteService>(
      HospitalFavoriteService,
    );
    redisService = moduleFixture.get<RedisService>(RedisService);
    _redisPubSubService =
      moduleFixture.get<RedisPubSubService>(RedisPubSubService);
    await app.init();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // Close Redis connection
    const redis = redisService.getRedis();
    if (redis && typeof redis.quit === 'function') {
      await redis.quit();
    }

    // Clear all mocks
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Close the app
    await app.close();

    // Destroy Knex connection pool
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { Model } = require('objection');
    if (Model && Model.knex && typeof Model.knex().destroy === 'function') {
      await Model.knex().destroy();
    }

    // Wait for any pending operations to complete
    await new Promise((resolve) => setTimeout(resolve, 500));
  });

  const makeGraphQLRequest = async (query: string, variables = {}) => {
    return supertest(app.getHttpServer())
      .post('/graphql')
      .set('Cookie', ['session_id=test-session-id'])
      .send({ query, variables });
  };

  describe('toggleFavoriteHospital', () => {
    it('should toggle favorite hospital', async () => {
      jest
        .spyOn(hospitalFavoriteService, 'toggleFavoriteHospital')
        .mockResolvedValue(true);

      const { body, status } = await makeGraphQLRequest(
        `
        mutation toggleFavoriteHospital($input: FavoriteHospitalInput!) {
          toggleFavoriteHospital(input: $input)
        }
      `,
        {
          input: {
            hospitalId: 1,
            scuelId: 'test-scuel',
            isFavorite: true,
          },
        },
      );

      expect(status).toBe(200);
      expect(body.data.toggleFavoriteHospital).toBe(true);
    });
  });

  describe('getHospitalFavorite', () => {
    it('should get hospital favorite status', async () => {
      jest
        .spyOn(hospitalFavoriteService, 'getHospitalFavorite')
        .mockResolvedValue(mockFavorite as HospitalFavorite);

      const { body, status } = await makeGraphQLRequest(
        `
        query getHospitalFavorite($hospitalId: Float, $scuelId: String) {
          getHospitalFavorite(hospitalId: $hospitalId, scuelId: $scuelId)
        }
      `,
        {
          hospitalId: 1,
          scuelId: 'test-scuel',
        },
      );

      expect(status).toBe(200);
      expect(body.data.getHospitalFavorite).toBe(true);
    });
  });

  describe('getHospitalFavorites', () => {
    it('should get paginated hospital favorites', async () => {
      const mockResponse: PaginatedHospitalFavorite = {
        result: [mockFavorite as HospitalFavorite],
        metaData: {
          limit: 10,
          total: 1,
          lastId: 1,
        },
      };

      jest
        .spyOn(hospitalFavoriteService, 'getHospitalFavorites')
        .mockResolvedValue(mockResponse);

      const { body, status } = await makeGraphQLRequest(
        `
        query getHospitalFavorites($limit: Int, $lastId: Int, $treatmentCategoryIds: [Int!], $serviceIds: [Int!]) {
          getHospitalFavorites(limit: $limit, lastId: $lastId, treatmentCategoryIds: $treatmentCategoryIds, serviceIds: $serviceIds) {
            result {
              hospitalFavoriteId
              customerId
              portalHospital {
                hospitalId
                name
              }
            }
            metaData {
              limit
              total
              lastId
            }
          }
        }
      `,
        {
          limit: 10,
          lastId: null,
          treatmentCategoryIds: [1, 2],
          serviceIds: [1, 2],
        },
      );

      expect(status).toBe(200);
      expect(body.data.getHospitalFavorites.result.length).toBe(1);
    });
  });

  describe('getAllCategories', () => {
    it('should get all categories from favorite hospitals', async () => {
      const mockCategories = [
        { treatmentCategoryId: 1, name: 'Treatment Category 1' },
        { importServiceId: 3, name: 'Import Service 1' },
      ];

      jest
        .spyOn(hospitalFavoriteService, 'getAllCategories')
        .mockResolvedValue(mockCategories);

      const { body, status } = await makeGraphQLRequest(
        `
        query getAllCategories {
          getAllCategories {
            treatmentCategoryId
            importServiceId
            name
          }
        }
      `,
      );

      expect(status).toBe(200);
      expect(body.data.getAllCategories.length).toBe(2);
    });
  });

  describe('updateHospitalFavorite', () => {
    it('should successfully update hospital favorites from session to customer', async () => {
      jest
        .spyOn(hospitalFavoriteService, 'updateHospitalFavorite')
        .mockResolvedValue(true);

      const { body, status } = await makeGraphQLRequest(
        `
        mutation updateHospitalFavorite {
          updateHospitalFavorite
        }
      `,
      );

      expect(status).toBe(200);
      expect(body.data.updateHospitalFavorite).toBe(true);
      expect(
        hospitalFavoriteService.updateHospitalFavorite,
      ).toHaveBeenCalledWith(mockCustomer.customerId, 'test-session-id');
    });

    it('should return false when customerId is missing', async () => {
      const originalCustomerId = mockCustomer.customerId;
      mockCustomer.customerId = undefined;
      jest
        .spyOn(hospitalFavoriteService, 'updateHospitalFavorite')
        .mockResolvedValue(false);

      const { body, status } = await makeGraphQLRequest(
        `
        mutation updateHospitalFavorite {
          updateHospitalFavorite
        }
      `,
      );

      expect(status).toBe(200);
      expect(body.data.updateHospitalFavorite).toBe(false);
      mockCustomer.customerId = originalCustomerId;
    });

    it('should return false when sessionId is missing', async () => {
      jest
        .spyOn(hospitalFavoriteService, 'updateHospitalFavorite')
        .mockResolvedValue(false);

      const { body, status } = await supertest(app.getHttpServer())
        .post('/graphql')
        .send({
          query: `
            mutation updateHospitalFavorite {
              updateHospitalFavorite
            }
          `,
        });

      expect(status).toBe(200);
      expect(body.data.updateHospitalFavorite).toBe(false);
    });
  });
});
