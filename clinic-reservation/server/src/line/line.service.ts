/* eslint-disable no-case-declarations */
import {
  AccountLinkEvent,
  BotInfoResponse,
  Client,
  Message,
  MessageAPIResponseBase,
  MessageEvent,
  Profile,
  WebhookEvent,
} from '@line/bot-sdk';
import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { ModelClass, Transaction } from 'objection';
import { stringify } from 'qs';
import { lastValueFrom } from 'rxjs';

import {
  getLinkingConfirmLineMsg,
  getLinkingFailLineMsg,
  getLinkingSuccessLineMsg,
  getUnfoundedLineAccountMessage,
  getUnLinkingConfirmLineMsg,
  getUnlinkingFailLineMsg,
  getUnlinkingSuccessLineMsg,
  getUserTokenNotFoundLineMsg,
} from '@/common/constants/line.template';
import { IS_DELETED, LINE_LINKING_TYPE } from '@/common/constants/master-type';
import { Logger } from '@/common/logger/logger';
import { CustomerLine } from '@/models/CustomerLine';
import { LineUser } from '@/models/Line';
import { LineAccount } from '@/models/LineAccount';

import {
  LINE_ACTION_TYPE,
  LINE_CHAT_MSG,
  LINE_EVENT_TYPE,
  LINE_LINK_TOKEN_EXPIRED,
  LINE_MSG,
} from '../common/constants';
import { ERROR_TYPE } from '../common/constants/error-type';
import { DeleteLineAccountRes, LineLinkResponse } from './line.output';

@Injectable()
export class LineService {
  private client: Client;

  constructor(
    @Inject('CustomerLine')
    private customerLine: ModelClass<CustomerLine>,
    @Inject('LineAccount')
    private lineAccount: ModelClass<LineAccount>,
    @Inject('LineUser')
    private lineUser: ModelClass<LineUser>,

    private readonly httpService: HttpService,
    private readonly logger: Logger,
  ) {
    this.client = new Client({
      channelAccessToken: process.env.LINE_MESSAGING_CHANNEL_ACCESS_TOKEN || '',
      channelSecret: process.env.LINE_MESSAGING_CHANNEL_SECRET,
    });
  }

  readonly AUTHORIZE_URL = 'https://access.line.me/oauth2/v2.1/authorize';
  readonly TOKEN_URL = 'https://api.line.me/oauth2/v2.1/token';
  readonly LINKING_URL = 'https://access.line.me/dialog/bot/accountLink';

  /**
   * Update tCustLine element matches custNo
   * @param customerLine CustomerLine
   * @param props tCustLine props
   * @returns updated CustomerLine
   */
  async updatecustLine(
    customerLine: CustomerLine,
    props: Partial<CustomerLine>,
  ): Promise<CustomerLine | undefined> {
    const trx = await CustomerLine.startTransaction();
    try {
      const newCustomerLine = await customerLine
        .$query(trx)
        .patchAndFetch(props);
      await trx.commit();
      return newCustomerLine;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
   * LINEアカウント一覧を更新する
   * @param lineAccount LineAccount
   * @param props line_account props
   * @returns updated LineAccount
   */
  async updateLineAccounts(
    lineAccounts: LineAccount[],
    props: Partial<LineAccount>,
  ): Promise<LineAccount[] | undefined> {
    const updatedLineAccounts: LineAccount[] = [];
    const trx = await LineAccount.startTransaction();
    try {
      // If lineAccounts is an array, iterate over each item
      for (const lineAccount of lineAccounts) {
        const updatedLineAccount = await lineAccount
          .$query(trx)
          .where('isDeleted', IS_DELETED.FALSE)
          .patchAndFetch(props);
        updatedLineAccounts.push(updatedLineAccount);
      }
      await trx.commit();
      return updatedLineAccounts;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
   * Update line_account element matches line_user_id
   * @param lineAccount LineAccount
   * @param props line_account props
   * @returns updated LineAccount
   */
  async updateLineAccount(
    lineAccount: LineAccount,
    props: Partial<LineAccount>,
    transaction?: Transaction,
  ): Promise<LineAccount | undefined> {
    const trx = transaction ?? (await LineAccount.startTransaction());
    try {
      const updatedLineAccount = await lineAccount
        .$query(trx)
        .where('isDeleted', IS_DELETED.FALSE)
        .patchAndFetch(props);
      await trx.commit();
      return updatedLineAccount;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  async insertCustLine(
    customerLine: CustomerLine,
  ): Promise<CustomerLine | undefined> {
    const trx = await CustomerLine.startTransaction();
    try {
      const newCustomerLine = await customerLine.$query(trx).insertAndFetch();
      await trx.commit();
      return newCustomerLine;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  async getLineUserByLinkToken(
    linkToken: string,
  ): Promise<LineUser | undefined> {
    return await this.lineUser
      .query()
      .where('lineLinkToken', linkToken)
      .first()
      .returning('*');
  }

  /**
   * トークンでLINEアカウントを取得
   * @param linkToken
   * @returns
   */
  async getLineAccountByLinkToken(
    linkToken: string,
  ): Promise<LineAccount | undefined> {
    const now = new Date();
    return await this.lineAccount
      .query()
      .where('lineLinkToken', linkToken)
      .andWhere('linkTokenExpiry', '>=', now)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .first()
      .returning('*');
  }

  async getLineUserByLineUserId(
    lineUserId: string,
  ): Promise<LineUser | undefined> {
    return await this.lineUser
      .query()
      .where('lineUserId', lineUserId)
      .first()
      .returning('*');
  }

  async insertLineUser(lineUser: LineUser): Promise<LineUser | undefined> {
    const trx = await LineUser.startTransaction();
    try {
      const newLineUser = await lineUser.$query(trx).insertAndFetch();
      await trx.commit();
      return newLineUser;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  /**
   * LINEユーザーIDでデータを取得
   * @param lineUserId
   * @returns
   */
  async getLineAccountByLineUserId(
    lineUserId: string,
  ): Promise<LineAccount | undefined> {
    return await this.lineAccount
      .query()
      .where('lineUserId', lineUserId)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .returning('*')
      .first();
  }

  /**
   * LINEアカウントのデータを取得する
   * @param lineUserId
   * @param customerId
   * @returns
   */
  async getLineAccountByLineUserIdAndCustomerId(
    lineUserId: string,
    customerId: number,
  ): Promise<LineAccount | undefined> {
    const lineAccountQuery = this.lineAccount
      .query()
      .where('lineUserId', lineUserId)
      .andWhere('customerId', customerId)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .first()
      .returning('*');
    return await lineAccountQuery;
  }

  /**
   * LINEアカウントのデータを取得する
   * @param lineUserId
   * @param customerId
   * @returns
   */
  async getLineAccountByCustomerIdExcludingLineId(
    customerId: number,
    lineUserId: string,
  ): Promise<LineAccount | undefined> {
    const lineAccountQuery = this.lineAccount
      .query()
      .where('customerId', customerId)
      .andWhereNot('lineUserId', lineUserId)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .first()
      .returning('*');
    return await lineAccountQuery;
  }

  /**
   * LINEアカウント一覧を取得する
   * @param customerId
   * @returns
   */
  async getLineAccounts(customerId: number): Promise<LineAccount[]> {
    return await this.lineAccount
      .query()
      .where('customerId', customerId)
      .andWhere('isLinked', LINE_LINKING_TYPE.LINKED)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .orderBy('id');
  }

  /**
   * LINEアカウント新規登録
   * @param lineAccountInput
   * @returns
   */
  async insertLineAccount(lineAccountInput: LineAccount): Promise<LineAccount> {
    const profileInfo = await this.getLineUserInfo(lineAccountInput.lineUserId);
    const lineAccount = LineAccount.fromJson({
      ...lineAccountInput,
      displayName: profileInfo.displayName,
      pictureUrl: profileInfo.pictureUrl,
    });

    const trx = await LineAccount.startTransaction();
    try {
      const newLineAccount = await lineAccount.$query(trx).insertAndFetch();
      await trx.commit();
      return newLineAccount;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  private async updateLineUser(
    lineUserId: string,
    props: Partial<LineUser>,
  ): Promise<LineUser | undefined> {
    const trx = await LineUser.startTransaction();
    try {
      const lineUser = await this.lineUser
        .query(trx)
        .patch(props)
        .where('lineUserId', lineUserId)
        .returning('*')
        .first();
      await trx.commit();
      return lineUser;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  async getCustLineBycustNo(
    customerId: number,
  ): Promise<CustomerLine | undefined> {
    return await this.customerLine
      .query()
      .where('customerId', customerId)
      .returning('*')
      .first();
  }

  async getCustLineByLineUserId(
    lineUserId: string,
  ): Promise<CustomerLine | undefined> {
    return await this.customerLine
      .query()
      .where('lineUserId', lineUserId)
      .returning('*')
      .first();
  }

  async getCustLineByUserToken(
    userToken: string,
  ): Promise<CustomerLine | undefined> {
    return await this.customerLine
      .query()
      .where('userToken', userToken)
      .returning('*')
      .first();
  }

  /**
   * LINE連携処理を担当
   * @param customerId customerId
   * @param linkToken
   * @returns LineLinkResponse
   */
  async handleLineLinkingEvent(
    customerId: number,
    lineLinkToken: string,
    trx?: Transaction,
  ): Promise<LineLinkResponse> {
    const lineAccount = await this.getLineAccountByLinkToken(lineLinkToken);

    if (lineAccount) {
      const existingLineAccount =
        await this.getLineAccountByCustomerIdExcludingLineId(
          customerId,
          lineAccount.lineUserId,
        );
      if (existingLineAccount) {
        return {
          error: ERROR_TYPE.CUSTOMER_LINE_ALREADY_LINKED,
        };
      }
      const linkedLineAccount =
        await this.getLineAccountByLineUserIdAndCustomerId(
          lineAccount.lineUserId,
          customerId,
        );
      if (!linkedLineAccount) {
        const now = new Date();
        await this.updateLineAccount(
          lineAccount,
          {
            customerId: customerId,
            isLinked: LINE_LINKING_TYPE.LINKED,
            linkTokenExpiry: now,
            updatedAt: now,
          },
          trx,
        );
      }
      return {
        message: LINE_MSG.LINE_LINK_SUCCESSFUL,
      };
    }

    return {
      error: ERROR_TYPE.LINE_LINK_TOKEN_EXPIRED,
    };
  }

  async verifyLineLinkToken(lineLinkToken: string): Promise<{
    message: string;
  }> {
    const lineUser = await this.getLineUserByLinkToken(lineLinkToken);
    if (lineUser) {
      return {
        message: 'OK',
      };
    }
    return {
      message: 'FAIL',
    };
  }

  /**
   * Web イベント ハンドラー: 開始!
   * @param events WebhookEvent
   * @returns
   */
  async handleWebhookEvent(events: WebhookEvent[]): Promise<unknown> {
    const promises: Promise<unknown>[] = [];

    for (const event of events) {
      promises.push(this.handleEvent(event));
    }

    await Promise.all(promises);
    return { message: 'OK' };
  }

  /**
   * Lineエーベルトハンドル
   * @param event
   * @returns
   */
  async handleEvent(event: WebhookEvent): Promise<void> {
    try {
      // Store lineUserId for every events
      const lineUserId: string = event.source.userId ?? '';
      const eventType = event.type;
      switch (eventType) {
        case LINE_EVENT_TYPE.FOLLOW:
          await this.handleFollowEvent(lineUserId);
          break;
        case LINE_EVENT_TYPE.UNFOLLOW:
          const existLineAccount = await this.getLineAccountByLineUserId(
            lineUserId,
          );

          if (existLineAccount?.customerId) {
            await this.updateLineAccount(existLineAccount, {
              isLinked: LINE_LINKING_TYPE.UNLINKED,
              updatedAt: new Date(),
            });
          }
          break;
        case LINE_EVENT_TYPE.POSTBACK:
          const params = new URLSearchParams(event.postback.data);
          const action = params.get('action');
          if (action === LINE_ACTION_TYPE.UNLINKING) {
            await this.handleLineUnLinking(lineUserId);
          }
          break;
        case LINE_EVENT_TYPE.MESSAGE:
          await this.handleMessageEvent(lineUserId, event as MessageEvent);
          break;
        default:
          this.logger.warn('このイベントに対するプロセスがありません:', {
            eventType,
          });
          break;
      }
    } catch (error) {
      this.logger.error('handleEvent: ', error);
      throw error;
    }
  }

  /**
   * Line友達追加
   * @param event
   * @returns Lineユーザーのメッセージに対する返事
   */
  async handleFollowEvent(lineUserId: string): Promise<void> {
    const existLineAccount = await this.getLineAccountByLineUserId(lineUserId);

    if (existLineAccount?.customerId) {
      await this.updateLineAccount(existLineAccount, {
        isLinked: LINE_LINKING_TYPE.LINKED,
        updatedAt: new Date(),
      });
    }

    await this.handleLineAccountLinking(lineUserId);
  }

  /**
   * LINE連携の処理関数
   * @param lineUserId
   * @returns
   */
  async handleSendLinkingMessage(lineUserId: string): Promise<void> {
    const customerLine: CustomerLine | undefined =
      await this.getCustLineByLineUserId(lineUserId);
    // If customerLine exists, we will send a message to announce that the lineUserId is already linked.
    if (customerLine) {
      await this.sendMessage(lineUserId, getLinkingSuccessLineMsg());
      return;
    }

    // If customerLine doesn't exist, we will send a linking message to lineUserId.
    const linkToken = await this.client.getLinkToken(lineUserId);
    await this.updateLineUser(lineUserId, {
      lineLinkToken: linkToken,
    });
    await this.sendMessage(lineUserId, getLinkingFailLineMsg(linkToken));
    return;
  }

  /**
   * LINE連携かどうかチェックして、連携・連携削除のリクエストを送信
   * @param lineUserId
   * @returns
   */
  async handleLineAccountLinking(lineUserId: string): Promise<void> {
    let lineAccount: LineAccount | undefined =
      await this.getLineAccountByLineUserId(lineUserId);

    if (lineAccount?.customerId) {
      await this.sendMessage(lineUserId, getUnLinkingConfirmLineMsg());
      return;
    }

    lineAccount =
      lineAccount ??
      (await this.insertLineAccount(
        LineAccount.fromJson({
          lineUserId,
        }),
      ));

    const linkToken = await this.client.getLinkToken(lineUserId);
    const expiry = Date.now() + LINE_LINK_TOKEN_EXPIRED * 1000;

    await this.updateLineAccount(lineAccount, {
      lineLinkToken: linkToken,
      linkTokenExpiry: new Date(expiry),
      updatedAt: new Date(),
    });

    await this.sendMessage(lineUserId, getLinkingConfirmLineMsg(linkToken));
  }

  async handleLineAccountUnlinking(lineUserId: string): Promise<void> {
    const linkedLineAccount = await this.getLineAccountByLineUserId(lineUserId);
    const unlinkMessage = linkedLineAccount?.customerId
      ? getUnLinkingConfirmLineMsg()
      : getUnfoundedLineAccountMessage();
    await this.sendMessage(lineUserId, unlinkMessage);
  }

  /**
   * LINE連携削除の処理関数
   * @param lineUserId
   */
  async handleLineUnLinking(lineUserId: string): Promise<void> {
    try {
      const lineAccount = await this.getLineAccountByLineUserId(lineUserId);
      const message = lineAccount?.customerId
        ? (await this.deleteLineAccountByLineUserId(lineUserId),
          getUnlinkingSuccessLineMsg())
        : getUnfoundedLineAccountMessage();

      await this.sendMessage(lineUserId, message);
    } catch (error) {
      await this.sendMessage(lineUserId, getUnlinkingFailLineMsg());
      throw error;
    }
  }

  /**
   * ユーザーにLINEメッセージを送信
   * @param lineUserId
   * @param message
   * @returns
   */
  async sendMessage(
    lineUserId: string,
    message: Message | Message[],
  ): Promise<MessageAPIResponseBase | void> {
    return await this.client.pushMessage(lineUserId, message).catch((error) => {
      this.logger.error('sendMessage: ', error);
    });
  }

  /**
   * 複数のユーザーにLINEメッセージを送信
   * @param lineUserId
   * @param message
   * @returns
   */
  async sendLineMessage(
    lineUserId: string,
    message: string,
  ): Promise<MessageAPIResponseBase> {
    return await this.client.pushMessage(lineUserId, this.getMessage(message));
  }

  /**
   * 複数のユーザーにLINEメッセージを送信
   * @param lineUserIds
   * @param message
   * @returns
   */
  async sendMultiLineMessage(
    lineUserIds: string[],
    message: string,
  ): Promise<MessageAPIResponseBase> {
    return await this.client.multicast(lineUserIds, this.getMessage(message));
  }

  /**
   * LINEユーザーIDとカスタマーIDでLINEアカウントを削除する関数
   * @param customerId カスタマーID
   * @param lineUserId LINEユーザー番号
   * @returns
   */
  async deleteLineAccount(
    customerId: number,
    lineUserId: string,
  ): Promise<DeleteLineAccountRes> {
    const trx = await LineAccount.startTransaction();
    try {
      await this.lineAccount
        .query(trx)
        .where('customerId', customerId)
        .andWhere('lineUserId', lineUserId)
        .andWhere('isDeleted', IS_DELETED.FALSE)
        .update({
          updatedAt: new Date(),
          isDeleted: IS_DELETED.TRUE,
        });
      await trx.commit();
      return {
        message: '正常に削除されました',
      };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  /**
   * LINEユーザーIDでLINEアカウントを削除する関数
   * @param lineUserId LINEユーザー番号
   * @returns
   */
  async deleteLineAccountByLineUserId(
    lineUserId: string,
  ): Promise<DeleteLineAccountRes> {
    const trx = await LineAccount.startTransaction();
    try {
      await this.lineAccount
        .query(trx)
        .where('lineUserId', lineUserId)
        .andWhere('isDeleted', IS_DELETED.FALSE)
        .update({
          updatedAt: new Date(),
          isDeleted: IS_DELETED.TRUE,
        });
      await trx.commit();
      return {
        message: '正常に削除されました',
      };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  /**
   *
   * @param event event.type ==== 'message'
   * @returns
   */
  async handleMessageEvent(
    lineUserId: string,
    event: MessageEvent,
  ): Promise<void> {
    if (event.message.type === 'text') {
      const messageText = event.message.text;
      switch (messageText) {
        case LINE_CHAT_MSG.LINK:
          await this.handleLineAccountLinking(lineUserId);
          break;
        //一時的に「アカウント連携削除」のメッセージの処理を削除
        /*
          case LINE_CHAT_MSG.UNLINK:
            await this.handleLineAccountUnlinking(lineUserId);
            break;
          */
      }
    }
  }

  /**
   * Line連携認証
   * Document here:
   * https://developers.line.biz/en/docs/messaging-api/linking-accounts/#step-five-linking-accounts
   * @param event
   * @returns Lineユーザーのメッセージに対する返事
   */
  async handleAccountLinkEvent(event: AccountLinkEvent): Promise<void> {
    const userToken = event.link.nonce;
    const lineUserId = event.source.userId ?? '';
    const customerLine = await this.getCustLineByUserToken(userToken);
    if (customerLine) {
      if (event.link.result === 'ok') {
        // Update lineUserToken, isLinked using the returned nonce

        await this.updatecustLine(customerLine, {
          lineUserId: lineUserId,
          userToken: null,
        });
        // Send a success message to user.
        await this.sendMessage(lineUserId, getLinkingSuccessLineMsg());
      } else if (event.link.result === 'failed') {
        // Re-send a linking message to user.
        const linkToken = await this.client.getLinkToken(lineUserId);
        await this.updateLineUser(lineUserId, {
          lineLinkToken: linkToken,
        });
        await this.sendMessage(lineUserId, getLinkingFailLineMsg(linkToken));
      }
      return;
    }
    await this.sendMessage(lineUserId, getUserTokenNotFoundLineMsg());
  }

  async getUserToken(code: string): Promise<unknown> {
    const postData = {
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: `${process.env.API_URL}/auth/line/callback`,
      client_id: process.env.LINE_LOGIN_CHANNEL_ID,
      client_secret: process.env.LINE_LOGIN_CHANNEL_SECRET,
    };
    const tokenResponse = await lastValueFrom(
      this.httpService.post(this.TOKEN_URL, stringify(postData)),
    );
    return tokenResponse.data;
  }

  async getBotInfo(): Promise<BotInfoResponse> {
    const botInfo = await this.client.getBotInfo();
    return botInfo;
  }

  async getLineUserInfo(userId: string): Promise<Profile> {
    const userInfo = await this.client.getProfile(userId);
    return userInfo;
  }

  getMessage(message: string): Message {
    return {
      type: 'text',
      text: `${message}`,
    };
  }
}
