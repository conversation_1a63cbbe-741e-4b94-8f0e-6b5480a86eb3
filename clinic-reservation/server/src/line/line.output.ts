import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class LineLinkResponse {
  @Field(() => String, { nullable: true })
  error?: string;
  @Field(() => String, { nullable: true })
  message?: string;
  @Field(() => String, { nullable: true })
  redirectUrl?: string;
}

@ObjectType()
export class DeleteLineAccountRes {
  @Field(() => String, { nullable: true })
  error?: string;
  @Field(() => String, { nullable: true })
  message?: string;
}
