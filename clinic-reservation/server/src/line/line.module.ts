import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AgreeService } from '@/agree/agree.service';
import { TokenService } from '@/common/service/token.service';
import { RedisPubSubService } from '@/redis/redis.pubsub.service';

import { LineController } from './line.controller';
import { LineResolver } from './line.resolver';
import { LineService } from './line.service';

@Module({
  controllers: [LineController],
  providers: [
    LineService,
    LineResolver,
    RedisPubSubService,
    AgreeService,
    TokenService,
  ],
  imports: [HttpModule],
})
export class LineModule {}
