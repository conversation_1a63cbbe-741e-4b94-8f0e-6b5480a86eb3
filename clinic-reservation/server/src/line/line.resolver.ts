import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';

import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { LoginCustomer } from '@/customer/customer.output';
import { LineAccount } from '@/models/LineAccount';

import { LineLinkResponse } from './line.output';
import { LineService } from './line.service';
@Resolver()
export class LineResolver {
  constructor(private readonly lineService: LineService) {}

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Query(() => [LineAccount])
  async getLineAccounts(
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<LineAccount[]> {
    return await this.lineService.getLineAccounts(loginCustomer.customerId);
  }

  @Query(() => String)
  async getLineBotId(): Promise<string> {
    const botInfo = await this.lineService.getBotInfo();
    return botInfo.basicId;
  }

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Mutation(() => LineLinkResponse)
  async linkingLineAccount(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('lineLinkToken', { type: () => String, nullable: false })
    lineLinkToken: string,
  ): Promise<LineLinkResponse> {
    return await this.lineService.handleLineLinkingEvent(
      loginCustomer.customerId,
      lineLinkToken,
    );
  }
}
