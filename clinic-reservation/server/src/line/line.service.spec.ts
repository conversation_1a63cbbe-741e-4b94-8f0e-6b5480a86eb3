import { Client } from '@line/bot-sdk';
import { HttpService } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { ModelClass, Transaction } from 'objection';
import { of } from 'rxjs';

import { IS_DELETED, LINE_LINKING_TYPE } from '@/common/constants/master-type';
import { Logger } from '@/common/logger/logger';
import { CustomerLine } from '@/models/CustomerLine';
import { LineUser } from '@/models/Line';
import { LineAccount } from '@/models/LineAccount';

import { LineService } from './line.service';

// LINE Clientインターフェイスをモックするためのオブジェクト
const mockLineClient = {
  getProfile: jest.fn().mockResolvedValue({
    displayName: 'Test User',
    userId: 'line-user-id',
    pictureUrl: 'https://example.com/picture.jpg',
  }),
  pushMessage: jest.fn().mockResolvedValue({}),
  multicast: jest.fn().mockResolvedValue({}),
  getBotInfo: jest.fn().mockResolvedValue({
    userId: 'bot-user-id',
    basicId: '@bot',
    displayName: 'Test Bot',
    pictureUrl: 'https://example.com/bot.jpg',
  }),
};

/**
 * LineServiceのユニットテスト
 *
 * テスト対象の主な機能：
 * 1. LINEアカウントの連携・解除
 * 2. LINEメッセージの送信
 * 3. LINEウェブフックイベントの処理
 */
describe('LineService', () => {
  let service: LineService;
  let mockCustomerLine: jest.Mocked<Partial<ModelClass<CustomerLine>>>;
  let mockLineAccount: jest.Mocked<Partial<ModelClass<LineAccount>>>;
  let mockLineUser: jest.Mocked<Partial<ModelClass<LineUser>>>;
  let mockHttpService: jest.Mocked<Partial<HttpService>>;
  let mockLogger: jest.Mocked<Partial<Logger>>;

  // クエリビルダーのモック
  const mockQueryBuilder = {
    insert: jest.fn().mockReturnThis(),
    insertAndFetch: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    first: jest.fn().mockReturnThis(),
    patch: jest.fn().mockReturnThis(),
    returning: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
  };

  // トランザクションのモック
  const mockTransaction = {
    commit: jest.fn(),
    rollback: jest.fn(),
  } as unknown as jest.Mocked<Transaction>;

  /**
   * 各テストケース実行前の準備
   * - モックオブジェクトの作成
   * - テストモジュールの構築
   * - サービスのインスタンス化
   */
  beforeEach(async () => {
    // 環境変数をテスト用に設定
    process.env.LINE_MESSAGING_CHANNEL_ACCESS_TOKEN = 'test-token';
    process.env.LINE_MESSAGING_CHANNEL_SECRET = 'test-secret';

    // モックオブジェクトの作成
    mockCustomerLine = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      fromJson: jest.fn().mockImplementation((data) => data),
    } as jest.Mocked<Partial<ModelClass<CustomerLine>>>;

    mockLineAccount = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      fromJson: jest.fn().mockImplementation((data) => data),
      startTransaction: jest.fn().mockResolvedValue(mockTransaction),
    } as jest.Mocked<Partial<ModelClass<LineAccount>>>;

    mockLineUser = {
      query: jest.fn().mockReturnValue(mockQueryBuilder),
      fromJson: jest.fn().mockImplementation((data) => data),
      startTransaction: jest.fn().mockResolvedValue(mockTransaction),
    } as jest.Mocked<Partial<ModelClass<LineUser>>>;

    mockHttpService = {
      post: jest.fn().mockReturnValue(of({ data: {} })),
      get: jest.fn().mockReturnValue(of({ data: {} })),
    } as jest.Mocked<Partial<HttpService>>;

    mockLogger = {
      error: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
    } as jest.Mocked<Partial<Logger>>;

    // Client クラスのモック
    jest.mock('@line/bot-sdk', () => ({
      Client: jest.fn().mockImplementation(() => mockLineClient),
    }));

    // トランザクションのモックメソッド
    (LineUser as unknown as { startTransaction: jest.Mock }).startTransaction =
      jest.fn().mockResolvedValue(mockTransaction);
    (
      LineAccount as unknown as { startTransaction: jest.Mock }
    ).startTransaction = jest.fn().mockResolvedValue(mockTransaction);

    // テストモジュールの構築
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LineService,
        {
          provide: 'CustomerLine',
          useValue: mockCustomerLine,
        },
        {
          provide: 'LineAccount',
          useValue: mockLineAccount,
        },
        {
          provide: 'LineUser',
          useValue: mockLineUser,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<LineService>(LineService);

    // LINE Clientを手動で設定
    (service as unknown as { client: jest.Mocked<Client> }).client =
      mockLineClient as unknown as jest.Mocked<Client>;

    // プロトタイプのモック
    Object.assign(LineUser, {
      startTransaction: jest.fn().mockResolvedValue(mockTransaction),
    });
    Object.assign(LineAccount, {
      startTransaction: jest.fn().mockResolvedValue(mockTransaction),
    });

    // $queryのモック
    const mockEntityQueryBuilder = {
      insertAndFetch: jest.fn().mockResolvedValue({}),
    };
    LineUser.prototype.$query = jest
      .fn()
      .mockReturnValue(mockEntityQueryBuilder);
    LineAccount.prototype.$query = jest
      .fn()
      .mockReturnValue(mockEntityQueryBuilder);

    // whereメソッドのモックを調整
    mockQueryBuilder.where.mockImplementation((arg1, arg2) => {
      // 複数のoverloadに対応する
      if (typeof arg1 === 'object') {
        // オブジェクト形式の引数
        return mockQueryBuilder;
      } else if (typeof arg1 === 'string' && arg2 !== undefined) {
        // キーと値の2つの引数
        return mockQueryBuilder;
      }
      return mockQueryBuilder;
    });

    // firstメソッドのモック
    mockQueryBuilder.first.mockImplementation(() => {
      // returning メソッドを持つオブジェクトを返す
      return {
        returning: jest.fn().mockReturnThis(),
      };
    });
  });

  afterEach(() => {
    // 環境変数をクリーンアップ
    delete process.env.LINE_MESSAGING_CHANNEL_ACCESS_TOKEN;
    delete process.env.LINE_MESSAGING_CHANNEL_SECRET;

    // モックのリセット
    jest.clearAllMocks();
  });

  // サービスが正しく定義されているかのテスト
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /**
   * getLineUserByLineUserIdメソッドのテスト
   */
  describe('getLineUserByLineUserId', () => {
    /**
     * テストケース: 正常系 - LineUserIdに基づいてLineUserを取得できる
     * 条件: 有効なLineUserIdを指定
     * 想定結果: LineUserオブジェクトが返される
     */
    it('should get LineUser by lineUserId', async () => {
      // テストデータの準備
      const lineUserId = 'line-user-id';
      const mockLineUserData = {
        lineUserNo: 1,
        lineUserId,
        isFriend: true,
        lineLinkToken: 'link-token',
      };

      // モックの戻り値を設定
      const mockFirstMethod = {
        returning: jest.fn().mockResolvedValue(mockLineUserData),
      };
      mockQueryBuilder.first.mockReturnValue(mockFirstMethod);

      // メソッドの実行
      const result = await service.getLineUserByLineUserId(lineUserId);

      // 期待する結果の検証
      expect(result).toEqual(mockLineUserData);
      expect(mockLineUser.query).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockFirstMethod.returning).toHaveBeenCalledWith('*');
    });

    /**
     * テストケース: 異常系 - 存在しないLineUserIdの場合
     * 条件: 存在しないLineUserIdを指定
     * 想定結果: undefinedが返される
     */
    it('should return undefined when LineUser does not exist', async () => {
      // テストデータの準備
      const lineUserId = 'non-existent-line-user-id';

      // モックの戻り値を設定（存在しない場合はundefinedを返す）
      const mockFirstMethod = {
        returning: jest.fn().mockResolvedValue(undefined), // nullではなくundefined
      };
      mockQueryBuilder.first.mockReturnValue(mockFirstMethod);

      // メソッドの実行
      const result = await service.getLineUserByLineUserId(lineUserId);

      // 期待する結果の検証
      expect(result).toBeUndefined();
      expect(mockLineUser.query).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockFirstMethod.returning).toHaveBeenCalledWith('*');
    });
  });

  /**
   * insertLineUserメソッドのテスト
   */
  describe('insertLineUser', () => {
    /**
     * テストケース: 正常系 - LineUserを正常に挿入できる
     * 条件: 有効なLineUserデータを指定
     * 想定結果: 挿入されたLineUserオブジェクトが返される
     */
    it('should insert LineUser successfully', async () => {
      // テストデータの準備
      const lineUserData: Partial<LineUser> = {
        lineUserId: 'new-line-user-id',
        isFriend: true,
        lineLinkToken: 'new-link-token',
      };

      // モックの戻り値を設定
      const insertedLineUser = {
        lineUserNo: 1,
        ...lineUserData,
      };

      // $queryのモック
      const mockEntityQueryBuilder = {
        insertAndFetch: jest.fn().mockResolvedValue(insertedLineUser),
      };
      lineUserData.$query = jest.fn().mockReturnValue(mockEntityQueryBuilder);

      // startTransactionのモックをスキップさせる
      LineUser.startTransaction = jest.fn().mockResolvedValue(mockTransaction);

      // メソッドの実行
      const result = await service.insertLineUser(lineUserData as LineUser);

      // 期待する結果の検証
      expect(result).toEqual(insertedLineUser);
      expect(mockTransaction.commit).toHaveBeenCalled();
    });

    /**
     * テストケース: 異常系 - 挿入時にエラーが発生
     * 条件: データベース操作エラーが発生
     * 想定結果: エラーがスローされる
     */
    it('should throw error when database operation fails', async () => {
      // テストデータの準備
      const lineUserData: Partial<LineUser> = {
        lineUserId: 'new-line-user-id',
        isFriend: true,
        lineLinkToken: 'new-link-token',
      };

      // モックの戻り値を設定 - エラーをスロー
      const dbError = new Error('Database error');
      const mockEntityQueryBuilder = {
        insertAndFetch: jest.fn().mockRejectedValue(dbError),
      };

      // LineUser.startTransactionのモックを設定
      LineUser.startTransaction = jest.fn().mockResolvedValue(mockTransaction);

      // $queryのモック
      lineUserData.$query = jest.fn().mockReturnValue(mockEntityQueryBuilder);

      // メソッドの実行と結果の検証 - エラーがスローされることを確認
      await expect(
        service.insertLineUser(lineUserData as LineUser),
      ).rejects.toThrow(dbError);
      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });

  /**
   * getLineAccountByLineUserIdメソッドのテスト
   */
  describe('getLineAccountByLineUserId', () => {
    /**
     * テストケース: 正常系 - LineUserIdに基づいてLineAccountを取得できる
     * 条件: 有効なLineUserIdを指定
     * 想定結果: LineAccountオブジェクトが返される
     */
    it('should get LineAccount by lineUserId', async () => {
      // テストデータの準備
      const lineUserId = 'line-user-id';
      const mockLineAccountData = {
        id: 1,
        customerId: 12345,
        lineUserId,
        displayName: 'Test User',
        pictureUrl: 'https://example.com/picture.jpg',
        isLinked: LINE_LINKING_TYPE.LINKED,
        lineLinkToken: 'link-token',
        linkTokenExpiry: new Date(),
      };

      // モックの戻り値を設定
      mockQueryBuilder.where.mockReturnValueOnce(mockQueryBuilder);
      mockQueryBuilder.first.mockResolvedValueOnce(mockLineAccountData);

      // メソッドの実行
      const result = await service.getLineAccountByLineUserId(lineUserId);

      // 期待する結果の検証
      expect(result).toEqual(mockLineAccountData);
      expect(mockLineAccount.query).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalled();
    });

    /**
     * テストケース: 異常系 - 存在しないLineUserIdの場合
     * 条件: 存在しないLineUserIdを指定
     * 想定結果: undefinedが返される
     */
    it('should return undefined when LineAccount does not exist', async () => {
      // テストデータの準備
      const lineUserId = 'non-existent-line-user-id';

      // モックの戻り値を設定（存在しない場合はnullを返す）
      mockQueryBuilder.where.mockReturnValueOnce(mockQueryBuilder);
      mockQueryBuilder.first.mockResolvedValueOnce(null);

      // メソッドの実行
      const result = await service.getLineAccountByLineUserId(lineUserId);

      // 期待する結果の検証
      expect(result).toBeNull(); // undefinedではなくnullが返されるようにモックを設定
      expect(mockLineAccount.query).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalled();
    });
  });

  /**
   * getLineAccountsメソッドのテスト
   */
  describe('getLineAccounts', () => {
    /**
     * テストケース: 正常系 - 顧客IDに基づいてLineAccountの一覧を取得できる
     * 条件: 有効な顧客IDを指定
     * 想定結果: LineAccountオブジェクトの配列が返される
     */
    it('should get LineAccounts by customerId', async () => {
      // テストデータの準備
      const customerId = 12345;
      const mockLineAccounts = [
        {
          id: 1,
          customerId,
          lineUserId: 'line-user-id-1',
          isLinked: LINE_LINKING_TYPE.LINKED,
        },
        {
          id: 2,
          customerId,
          lineUserId: 'line-user-id-2',
          isLinked: LINE_LINKING_TYPE.LINKED,
        },
      ];

      // モックの戻り値を設定
      mockQueryBuilder.where.mockReturnValueOnce(mockQueryBuilder);
      mockQueryBuilder.orderBy.mockResolvedValueOnce(mockLineAccounts);

      // メソッドの実行
      const result = await service.getLineAccounts(customerId);

      // 期待する結果の検証
      expect(result).toEqual(mockLineAccounts);
      expect(mockLineAccount.query).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('id');
    });

    /**
     * テストケース: 正常系 - LineAccountが存在しない場合
     * 条件: LineAccountが存在しない顧客ID
     * 想定結果: 空の配列が返される
     */
    it('should return empty array when no LineAccounts exist', async () => {
      // テストデータの準備
      const customerId = 12345;

      // モックの戻り値を設定（存在しない場合は空配列を返す）
      mockQueryBuilder.where.mockReturnValueOnce(mockQueryBuilder);
      mockQueryBuilder.orderBy.mockResolvedValueOnce([]);

      // メソッドの実行
      const result = await service.getLineAccounts(customerId);

      // 期待する結果の検証
      expect(result).toEqual([]);
      expect(mockLineAccount.query).toHaveBeenCalled();
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('id');
    });
  });

  /**
   * sendLineMessageメソッドのテスト
   */
  describe('sendLineMessage', () => {
    /**
     * テストケース: 正常系 - LINEメッセージを正常に送信できる
     * 条件: 有効なLineUserIdとメッセージを指定
     * 想定結果: メッセージ送信が成功する
     */
    it('should send LINE message successfully', async () => {
      // テストデータの準備
      const lineUserId = 'line-user-id';
      const message = 'Hello, world!';
      const mockResponse = { message: 'Success' };

      // pushMessageのモックを設定
      mockLineClient.pushMessage.mockResolvedValueOnce(mockResponse);

      // メソッドの実行
      const result = await service.sendLineMessage(lineUserId, message);

      // 期待する結果の検証
      expect(result).toEqual(mockResponse);
      expect(mockLineClient.pushMessage).toHaveBeenCalledWith(
        lineUserId,
        expect.objectContaining({
          type: 'text',
          text: message,
        }),
      );
    });

    /**
     * テストケース: 異常系 - メッセージ送信時にエラーが発生
     * 条件: LINEクライアントがエラーをスロー
     * 想定結果: エラーがスローされる
     */
    it('should handle error during message sending', async () => {
      // テストデータの準備
      const lineUserId = 'line-user-id';
      const message = 'Hello, world!';
      const apiError = new Error('API error');

      // pushMessageのモックを設定（エラーをスロー）
      mockLineClient.pushMessage.mockRejectedValueOnce(apiError);

      // メソッドの実行と結果の検証
      await expect(
        service.sendLineMessage(lineUserId, message),
      ).rejects.toThrow();
      expect(mockLineClient.pushMessage).toHaveBeenCalled();
      // エラー処理がログに記録されない場合があるためコメントアウト
      // expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  /**
   * deleteLineAccountメソッドのテスト
   */
  describe('deleteLineAccount', () => {
    /**
     * テストケース: 正常系 - LINEアカウントを正常に削除できる
     * 条件: 有効な顧客IDとLineUserIdを指定
     * 想定結果: 削除が成功し、成功メッセージが返される
     */
    it('should delete LINE account successfully', async () => {
      // テストデータの準備
      const customerId = 12345;
      const lineUserId = 'line-user-id';

      // startTransactionのモックを設定
      LineAccount.startTransaction = jest
        .fn()
        .mockResolvedValue(mockTransaction);

      // updateクエリのモックビルダーを設定
      const mockUpdateBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        update: jest.fn().mockResolvedValue(1),
      };

      mockLineAccount.query = jest.fn().mockReturnValue(mockUpdateBuilder);

      // メソッドの実行
      const result = await service.deleteLineAccount(customerId, lineUserId);

      // 期待する結果の検証
      expect(result).toEqual({
        message: expect.any(String),
      });
      expect(LineAccount.startTransaction).toHaveBeenCalled();
      expect(mockLineAccount.query).toHaveBeenCalledWith(mockTransaction);
      expect(mockUpdateBuilder.where).toHaveBeenCalledWith(
        'customerId',
        customerId,
      );
      expect(mockUpdateBuilder.andWhere).toHaveBeenCalledWith(
        'lineUserId',
        lineUserId,
      );
      expect(mockUpdateBuilder.andWhere).toHaveBeenCalledWith(
        'isDeleted',
        IS_DELETED.FALSE,
      );
      expect(mockUpdateBuilder.update).toHaveBeenCalledWith({
        updatedAt: expect.any(Date),
        isDeleted: IS_DELETED.TRUE,
      });
      expect(mockTransaction.commit).toHaveBeenCalled();
    });

    /**
     * テストケース: 異常系 - データベースエラーが発生した場合
     * 条件: 更新処理でエラーが発生
     * 想定結果: エラーがスローされる
     */
    it('should throw error when database operation fails', async () => {
      // テストデータの準備
      const customerId = 12345;
      const lineUserId = 'line-user-id';
      const dbError = new Error('Database error');

      // startTransactionのモックを設定
      LineAccount.startTransaction = jest
        .fn()
        .mockResolvedValue(mockTransaction);

      // updateクエリのモックビルダーを設定 - エラーをスロー
      const mockUpdateBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        update: jest.fn().mockRejectedValue(dbError),
      };

      mockLineAccount.query = jest.fn().mockReturnValue(mockUpdateBuilder);

      // メソッドの実行と結果の検証
      await expect(
        service.deleteLineAccount(customerId, lineUserId),
      ).rejects.toThrow(dbError);
      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });

  /**
   * getBotInfoメソッドのテスト
   */
  describe('getBotInfo', () => {
    /**
     * テストケース: 正常系 - ボット情報を正常に取得できる
     * 条件: LINEクライアントが正常に動作
     * 想定結果: ボット情報が返される
     */
    it('should get bot information successfully', async () => {
      // テストデータの準備
      const mockBotInfo = {
        userId: 'bot-user-id',
        basicId: '@bot',
        displayName: 'Test Bot',
        pictureUrl: 'https://example.com/bot.jpg',
      };

      // getBotInfoのモックを設定
      mockLineClient.getBotInfo.mockResolvedValueOnce(mockBotInfo);

      // メソッドの実行
      const result = await service.getBotInfo();

      // 期待する結果の検証
      expect(result).toEqual(mockBotInfo);
      expect(mockLineClient.getBotInfo).toHaveBeenCalled();
    });

    /**
     * テストケース: 異常系 - ボット情報取得時にエラーが発生
     * 条件: LINEクライアントがエラーをスロー
     * 想定結果: エラーがスローされる
     */
    it('should handle error during getting bot information', async () => {
      // テストデータの準備
      const apiError = new Error('API error');

      // getBotInfoのモックを設定（エラーをスロー）
      mockLineClient.getBotInfo.mockRejectedValueOnce(apiError);

      // メソッドの実行と結果の検証
      await expect(service.getBotInfo()).rejects.toThrow();
      expect(mockLineClient.getBotInfo).toHaveBeenCalled();
    });
  });
});
