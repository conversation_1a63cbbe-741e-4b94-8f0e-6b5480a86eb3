/* eslint-disable @typescript-eslint/no-explicit-any */
import { WebhookEvent } from '@line/bot-sdk';
import { Body, Controller, Post } from '@nestjs/common';

import { LineService } from './line.service';

@Controller('line')
export class LineController {
  constructor(private readonly lineService: LineService) {}
  @Post('webhook')
  async handleWebhookEvent(@Body() body: any): Promise<any> {
    const events: WebhookEvent[] = body.events;
    return await this.lineService.handleWebhookEvent(events);
  }
}
