import { Inject, Injectable } from '@nestjs/common';
import { ModelClass } from 'objection';

import { IS_DELETED } from '@/common/constants/master-type';
import { CalendarTreatment } from '@/models/CalendarTreatment';

@Injectable()
export class CalendarTreatmentService {
  constructor(
    @Inject(CalendarTreatment.name)
    private calendarTreatment: ModelClass<CalendarTreatment>,
  ) {}

  async getCalendarTreatmentById(id: number): Promise<CalendarTreatment> {
    return await this.calendarTreatment
      .query()
      .findById(id)
      .withGraphFetched('treatmentDepartment')
      .modifyGraph('treatmentDepartment', (builder) => {
        builder.where('isDeleted', IS_DELETED.FALSE);
      })
      .where('calendarTreatment.isDeleted', IS_DELETED.FALSE)
      .throwIfNotFound();
  }
}
