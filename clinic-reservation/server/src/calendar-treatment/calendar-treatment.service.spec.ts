import { Test, TestingModule } from '@nestjs/testing';
import { ModelClass } from 'objection';

import { IS_DELETED } from '@/common/constants/master-type';
import { CalendarTreatment } from '@/models/CalendarTreatment';

import { CalendarTreatmentService } from './calendar-treatment.service';

const mockCalendarTreatment = {
  calendarTreatmentId: 1,
  calendarId: 100,
  treatmentDepartmentId: 200,
  treatmentDepartment: {
    treatmentDepartmentId: 200,
    title: 'テスト診療科',
    description: 'テスト説明',
    isDeleted: IS_DELETED.FALSE,
  },
  isDeleted: IS_DELETED.FALSE,
};

describe('CalendarTreatmentService', () => {
  let service: CalendarTreatmentService;
  let calendarTreatmentModel: ModelClass<CalendarTreatment>;

  // クエリビルダーのモック
  const mockQueryBuilder = {
    findById: jest.fn().mockReturnThis(),
    withGraphFetched: jest.fn().mockReturnThis(),
    modifyGraph: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    throwIfNotFound: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CalendarTreatmentService,
        {
          provide: CalendarTreatment.name,
          useValue: {
            query: jest.fn().mockReturnValue(mockQueryBuilder),
          },
        },
      ],
    }).compile();

    service = module.get<CalendarTreatmentService>(CalendarTreatmentService);
    calendarTreatmentModel = module.get<ModelClass<CalendarTreatment>>(
      CalendarTreatment.name,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  /**
   * 正常系テスト - 存在するIDでカレンダー診療情報を取得
   * Given: 有効なカレンダー診療ID
   * When: getCalendarTreatmentByIdメソッドを呼び出す
   * Then: 該当するカレンダー診療情報が返される
   */
  it('正常系 - 存在するIDでカレンダー診療情報が正しく取得される', async () => {
    // Given
    const id = 1;
    mockQueryBuilder.throwIfNotFound.mockResolvedValue(mockCalendarTreatment);

    // When
    const result = await service.getCalendarTreatmentById(id);

    // Then
    expect(calendarTreatmentModel.query).toHaveBeenCalled();
    expect(mockQueryBuilder.findById).toHaveBeenCalledWith(id);
    expect(mockQueryBuilder.withGraphFetched).toHaveBeenCalledWith(
      'treatmentDepartment',
    );
    expect(mockQueryBuilder.modifyGraph).toHaveBeenCalledWith(
      'treatmentDepartment',
      expect.any(Function),
    );
    expect(mockQueryBuilder.where).toHaveBeenCalledWith(
      'calendarTreatment.isDeleted',
      IS_DELETED.FALSE,
    );
    expect(mockQueryBuilder.throwIfNotFound).toHaveBeenCalled();
    expect(result).toEqual(mockCalendarTreatment);
  });

  /**
   * 異常系テスト - 存在しないIDでカレンダー診療情報を取得
   * Given: 存在しないカレンダー診療ID
   * When: getCalendarTreatmentByIdメソッドを呼び出す
   * Then: エラーがスローされる
   */
  it('異常系 - 存在しないIDでエラーがスローされる', async () => {
    // Given
    const id = 999;
    const notFoundError = new Error('カレンダー診療が見つかりません');
    mockQueryBuilder.throwIfNotFound.mockRejectedValue(notFoundError);

    // When & Then
    await expect(service.getCalendarTreatmentById(id)).rejects.toThrow(
      notFoundError,
    );
    expect(calendarTreatmentModel.query).toHaveBeenCalled();
    expect(mockQueryBuilder.findById).toHaveBeenCalledWith(id);
  });

  /**
   * 異常系テスト - 削除済みカレンダー診療情報を取得
   * Given: 削除済みのカレンダー診療ID
   * When: getCalendarTreatmentByIdメソッドを呼び出す
   * Then: クエリに削除されていないという条件が含まれている
   */
  it('異常系 - 削除済みデータを除外する条件が適用される', async () => {
    // Given
    const id = 1;
    mockQueryBuilder.throwIfNotFound.mockResolvedValue(mockCalendarTreatment);

    // When
    await service.getCalendarTreatmentById(id);

    // Then
    expect(mockQueryBuilder.where).toHaveBeenCalledWith(
      'calendarTreatment.isDeleted',
      IS_DELETED.FALSE,
    );
  });

  /**
   * 正常系テスト - 関連する診療科情報を含めて取得
   * Given: 有効なカレンダー診療ID
   * When: getCalendarTreatmentByIdメソッドを呼び出す
   * Then: 診療科情報が正しく取得される
   */
  it('正常系 - 関連する診療科情報も正しく取得される', async () => {
    // Given
    const id = 1;
    mockQueryBuilder.throwIfNotFound.mockResolvedValue(mockCalendarTreatment);

    // When
    const result = await service.getCalendarTreatmentById(id);

    // Then
    expect(mockQueryBuilder.withGraphFetched).toHaveBeenCalledWith(
      'treatmentDepartment',
    );
    expect(result.treatmentDepartment).toBeDefined();
    expect(result.treatmentDepartment.title).toBe('テスト診療科');
  });

  /**
   * 正常系テスト - 診療科情報の削除フラグの条件
   * Given: 有効なカレンダー診療ID
   * When: getCalendarTreatmentByIdメソッドを呼び出す
   * Then: 診療科情報の削除フラグの条件が正しく設定される
   */
  it('正常系 - 診療科の削除フラグに関する条件が正しく設定される', async () => {
    // Given
    const id = 1;
    mockQueryBuilder.throwIfNotFound.mockResolvedValue(mockCalendarTreatment);

    // whereメソッドを持つmodifyGraphFnを作成
    const modifyGraphFn = {
      where: jest.fn().mockReturnThis(),
    };

    mockQueryBuilder.modifyGraph.mockImplementation(
      (relationName, modifierFn) => {
        modifierFn(modifyGraphFn);
        return mockQueryBuilder;
      },
    );

    // When
    await service.getCalendarTreatmentById(id);

    // Then
    expect(modifyGraphFn.where).toHaveBeenCalledWith(
      'isDeleted',
      IS_DELETED.FALSE,
    );
  });
});
