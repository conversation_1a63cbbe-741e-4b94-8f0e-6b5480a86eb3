---
description: 
globs: 
alwaysApply: true
---
## Project Structure

### Feature Module Structure
```
[feature]/
├── [feature].module.ts           # Module definition
├── [feature].resolver.ts         # GraphQL resolver
├── [feature].controller.ts       # REST API endpoints (if needed)
├── [feature].service.ts          # Business logic
├── [feature].input.ts           # GraphQL input types
└── [feature].output.ts          # GraphQL output types
```

### Common Directories
```
src/
├── common/                      # Shared utilities and services
├── models/                      # Database models
├── app.module.ts               # Root module
└── main.ts                     # Application entry point
```
