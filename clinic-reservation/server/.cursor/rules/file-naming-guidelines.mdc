---
description: 
globs: 
alwaysApply: true
---
# Server File Naming Guidelines

## Model Files
```typescript
// ✅ Correct: PascalCase for models
src/models/CalendarTreatment.ts
src/models/UserProfile.ts
src/models/Appointment.ts

// ❌ Incorrect
src/models/calendar-treatment.ts
src/models/user-profile.ts
src/models/appointment.ts
```

## Non-Model Files
```typescript
// ✅ Correct: snake-case for non-model files
src/services/auth-service.ts
src/controllers/user-controller.ts
src/middlewares/auth-middleware.ts
src/utils/date-utils.ts
src/constants/api-constants.ts
src/types/user-types.ts
src/config/database-config.ts
src/interfaces/auth-interface.ts
src/dto/create-user-dto.ts
src/guards/auth-guard.ts
src/pipes/validation-pipe.ts
src/filters/http-exception-filter.ts
src/interceptors/logging-interceptor.ts

// ❌ Incorrect
src/services/authService.ts
src/controllers/userController.ts
src/middlewares/authMiddleware.ts
src/utils/dateUtils.ts
src/constants/apiConstants.ts
src/types/userTypes.ts
src/config/databaseConfig.ts
src/interfaces/authInterface.ts
src/dto/createUserDto.ts
src/guards/authGuard.ts
src/pipes/validationPipe.ts
src/filters/httpExceptionFilter.ts
src/interceptors/loggingInterceptor.ts
```

## File Types and Extensions

### TypeScript Files
- `.ts` for all TypeScript files
- `.d.ts` for type declarations
- `.test.ts` for test files
- `.spec.ts` for test files (alternative)

### Configuration Files
- `.config.ts` for configuration files
- `.env` for environment variables
- `.json` for JSON files

## Directory Structure
```
src/
  models/              # Database models (PascalCase)
    CalendarTreatment.ts
    UserProfile.ts
    Appointment.ts

  middlewares/        # Middleware functions (snake-case)
    auth-middleware.ts
    logging-middleware.ts
  
  utils/              # Utility functions (snake-case)
    date-utils.ts
    validation-utils.ts
  
  constants/          # Constants (snake-case)
    api-constants.ts
    error-constants.ts
  
  types/              # TypeScript types (snake-case)
    user-types.ts
    api-types.ts
  
  guards/             # Route guards (snake-case)
    auth-guard.ts
    role-guard.ts
  
  pipes/              # Validation pipes (snake-case)
    validation-pipe.ts
    transform-pipe.ts
  
  filters/            # Exception filters (snake-case)
    http-exception-filter.ts
    validation-filter.ts
  
  interceptors/       # Interceptors (snake-case)
    logging-interceptor.ts
    transform-interceptor.ts
```

## Naming Conventions

### Models
- Use PascalCase for model files
- Match model name with file name
- Use .ts extension
- Group related models together

### Services
- Use snake-case with '-service' suffix
- Use descriptive names
- Use .ts extension
- Group related services together

### Controllers
- Use snake-case with '-controller' suffix
- Use descriptive names
- Use .ts extension
- Group related controllers together

### Middlewares
- Use snake-case with '-middleware' suffix
- Use descriptive names
- Use .ts extension
- Group related middlewares together

### Utils
- Use snake-case with '-utils' suffix
- Use descriptive names
- Use .ts extension
- Group related utilities together

### Constants
- Use snake-case with '-constants' suffix
- Use descriptive names
- Use .ts extension
- Group related constants together

### Types
- Use snake-case with '-types' suffix
- Use descriptive names
- Use .ts extension
- Group related types together

### Config
- Use snake-case with '-config' suffix
- Use descriptive names
- Use .ts extension
- Group related configs together

### Interfaces
- Use snake-case with '-interface' suffix
- Use descriptive names
- Use .ts extension
- Group related interfaces together

### Guards
- Use snake-case with '-guard' suffix
- Use descriptive names
- Use .ts extension
- Group related guards together

### Pipes
- Use snake-case with '-pipe' suffix
- Use descriptive names
- Use .ts extension
- Group related pipes together

### Filters
- Use snake-case with '-filter' suffix
- Use descriptive names
- Use .ts extension
- Group related filters together

### Interceptors
- Use snake-case with '-interceptor' suffix
- Use descriptive names
- Use .ts extension
- Group related interceptors together

## Best Practices
- Be consistent with naming conventions
- Use descriptive and meaningful names
- Follow the established patterns
- Keep file names short but clear
- Use proper file extensions
- Group related files together
- Maintain directory structure
- Use proper suffixes
- Follow TypeScript conventions
- Consider file organization
