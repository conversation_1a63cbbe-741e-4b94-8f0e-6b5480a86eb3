---
description: 
globs: 
alwaysApply: true
---
### Database
- Use Objection.js for database operations
- Define models in models directory
- Use migrations for schema changes
- Implement repository pattern

### Transactions
- Always use transactions for operations that modify multiple tables
- Use transaction for operations that need to be atomic
- For operations affecting multiple models, use `execWithTx` from `@/common/utils/transaction`:
  ```typescript
  import { execWithTx } from '@/common/utils/transaction';
  
  // Example usage
  await execWithTx(async (trx) => {
    await model1.query(trx).insert(data1);
    await model2.query(trx).insert(data2);
    await model3.query(trx).update(data3);
  });
  ```
- Benefits of using `execWithTx`:
  - Automatic transaction management
  - Automatic rollback on error
  - Cleaner code structure
  - Consistent error handling
- Keep transactions as short as possible
- Avoid long-running transactions
- Handle transaction rollback properly
- Use transaction for:
  - Creating related records
  - Updating multiple records
  - Deleting related records
  - Any operation that needs to be atomic
- Consider using transaction for:
  - Complex queries that modify data
  - Operations that need to be rolled back on failure
  - Operations that need to maintain data consistency
