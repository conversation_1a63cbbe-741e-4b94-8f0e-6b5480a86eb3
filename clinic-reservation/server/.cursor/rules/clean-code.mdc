---
description: 
globs: 
alwaysApply: true
---
# Clean Code Guidelines

## Constants Over Magic Numbers
- Replace hard-coded values with named constants
- Use descriptive constant names that explain the value's purpose
- Keep constants at the top of the file or in a dedicated constants file
- Use TypeScript enums for related constants
- Group related constants together
- Use UPPERCASE for constant names
- Document complex constants with JSDoc

## Meaningful Names
- Variables, functions, and classes should reveal their purpose
- Names should explain why something exists and how it's used
- Avoid abbreviations unless they're universally understood
- Use consistent naming patterns across the codebase
- Prefix boolean variables with is, has, can, should
- Use verbs for function names
- Use nouns for class and variable names
- Make names searchable and pronounceable

## Smart Comments
- Don't comment on what the code does - make the code self-documenting
- Use comments to explain why something is done a certain way
- Document APIs, complex algorithms, and non-obvious side effects
- Use JSDoc for public APIs and classes
- Keep comments up to date with code changes
- Remove commented-out code
- Use TODO comments for future improvements
- Document complex business logic

## Single Responsibility
- Each function should do exactly one thing
- Functions should be small and focused
- If a function needs a comment to explain what it does, it should be split
- Keep functions under 20 lines
- Use early returns to avoid nesting
- Extract complex conditions into well-named functions
- Follow the Single Responsibility Principle for classes

## DRY (Don't Repeat Yourself)
- Extract repeated code into reusable functions
- Share common logic through proper abstraction
- Maintain single sources of truth
- Use utility functions for common operations
- Create shared services for common business logic
- Use inheritance and composition appropriately
- Avoid copy-pasting code

## Clean Structure
- Keep related code together
- Organize code in a logical hierarchy
- Use consistent file and folder naming conventions
- Follow NestJS module structure
- Group related functionality in modules
- Keep files focused and small
- Use proper file naming conventions

## Encapsulation
- Hide implementation details
- Expose clear interfaces
- Move nested conditionals into well-named functions
- Use private methods and properties
- Implement proper getters and setters
- Use dependency injection
- Follow the principle of least knowledge

## Code Quality Maintenance
- Refactor continuously
- Fix technical debt early
- Leave code cleaner than you found it
- Use linters and formatters
- Run code quality checks before commits
- Review code regularly
- Keep dependencies up to date

## Testing
- Write tests before fixing bugs
- Keep tests readable and maintainable
- Test edge cases and error conditions
- Follow AAA pattern (Arrange-Act-Assert)
- Use meaningful test descriptions
- Keep tests independent
- Mock external dependencies

## Version Control
- Write clear commit messages
- Make small, focused commits
- Use meaningful branch names
- Follow Git flow branching strategy
- Keep commits atomic
- Use conventional commits format
- Review code before merging

## Error Handling
- Use proper error types
- Handle errors at appropriate levels
- Provide meaningful error messages
- Log errors properly
- Use try-catch blocks effectively
- Implement proper error recovery
- Document error handling strategies

## Performance
- Write efficient code
- Optimize database queries
- Use proper caching strategies
- Implement pagination for large datasets
- Monitor performance metrics
- Profile code regularly
- Optimize critical paths

## Security
- Follow security best practices
- Validate all inputs
- Sanitize outputs
- Use proper authentication
- Implement proper authorization
- Handle sensitive data securely
- Follow OWASP guidelines
