---
description: 
globs: 
alwaysApply: true
---
### Security
- Implement proper authentication
- Use guards for authorization
- Validate all inputs
- Sanitize outputs
- Use environment variables for sensitive data
- Always define environment variables in both `taskdef.json` and `prd-taskdef.json` for ECS deployment
- Use AWS Secrets Manager or Parameter Store for sensitive data
- Never hardcode sensitive values in code
- Validate environment variables at application startup
- Use TypeScript enums or constants for environment variable names
- Document all environment variables in README
- Follow the principle of least privilege for AWS IAM roles
- Regularly rotate secrets and credentials
- Use encryption for data at rest and in transit
- Implement proper logging and monitoring
- Follow security best practices for API endpoints
- Implement rate limiting for public APIs
- Use HTTPS for all external communications
- Implement proper CORS policies
- Regular security audits and updates
