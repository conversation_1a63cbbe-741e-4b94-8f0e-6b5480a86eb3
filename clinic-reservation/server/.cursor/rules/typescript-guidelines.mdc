---
description: 
globs: 
alwaysApply: true
---
## TypeScript General Guidelines

### Basic Principles
- Use English for all code and documentation
- Always declare types for variables, parameters, and return values
- Avoid using `any` type
- Create necessary types and interfaces
- Use JSDoc for public classes and methods
- One export per file
- No blank lines within functions

### Naming Conventions
- PascalCase: Classes, Interfaces, Types, Enums
- camelCase: Variables, Functions, Methods
- kebab-case: Files and Directories
- UPPERCASE: Environment Variables, Constants
- Boolean variables: isX, hasX, canX (e.g., isLoading, hasError)
- Function names: Start with verb (e.g., createUser, updateProfile)
- Standard abbreviations allowed: API, URL, i/j for loops, err for errors
