---
description: 
globs: 
alwaysApply: true
---
### Functions
- Keep functions short (< 20 lines)
- Single responsibility principle
- Use early returns to avoid nesting
- Use arrow functions for simple operations
- Use named functions for complex logic
- Use default parameters instead of null checks
- Use object parameters for multiple arguments (RO-RO pattern)
- Maintain single level of abstraction

### Data Handling
- Use composite types over primitives
- Implement validation in classes
- Prefer immutability
- Use `readonly` for immutable data
- Use `as const` for literal types
