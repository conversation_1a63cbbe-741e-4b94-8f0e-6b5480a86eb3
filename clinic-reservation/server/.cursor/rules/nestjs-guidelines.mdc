---
description: 
globs: 
alwaysApply: true
---
## NestJS Specific Guidelines

### Module Organization
- One module per main domain/feature
- Feature modules should be self-contained
- Use shared modules for common functionality
- Core module for NestJS artifacts:
  - Global filters
  - Global middlewares
  - Guards
  - Interceptors

### Controllers
- Use for REST API endpoints
- One controller per route
- Implement for:
  - Webhook endpoints
  - File operations
  - Redirect URLs
  - Health checks
  - Simple APIs

### Resolvers
- Use for GraphQL operations
- Implement for:
  - Complex queries
  - Flexible data fetching
  - Complex mutations
  - Subscriptions
