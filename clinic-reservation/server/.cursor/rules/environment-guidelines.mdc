---
description: 
globs: 
alwaysApply: true
---
### Environment Variables
- Always define environment variables in both `taskdef.json` and `prd-taskdef.json` for ECS deployment
- Follow this structure in taskdef files:
  ```json
  {
    "environment": [
      {
        "name": "VARIABLE_NAME",
        "value": "variable_value"
      }
    ]
  }
  ```
- For sensitive data:
  - Use AWS Secrets Manager or Parameter Store
  - Reference in taskdef using:
    ```json
    {
      "secrets": [
        {
          "name": "VARIABLE_NAME",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:secret-name"
        }
      ]
    }
    ```
- Best practices:
  - Use UPPERCASE for environment variable names
  - Document all environment variables in README
  - Provide default values in code when possible
  - Validate required environment variables at startup
  - Use TypeScript enums or constants for environment variable names
  - Never commit sensitive values to version control
