import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  await knex('portal_hosp_tag').del();
  await knex('portal_hospital_examination').del();
  await knex('portal_hosp_picture').del();
  await knex('portal_staff_picture').del();
  await knex('portal_contents_pub').del();
  await knex('portal_pict').del();
  await knex.raw(`SELECT setval('portal_pict_pict_id_seq', 1, false)`);
  await knex('portal_business_time').del();
  await knex.raw(
    `SELECT setval('portal_business_time_business_time_id_seq', 1, false)`,
  );
  await knex('meeting').del();
  await knex.raw(`SELECT setval('meeting_meeting_id_seq', 1, false)`);
  await knex('survey_answer').del();
  await knex('reserve_detail_history').del();
  await knex('reserve_detail').del();
  await knex.raw(
    `SELECT setval('reserve_detail_reserve_detail_id_seq', 1, false)`,
  );
  await knex('calendar_treatment').del();
  await knex.raw(
    `SELECT setval('calendar_treatment_calendar_treatment_id_seq', 1, false)`,
  );
  await knex('treatment_department').del();
  await knex.raw(
    `SELECT setval('treatment_department_treatment_department_id_seq', 1, false)`,
  );
  await knex('portal_hosp_station').del();
  await knex('portal_hospital_notification').del();
  await knex.raw(
    `SELECT setval('portal_hospital_notification_hospital_notification_id_seq', 1, false)`,
  );
  await knex('portal_hospital_staff').del();
  await knex.raw(
    `SELECT setval('portal_hospital_staff_hospital_staff_id_seq', 1, false)`,
  );
  await knex('portal_hospital').del();
  await knex.raw(`SELECT setval('portal_hospital_hospital_id_seq', 1, false)`);
}
