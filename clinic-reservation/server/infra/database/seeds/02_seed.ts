import { BusinessTime } from '@/models/BusinessTime';
import { HospitalExamination } from '@/models/HospitalExamination';
import { HospitalNotification } from '@/models/HospitalNotification';
import { HospitalPicture } from '@/models/HospitalPicture';
import { HospitalStation } from '@/models/HospitalStation';
import { HospitalTag } from '@/models/HospitalTag';
import { Picture } from '@/models/Picture';
import { PortalHospital } from '@/models/PortalHospital';
import { PortalStaff } from '@/models/Staff';
import { StaffPicture } from '@/models/StaffPicture';
import { TreatmentDepartment } from '@/models/TreatmentDepartment';
import { randomInt } from 'crypto';
import dayjs from 'dayjs';
import { Knex } from 'knex';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const gimei = require('@longgt-public/node-gimei')();

const BASE_MODEL = {
  createdAt: new Date(),
  createdBy: 'DBA',
  updatedAt: new Date(),
  updatedBy: 'DBA',
};

const getRandomFloat = (min: number, max: number): number => {
  return Math.random() * (max - min) + min;
};

const getRandomInt = (max: number): number => {
  return Math.floor(Math.random() * max);
};

const currentLocation = { longitude: 139.6876531, latitude: 35.6777496 };

const radius = 5;

const generateRandomLocationNear = (): {
  longitude: number;
  latitude: number;
} => {
  const earthRadius = 6371; // Earth's radius in kilometers
  const randomDistance = Math.sqrt(Math.random()) * radius; // Random distance within the radius

  // Calculate new latitude and longitude
  const newLatitude =
    currentLocation.latitude + (randomDistance / earthRadius) * (180 / Math.PI);
  const newLongitude =
    currentLocation.longitude +
    ((randomDistance / earthRadius) * (180 / Math.PI)) /
      Math.cos(currentLocation.latitude * (Math.PI / 180));

  return { longitude: newLongitude, latitude: newLatitude };
};

const generateRandomLocationInTokyo = (): {
  latitude: number;
  longitude: number;
} => {
  // Define the geographical boundaries of Tokyo
  const minLatitude = 35.682839;
  const maxLatitude = 35.817813;
  const minLongitude = 139.649564;
  const maxLongitude = 139.910202;

  const latitude = getRandomFloat(minLatitude, maxLatitude);
  const longitude = getRandomFloat(minLongitude, maxLongitude);

  return { latitude, longitude };
};

const PICTURES_TOTAL = 4;

const initPictures = async (knex: Knex) => {
  const filenames: string[] = Array.from(
    { length: PICTURES_TOTAL },
    (_, index) => `テスト画像${index + 1}.jpeg`,
  );

  const filepaths = [
    'hospitalId_1/images/テスト画像1_679f42fd-3c68-4a45-bd73-51690f528b7b.jpeg',
    'hospitalId_1/images/テスト画像2_2b0f3520-b10e-4ea9-9f89-ebf2a0322c40.jpeg',
    'hospitalId_1/images/テスト画像3_58498eda-ec20-4f9b-923e-81f7172590ac.png',
    'hospitalId_1/images/テスト画像4_c02936db-757d-40a3-8e6b-150a27c02f10.jpeg',
  ];

  const pictures: Partial<Picture>[] = Array.from(
    { length: filenames.length },
    (_, index) => ({
      ...BASE_MODEL,
      filepath: filepaths[index],
      fileName: filenames[index],
    }),
  );

  await knex('portal_pict').insert(pictures);
};

const HOSPITAL_INF_TOTAL = 6;

const HOSPITAL_TOTAL = 1000; // TODO: Change this for testing with larger data.

const initHospitals = async (knex: Knex) => {
  const cityNames = await knex('portal_m_city')
    .select('name')
    .limit(HOSPITAL_TOTAL);

  const hospitals: Partial<PortalHospital>[] = Array.from(
    { length: HOSPITAL_TOTAL },
    (_, index) => {
      const name = gimei.name();
      const randomLocation =
        getRandomInt(2) === 1
          ? generateRandomLocationInTokyo()
          : generateRandomLocationNear();
      gimei.reset();
      return {
        ...BASE_MODEL,
        isActive: true,
        isCarpark: true,
        fax: `${name.kanji()} ファックス`,
        hpInfId: (index % HOSPITAL_INF_TOTAL) + 1,
        name: `${name.kanji()} クリニック`,
        address1: `${index + 1} ${cityNames[index % cityNames.length].name}`,
        address2: `${index + 1} ${cityNames[index % cityNames.length].name}`,
        latitude: randomLocation.latitude,
        longitude: randomLocation.longitude,
        description: `当院では初診${
          index + 1
        }。当院では初診・再診問わず、オンライン診療を実施しております。少しの体調変化ちょっといつもの薬が足りなくてなどのご相談もお受けしております。 `,
        telephone: '0123456789',
        carparkDetail: '駐車場有',
        postCode: '1500043',
        email: `hospital${index}@gmail.com`,
        descriptionTitle: `病院内容${index + 1}`,
        paymentDetails: 'すべて',
        holidayDetail: '木・日 / 祝日',
        timelineDescription: '補足説明補足説明補足説明補足説明補足説明補足説明',
        businessTimeDetail: `月火木金：09:00 ~ 12:00 / 13:30 ~18:00
        土：09:00 ~ 12:00`,
      };
    },
  );

  for (let i = 0; i < hospitals.length; i += 1000) {
    const batch = hospitals.slice(i, i + 1000);
    await knex('portal_hospital').insert(batch);
  }
};

const HOSPITAL_STAFFS = 3;

const initHospitalStaffs = async (knex: Knex) => {
  const hospitalStaffs: Partial<PortalStaff>[] = Array.from(
    { length: HOSPITAL_STAFFS * HOSPITAL_INF_TOTAL },
    (_, index) => {
      const name = gimei.name();

      gimei.reset();

      return {
        ...BASE_MODEL,
        hospitalId: (index % HOSPITAL_INF_TOTAL) + 1,
        name: `${name.kanji()}`,
        order: ~~(index / HOSPITAL_INF_TOTAL) + 1,
        description: `北里大学消化器${
          index + 1
        }年。北里大学消化器・肝臓内科講師 同病院消化器・肝臓内科副部長 `,
      };
    },
  );

  await knex('portal_hospital_staff').insert(hospitalStaffs);
};

const HOSPITAL_NEWS = 3;

const initHospitalNews = async (knex: Knex) => {
  const news: Partial<HospitalNotification>[] = Array.from(
    { length: HOSPITAL_NEWS * HOSPITAL_INF_TOTAL },
    (_, index) => {
      const name = gimei.name();
      const startDate = dayjs().add(index, 'd').toDate();
      const endDate = dayjs()
        .add(index + 7, 'd')
        .toDate();

      gimei.reset();

      return {
        ...BASE_MODEL,
        portalHospitalId: (index % HOSPITAL_INF_TOTAL) + 1,
        hospitalStaffId: index + 1,
        title: `${name.kanji()}のお知らせ`,
        status: ~~(index / HOSPITAL_INF_TOTAL),
        description: `WEBでの初診予約申込受付をはじめました${
          index + 1
        }年。WEBでの初診予約申込受付をはじめましたWEBでの初診予約申込受付をはじめましたWEBでの初診予約申込受付をはじめました `,
        startDate,
        endDate,
      };
    },
  );

  await knex('portal_hospital_notification').insert(news);
};

const initBusinessTimes = async (knex: Knex) => {
  const businessTimes: Partial<BusinessTime>[] = Array.from(
    { length: HOSPITAL_TOTAL },
    (_, index) => ({
      ...BASE_MODEL,
      hospitalId: index + 1,
      startTime: '05:00',
      endTime: '08:00',
      monFlag: getRandomInt(2),
      tueFlag: getRandomInt(2),
      wedFlag: getRandomInt(2),
      thuFlag: getRandomInt(2),
      friFlag: getRandomInt(2),
      satFlag: getRandomInt(2),
      sunFlag: getRandomInt(2),
      holidayFlag: getRandomInt(2),
    }),
  );

  for (let i = 0; i < businessTimes.length; i += 1000) {
    const batch = businessTimes.slice(i, i + 1000);
    await knex('portal_business_time').insert(batch);
  }
};

const TAG_TOTAL = 5;

const initHospTags = async (knex: Knex) => {
  const hospTags: Partial<HospitalTag>[] = [];
  Array.from({ length: HOSPITAL_TOTAL }, (_, index) => {
    hospTags.push({
      ...BASE_MODEL,
      hospitalId: index + 1,
      tagId: (index % TAG_TOTAL) + 1,
    });
  });

  for (let i = 0; i < hospTags.length; i += 1000) {
    const batch = hospTags.slice(i, i + 1000);
    await knex('portal_hosp_tag').insert(batch);
  }
};

const initHospitalPictures = async (knex: Knex) => {
  const hospPics: Partial<HospitalPicture>[] = [];

  Array.from({ length: HOSPITAL_TOTAL * PICTURES_TOTAL }, (_, index) => {
    hospPics.push({
      ...BASE_MODEL,
      hospitalId: (index % HOSPITAL_TOTAL) + 1,
      type: 1,
      pictId: ~~(index / HOSPITAL_TOTAL) + 1,
    });
  });

  for (let i = 0; i < hospPics.length; i += 1000) {
    const batch = hospPics.slice(i, i + 1000);
    await knex('portal_hosp_picture').insert(batch);
  }
};

const initStaffPictures = async (knex: Knex) => {
  const staffPics: Partial<StaffPicture>[] = [];

  Array.from({ length: HOSPITAL_STAFFS * HOSPITAL_INF_TOTAL }, (_, index) => {
    staffPics.push({
      ...BASE_MODEL,
      hospitalStaffId: index + 1,
      pictId: randomInt(1, PICTURES_TOTAL),
    });
  });

  await knex('portal_staff_picture').insert(staffPics);
};

const initExamination = async (knex: Knex) => {
  const examinationCategories = await knex('portal_m_examination').select(
    'examination_id',
  );

  const examinations: Partial<HospitalExamination>[] = [];

  Array.from({ length: HOSPITAL_TOTAL }, (_, index) => {
    examinations.push({
      ...BASE_MODEL,
      portalHospitalId: index + 1,
      examinationId: (index % examinationCategories.length) + 1,
    });
  });

  for (let i = 0; i < examinations.length; i += 1000) {
    const batch = examinations.slice(i, i + 1000);
    await knex('portal_hospital_examination').insert(batch);
  }
};

const initHospitalStations = async (knex: Knex) => {
  const tStation = await knex('portal_m_station')
    .select('station_id')
    .limit(HOSPITAL_TOTAL);

  const hospStations: Partial<HospitalStation>[] = [];
  Array.from({ length: HOSPITAL_TOTAL }, (_, index) => {
    hospStations.push({
      ...BASE_MODEL,
      hospitalId: index + 1,
      stationId: Number(tStation[index % tStation.length].stationId),
      walkingMinute: (index % 5) + 1,
    });
  });

  for (let i = 0; i < hospStations.length; i += 1000) {
    const batch = hospStations.slice(i, i + 1000);
    await knex('portal_hosp_station').insert(batch);
  }
};

const initTreatmentDepartments = async (knex: Knex) => {
  const treatmentCategories = await knex('m_treatment_category').select('*');
  const treat: Partial<TreatmentDepartment>[] = Array.from(
    { length: HOSPITAL_INF_TOTAL * 3 },
    (_, index) => ({
      ...BASE_MODEL,
      title: treatmentCategories[index % treatmentCategories.length].name,
      description: '',
      treatmentType: 1,
      treatmentMethod: randomInt(3),
      firstConsultationTime: 30,
      nextConsultationTime: 30,
      treatmentDepartmentStatus: 1,
      portalPublicStatus: 1,
      isDeleted: randomInt(2),
      order: index + 1,
      hospitalId: (index % HOSPITAL_INF_TOTAL) + 1,
      treatmentCategoryId: (index % treatmentCategories.length) + 1,
    }),
  );

  for (let i = 0; i < treat.length; i += 1000) {
    const batch = treat.slice(i, i + 1000);
    await knex('treatment_department').insert(batch);
  }
};

export async function seed(knex: Knex): Promise<void> {
  // portal_hospital
  await initHospitals(knex);

  // portal_hospital_examination
  await initExamination(knex);

  // portal_pict
  await initPictures(knex);

  // portal_business_time
  await initBusinessTimes(knex);

  // portal_hosp_tag
  await initHospTags(knex);

  // portal_hosp_station
  await initHospitalStations(knex);

  // treatment_department
  await initTreatmentDepartments(knex);

  // portal_hosp_picture
  await initHospitalPictures(knex);

  // portal_hospital_staff
  await initHospitalStaffs(knex);

  // portal_hospital_notification
  await initHospitalNews(knex);

  // portal_staff_picture
  await initStaffPictures(knex);
}
