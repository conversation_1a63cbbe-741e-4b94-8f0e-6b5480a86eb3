#!/bin/sh
# This script copy DB to local
if [ $# != 1 ] ; then
  echo 'Must have dev or stg'
  exit 0;
fi

if [ $# = 1 ] ; then
  echo 'copy from '$1
  db_env=$1
fi


if [ "$db_env" = "dev" ]; then
  DB_USER="dev_gmoht"
  DB_PW="dev_gmoht"
  DB_NAME="dev_gmoht"
  DB_SCHEMA="dev_gmoht"
fi

SSH_ALIAS="devht@*************"
LOCAL_DB_USER="root"
LOCAL_DB_PW="root"
LOCAL_DB_NAME="local_gmoht"
LOCAL_DB_SCHEMA="local_gmoht"
LOCAL_PORT=5433
DUMP_FILE="./infra/scripts/dump.sql"

# 注意: Confirm before execute 
# https://gmofhg.slack.com/archives/C06NJD4RX2N/p1723092542183999
# ssh -i ~/.ssh/ssh_devht.pem $SSH_ALIAS "pg_dump -O -x -a --dbname=postgresql://$DB_USER:$<EMAIL>/$DB_NAME --schema $DB_SCHEMA" --exclude-table="portal_m_import_*" > $DUMP_FILE
# sed -i "" "s/$DB_SCHEMA/$LOCAL_DB_SCHEMA/g" $DUMP_FILE
# sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" $DUMP_FILE

# Export the PGPASSWORD environment variable
export PGPASSWORD=$LOCAL_DB_PW

# Truncate all tables in the database
psql -U $LOCAL_DB_USER -d $LOCAL_DB_NAME -h localhost -p $LOCAL_PORT <<EOF
DO \$\$
DECLARE
    r RECORD;
BEGIN
    -- Truncate all tables
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = '$LOCAL_DB_SCHEMA') LOOP
        EXECUTE 'TRUNCATE TABLE $LOCAL_DB_SCHEMA."' || r.tablename || '" CASCADE;';
    END LOOP;
END
\$\$;
EOF

# Reset all sequences
psql -U $LOCAL_DB_USER -d $LOCAL_DB_NAME -h localhost -p $LOCAL_PORT <<EOF
DO \$\$
DECLARE
    r RECORD;
BEGIN
    -- Temporarily adjust and reset sequences
    FOR r IN (SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = '$LOCAL_DB_SCHEMA') LOOP
        -- Remove MINVALUE constraint
        EXECUTE FORMAT('ALTER SEQUENCE $LOCAL_DB_SCHEMA.%I MINVALUE 1', r.sequence_name);
        
        -- Reset the sequence to 1
        EXECUTE FORMAT('SELECT pg_catalog.setval(''$LOCAL_DB_SCHEMA.%I'', 1, false)', r.sequence_name);
    END LOOP;
END
\$\$;
EOF

# Unset the PGPASSWORD environment variable for security reasons
unset PGPASSWORD

# Reset DB
PGPASSWORD=$LOCAL_DB_PW psql -h localhost -p $LOCAL_PORT -U $LOCAL_DB_USER -d $LOCAL_DB_NAME -c "SET search_path TO ${LOCAL_DB_SCHEMA};" -f $DUMP_FILE > psql_output.txt 2>&1


error_count=$(grep -c "ERROR" psql_output.txt)
if [ $error_count -gt 0 ]; then
    echo "DB initialization encountered errors. Number of errors: $error_count"
    echo "Error details:"
    grep "ERROR" psql_output.txt
else
    echo "\n\nDB initialization complete with NO errors"
fi


rm psql_output.txt
