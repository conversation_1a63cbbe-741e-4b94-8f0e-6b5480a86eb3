# This file is automatically generated from terraform
version: 0.0
Resources:
  - TargetService:
      Type: AWS::ECS::Service
      Properties:
        TaskDefinition: "arn:aws:ecs:ap-northeast-1:898119824224:task-definition/untf-booking-prd"
        LoadBalancerInfo:
          ContainerName: "tf-booking-prd-api-container"
          ContainerPort: "3000" # Specify the port for your container where traffic reroutes
        PlatformVersion: "LATEST" # Specify the version of your Amazon ECS Service
        NetworkConfiguration:
          AwsvpcConfiguration:
            Subnets: ["subnet-015910abba2e55a99","subnet-0415f9b4e7c3bf2eb"] # Specify one or more comma-separated subnets in your Amazon ECS service
            # SecurityGroups: ["sg-0a8a013e99d7f1d6e"]
            SecurityGroups: ["<SECURITY_GROUP_FOR_ECS>"]
            AssignPublicIp: "DISABLED" # Specify "ENABLED" or "DISABLED"
