version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - apt-get update
      - apt-get install -y git
      - sudo apt-get install --reinstall -y libssl-dev
  pre_build:
    commands:
      - echo Check build environments...
      - pwd
      - aws --version
      - node -v
      - npm -v
      - ls -la
      - sh server/infra/cicd/codepipeline/check-params-prd.sh
      - echo Login Docker
      - aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin $ECR_URL
  build:
    commands:
      # git tag
      - mkdir -p /root/.ssh
      - aws ssm get-parameter --name /COMMON/PRD/GITHUB_ACCESS_KEY --with-decryption --query Parameter.Value --output text > /root/.ssh/id_ed25519
      - chmod 600 /root/.ssh/id_ed25519
      - DATE_TAG=$(date +%Y%m%d%H%M)  # 日時タグを生成（YYYYMMDDHHmm形式）
      - echo $DATE_TAG
      - eval "$(ssh-agent -s)"
      - ssh-add /root/.ssh/id_ed25519
      - git config --global user.email "<EMAIL>"
      - git config --global user.name "devhealthcare"
      - cd ..
      - <NAME_EMAIL>:bizleap-healthcare/clinic-reservation.git
      - cd clinic-reservation
      - git checkout prd # デフォルトブランチはdevなのでこれがないと想定外のリリースをしてしまう
      - |
        echo Checking if tag $DATE_TAG already exists...
        if git rev-parse ${DATE_TAG} >/dev/null 2>&1
        then
          echo "Tag ${DATE_TAG} already exists. Skipping tag creation and push."
        else
          echo "Creating Git tag ${DATE_TAG}..."
          git tag $DATE_TAG
          git push origin --tags
        fi

      - echo Build started on `date`
      - pwd
      - ls
      - cd server
      - pwd
      - ls
      - echo Remove .env file
      - rm -rf .env
      - yarn install
      # - yarn test
      - echo Build Docker Image
      - docker build -t clinic:$DATE_TAG -f infra/docker/Dockerfile.production .
      - echo Push image to ECR
      - docker tag clinic:$DATE_TAG $ECR_URL/tf-booking-prd:$DATE_TAG
      - docker push $ECR_URL/tf-booking-prd:$DATE_TAG
    on-failure: ABORT
  post_build:
    commands:
      - pwd
      - ls
      - cd ../
      - pwd
      - ls
      - printf '{"ImageURI":"%s"}' $IMAGE_URI:$DATE_TAG > imageDetail.json
      - printf '[{"imageUri":"%s"}]' $IMAGE_URI:$DATE_TAG > imagedefinitions.json
      - sed -i -e "s#<SECURITY_GROUP_FOR_ECS>#${SECURITY_GROUP_FOR_ECS}#" server/infra/cicd/codepipeline/prd-appspec.yml
      - echo CD completed on `date`
    on-failure: ABORT

artifacts:
  files:
    - imageDetail.json
    - imagedefinitions.json
    - server/infra/cicd/codepipeline/prd-appspec.yml
    - server/infra/cicd/codepipeline/prd-taskdef.json
  base-directory: ../clinic-reservation
  discard-paths: no

cache:
  paths:
    - /root/.cache/yarn/**/*
    - node_modules/**/*
