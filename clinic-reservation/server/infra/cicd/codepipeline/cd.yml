version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - |
        if [ $ENV = "prd" ]; then
          apt-get update
          apt-get install -y git
          sudo apt-get install --reinstall -y libssl-dev
        fi
  pre_build:
    commands:
      - echo Check build environments...
      - pwd
      - echo env is $ENV
      - aws --version
      - node -v
      - npm -v
      - ls -la
      - sh server/infra/cicd/codepipeline/check-params.sh
      - echo Login Docker
      - aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin $ECR_URL
  build:
    commands:
      - echo Build started on `date`

      - |
        if [ $ENV = "prd" ]; then
          # git tag
          mkdir -p /root/.ssh
          aws ssm get-parameter --name /COMMON/PRD/GITHUB_ACCESS_KEY --with-decryption --query Parameter.Value --output text > /root/.ssh/id_ed25519
          chmod 600 /root/.ssh/id_ed25519
          # Generate date tag（YYYYMMDDHHmm format）
          DATE_TAG=$(date +%Y%m%d%H%M)
          echo $DATE_TAG
          eval "$(ssh-agent -s)"
          ssh-add /root/.ssh/id_ed25519
          git config --global user.email "<EMAIL>"
          git config --global user.name "devhealthcare"
          cd ..
          <NAME_EMAIL>:bizleap-healthcare/clinic-reservation.git
          cd clinic-reservation
          # Below command is necessary to release from prd branch because default branch is dev.
          git checkout prd
          - |
            echo Checking if tag $DATE_TAG already exists...
            if git rev-parse ${DATE_TAG} >/dev/null 2>&1
            then
              echo "Tag ${DATE_TAG} already exists. Skipping tag creation and push."
            else
              echo "Creating Git tag ${DATE_TAG}..."
              git tag $DATE_TAG
              git push origin --tags
            fi
        fi

      - cd server
      - echo Remove .env file
      - rm -rf .env
      - yarn install
      # - yarn test
      - echo Build Docker Image
      - |
        if [ $ENV = "prd" ]; then
          docker build -t clinic:$DATE_TAG -f infra/docker/Dockerfile.production .
          docker tag clinic:$DATE_TAG $ECR_URL/tf-booking-prd:$DATE_TAG
          docker push $ECR_URL/tf-booking-prd:$DATE_TAG
        else
          docker build -t clinic -f infra/docker/Dockerfile.production .
          docker tag clinic:latest $ECR_URL/tf-booking-$ENV:latest
          docker push $ECR_URL/tf-booking-$ENV:latest
        fi
    on-failure: ABORT
  post_build:
    commands:
      - cd ..
      - |
        if [ $ENV = "prd" ]; then
          printf '{"ImageURI":"%s"}' $IMAGE_URI:$DATE_TAG > imageDetail.json
          printf '[{"imageUri":"%s"}]' $IMAGE_URI:$DATE_TAG > imagedefinitions.json
        else
          printf '{"ImageURI":"%s"}]' $IMAGE_URI > $CODEBUILD_SRC_DIR/imageDetail.json
          printf '[{"imageUri":"%s"}]' $IMAGE_URI > $CODEBUILD_SRC_DIR/imagedefinitions.json
        fi

      - sed -i -e "s#<AWS_ACCOUNT_ID>#${AWS_ACCOUNT_ID}#" server/infra/cicd/codepipeline/appspec.yml
      - sed -i -e "s#<CONTAINER_NAME>#${CONTAINER_NAME}#" server/infra/cicd/codepipeline/appspec.yml
      - sed -i -e "s#<PRIVATE_SUBNET_1A>#${PRIVATE_SUBNET_1A}#" server/infra/cicd/codepipeline/appspec.yml
      - sed -i -e "s#<PRIVATE_SUBNET_1C>#${PRIVATE_SUBNET_1C}#" server/infra/cicd/codepipeline/appspec.yml
      - sed -i -e "s#<SECURITY_GROUP_FOR_ECS>#${SECURITY_GROUP_FOR_ECS}#" server/infra/cicd/codepipeline/appspec.yml
      - sed -i -e "s#<TASK_DEFINITION_FAMILY>#${TASK_DEFINITION_FAMILY}#" server/infra/cicd/codepipeline/appspec.yml

      - sed -i -e "s#<AWS_ACCOUNT_ID>#${AWS_ACCOUNT_ID}#" server/infra/cicd/codepipeline/appspec-prd.yml
      - sed -i -e "s#<CONTAINER_NAME>#${CONTAINER_NAME}#" server/infra/cicd/codepipeline/appspec-prd.yml
      - sed -i -e "s#<PRIVATE_SUBNET_1A>#${PRIVATE_SUBNET_1A}#" server/infra/cicd/codepipeline/appspec-prd.yml
      - sed -i -e "s#<PRIVATE_SUBNET_1C>#${PRIVATE_SUBNET_1C}#" server/infra/cicd/codepipeline/appspec-prd.yml
      - sed -i -e "s#<SECURITY_GROUP_FOR_ECS>#${SECURITY_GROUP_FOR_ECS}#" server/infra/cicd/codepipeline/appspec-prd.yml
      - sed -i -e "s#<TASK_DEFINITION_FAMILY>#${TASK_DEFINITION_FAMILY}#" server/infra/cicd/codepipeline/appspec-prd.yml

      - sed -i -e "s#<AWS_ACCOUNT_ID>#${AWS_ACCOUNT_ID}#" server/infra/cicd/codepipeline/taskdef.json
      - sed -i -e "s#<CONTAINER_NAME>#${CONTAINER_NAME}#" server/infra/cicd/codepipeline/taskdef.json
      - sed -i -e "s#<TASK_DEFINITION_FAMILY>#${TASK_DEFINITION_FAMILY}#" server/infra/cicd/codepipeline/taskdef.json
      - sed -i -e "s#<ENV>#${ENV}#" server/infra/cicd/codepipeline/taskdef.json
      - sed -i -e "s#<ENV_PARAMETER>#${ENV_PARAMETER}#" server/infra/cicd/codepipeline/taskdef.json
      - sed -i -e "s#<TASKROLE>#${TASKROLE}#" server/infra/cicd/codepipeline/taskdef.json
      - sed -i -e "s#<EXECUTIONROLE>#${EXECUTIONROLE}#" server/infra/cicd/codepipeline/taskdef.json

      - echo CD completed on `date`
    on-failure: ABORT

artifacts:
  files:
    - imageDetail.json
    - imagedefinitions.json
    - server/infra/cicd/codepipeline/appspec.yml
    - server/infra/cicd/codepipeline/appspec-prd.yml
    - server/infra/cicd/codepipeline/taskdef.json
  base-directory: $CODEBUILD_SRC_DIR
  discard-paths: no

cache:
  paths:
    - /root/.cache/yarn/**/*
    - node_modules/**/*
