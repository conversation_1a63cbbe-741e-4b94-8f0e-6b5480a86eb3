#!/bin/bash

# タスク定義から secrets を抽出し、AWS SSM Parameter Store にパラメータが存在し、
# Key: TF_MANAGED, Value: true のタグが付いているか確認するスクリプト

UPPER_ENV=$(echo "$ENV" | tr '[:lower:]' '[:upper:]')
# 環境変数 ENV が設定されているか確認
if [ -z "$UPPER_ENV" ]; then
  echo "ERROR: ENV is not set. Please set the ENV variable before running the script."
  exit 1
fi

# タスク定義ファイルを読み込む
TASK_DEF_FILE="./server/infra/cicd/codepipeline/taskdef.json"

# secrets の valueFrom を抽出
SECRETS=$(jq -r '.containerDefinitions[].secrets[].valueFrom' $TASK_DEF_FILE)

echo "Checking Parameter Store for secrets in $TASK_DEF_FILE..."
echo "Using ENV: $UPPER_ENV"
echo ""

# 各パラメータの存在確認とタグチェック
for SECRET in $SECRETS; do
  # <PARAMETER_ENV> を $ENV に置き換え
  PARAM_NAME=$(echo $SECRET | sed "s|<ENV_PARAMETER>|$UPPER_ENV|g" | sed 's|^arn:aws:ssm:[^:]*:[^:]*:parameter||')

  # Parameter Store にパラメータが存在するか確認
  aws ssm get-parameter --name "$PARAM_NAME" --region "ap-northeast-1" >/dev/null 2>&1

  if [ $? -ne 0 ]; then
    echo "[✘] Parameter not found: $PARAM_NAME"
    exit 1
  else
    echo "[✔] Parameter exists: $PARAM_NAME"
  fi

  # パラメータのタグを取得して TF_MANAGED=true のタグが付いているか確認
  # TAGS=$(aws ssm list-tags-for-resource --resource-type "Parameter" --resource-id "$PARAM_NAME" --region "ap-northeast-1" 2>/dev/null)

  # if echo "$TAGS" | jq -e '.TagList[] | select(.Key=="TF_MANAGED" and .Value=="true")' >/dev/null; then
  #     echo "[✔] Parameter exists with TF_MANAGED=true tag: $PARAM_NAME"
  # else
  #     echo "[✘] Missing or incorrect tag: $PARAM_NAME"
  #     echo "ERROR: Missing TF_MANAGED=true tag. Exiting."
  #     exit 1
  # fi
done

echo ""
echo "Parameter check OK!!✨✨"
