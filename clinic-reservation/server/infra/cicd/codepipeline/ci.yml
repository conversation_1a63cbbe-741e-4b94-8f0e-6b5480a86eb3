version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
  pre_build:
    commands:
      - echo Check build environments...
      - pwd
      - aws --version
      - node -v
      - npm -v
      - ls -la
  build:
    commands:
      - echo Build started on `date`
      - cd server
      - cp .env.local .env
      - yarn install
      - yarn lint
      - yarn test
      - yarn build
    on-failure: ABORT
  post_build:
    commands:
      - echo CI completed on `date`
    on-failure: ABORT

artifacts:
  files:
    - imageDetail.json
    - imagedefinitions.json

cache:
  paths:
    - /root/.cache/yarn/**/*
    - node_modules/**/*
