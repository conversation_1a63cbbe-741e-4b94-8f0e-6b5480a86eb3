version: 0.0
Resources:
  - TargetService:
      Type: AWS::ECS::Service
      Properties:
        TaskDefinition: 'arn:aws:ecs:ap-northeast-1:<AWS_ACCOUNT_ID>:task-definition/<TASK_DEFINITION_FAMILY>'
        LoadBalancerInfo:
          ContainerName: '<CONTAINER_NAME>'
          ContainerPort: '3000' # Specify the port for your container where traffic reroutes
        PlatformVersion: 'LATEST' # Specify the version of your Amazon ECS Service
        NetworkConfiguration:
          AwsvpcConfiguration:
            # Subnets: ["subnet-0a8949c9fb43b69f8","subnet-00d4d1f6aec0edace"]
            Subnets: ['<PRIVATE_SUBNET_1A>', '<PRIVATE_SUBNET_1C>']
            SecurityGroups: ['<SECURITY_GROUP_FOR_ECS>']
            AssignPublicIp: 'DISABLED' # Specify "ENABLED" or "DISABLED"
        CapacityProviderStrategy:
          - CapacityProvider: 'FARGATE_SPOT'
            Weight: 0
            Base: 0
          - CapacityProvider: 'FARGATE'
            Weight: 1
            Base: 1
