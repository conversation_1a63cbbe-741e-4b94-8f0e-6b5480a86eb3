// Import Swiper styles
import "swiper/css";

import dynamic from "next/dynamic";
import Link from "next/link";
import { useRouter } from "next/router";
import { useState } from "react";
import styled from "styled-components";
import { Swiper, SwiperSlide } from "swiper/react";

import { Maybe, PortalHospital } from "@/api/generated/types";
import Button, { ButtonType } from "@/components/atoms/button/Button";
import Divider from "@/components/atoms/Divider";
import Footer from "@/components/atoms/footer/Footer";
import { FooterSlot } from "@/components/atoms/footer/FooterSlot";
import { TrendingFlatFill0Wght400Grad0Opsz241 } from "@/components/atoms/icon/generated";
import ImageAtom from "@/components/atoms/Image";
import { ImageModal } from "@/components/atoms/ImageModal";
import SectionTitle from "@/components/atoms/text/SectionTitle";
import { MAX_WIDTH_MAIN_CONTENT, PAGE_PATH } from "@/constants";
import { EXAMINATION_TYPE } from "@/constants/hospital";
import { SPECIFIED_COMMERCIAL_TRANSACTION_ACT } from "@/constants/link";
import {
  GlobalStateType,
  useGlobalContext,
} from "@/providers/GlobalStateProvider";
import {
  HospitalInfoPageType,
  HospitalStateActionType,
  useHospitalContext,
} from "@/providers/HospitalStateProvider";
import { COLOR } from "@/styles/style";
import { encodeId, scrollToHospitalContentMenu } from "@/util";
import { formatPhoneNumber } from "@/util/phonenumber";

import { DirectConfirmModal } from "../../import-hospital/DirectConfirmModal";
import { WorkingHours } from "../WorkingHours";

const SubmenuListComponent = dynamic(
  () => import("@/components/molecules/hospital/top/SubmenuListComponent")
);

const NotificationTable = dynamic(
  () => import("@/components/molecules/hospital/top/NotificationTable")
);

const WeeklyBusinessTimeTable = dynamic(
  () => import("@/components/molecules/hospital/WeeklyBusinessTimeTable")
);

const HospitalContent = styled.div`
  width: 100%;
`;

const Section = styled.section`
  margin-bottom: 24px;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    margin-bottom: 40px;
  }
`;

const HospitalInfoSection = styled(Section)`
  background-color: #fff;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    padding: 12px;
  }
`;

const SectionTitleText = styled(SectionTitle)`
  font-weight: bold;
  font-size: 18px;
  color: ${COLOR.TEXT_PRIMARY};
  margin-bottom: 12px;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    margin-bottom: 20px;
  }
`;

const SectionSubTitle = styled(SectionTitle)`
  font-size: 16px;
  color: ${COLOR.TEXT_PRIMARY};
  font-weight: bold;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin-bottom: 12px;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    margin-bottom: 20px;
  }
`;

const HospitalInfoTitle = styled(SectionTitleText)`
  margin-bottom: 12px;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    margin-bottom: 20px;
  }
`;

const Container = styled.div`
  padding: 0 24px;
  margin: auto;
`;

const CommonText = styled.div`
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
`;

const TextPrimary = styled(CommonText)`
  color: ${COLOR.TEXT_PRIMARY};
  white-space: pre-line;
`;

const TextLinkPrimary = styled(CommonText)`
  color: ${COLOR.TEXT_LINK};
  cursor: pointer;
  text-decoration: underline;
  white-space: pre-wrap;
  word-wrap: break-word;
  width: 66%;
`;

const DetailInfo = styled(CommonText)`
  width: 66%;
  color: ${COLOR.TEXT_PRIMARY};
  white-space: pre-wrap;
  word-wrap: break-word;
`;

const BasicInfoItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    padding: 20px 0px;
  }
`;

const InfoTitle = styled(CommonText)`
  color: ${COLOR.TEXT_SECONDARY};
  width: 33%;
  line-height: normal;
  align-self: flex-start;
`;

const PhoneNumber = styled.a`
  color: ${COLOR.TEXT_LINK};
  font-size: 14px;
  white-space: pre-wrap;
  word-wrap: break-word;
  width: 66%;
`;

const ShowMoreText = styled.div`
  font-size: 14px;
  text-align: right;
  color: ${COLOR.TEXT_PRIMARY};
  cursor: pointer;
  margin-top: 12px;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    margin-top: 20px;
  }
`;

const TimelineDes = styled(DetailInfo)`
  width: 100%;
`;

const SliderWrapper = styled.div`
  max-width: 800px;
  margin: 0 12px 24px;
`;

const Img = styled(ImageAtom)`
  height: 105px !important;
`;

const StyledDivider = styled(Divider)`
  margin: 0 12px;
  @media only screen and (min-width: ${MAX_WIDTH_MAIN_CONTENT}) {
    margin: 0px;
  }
`;

const TrendingFlatFill0Wght400Grad0Opsz241Styled = styled(
  TrendingFlatFill0Wght400Grad0Opsz241
)`
  padding-top: 3px;
`;

const StyledSwiperSlide = styled(SwiperSlide)`
  width: auto !important;
`;

const StyledLink = styled(Link)`
  display: block;
  color: ${COLOR.TEXT_LINK};
  text-align: right;
  padding-bottom: 12px;
  padding-right: 24px;
  font-size: 14px;
`;

const MailAddress = styled(Link)`
  color: ${COLOR.TEXT_LINK};
  font-size: 14px;
`;

type Props = {
  hospital: PortalHospital;
  onGoToMapPage: () => void;
};

const TopContentComponent: React.FC<Props> = ({ hospital, onGoToMapPage }) => {
  const { push } = useRouter();
  const {
    hospitalStateContext: { treatmentDepartments },
    dispatchHospitalState,
  } = useHospitalContext();
  const { dispatchGlobalState } = useGlobalContext();
  const [homePageUrl, setHomePageUrl] = useState<Maybe<string>>(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const openModal = (index: number) => {
    setCurrentIndex(index);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const phoneHref = hospital.telephone ? `tel:${hospital.telephone}` : "#";

  const handleClickOnNews = (newsId: number) => {
    push({
      pathname: PAGE_PATH.HOSPITAL_NEW,
      query: { id: encodeId(hospital.hospitalId) },
      hash: `notification${newsId}`,
    });
  };

  return (
    <HospitalContent>
      {isModalOpen && (
        <ImageModal
          srcs={hospital.pictures.map(
            (picture) => picture.pictureDetail.filepath
          )}
          onClose={closeModal}
          currentIndex={currentIndex}
          setCurrentIndex={setCurrentIndex}
        />
      )}

      {/* Image block  */}
      {!!hospital.pictures?.length && (
        <SliderWrapper>
          <Swiper
            slidesPerView="auto" // Allow slides to adjust based on content size
            spaceBetween={12}
            loop={false}
            centeredSlides={false} // Disable centering behavior
          >
            {hospital.pictures.map((picture, index) => (
              <StyledSwiperSlide key={picture.pictId + index}>
                <Img
                  src={picture.pictureDetail.filepath}
                  onClick={() => openModal(index)}
                />
              </StyledSwiperSlide>
            ))}
          </Swiper>
        </SliderWrapper>
      )}

      <Container>
        {/* Notification block  */}
        <Section>
          <SectionTitleText>お知らせ</SectionTitleText>
          {!!hospital.notifications?.length && (
            <>
              <NotificationTable
                notifications={hospital.notifications.slice(0, 3)}
                onClickOnNews={handleClickOnNews}
              />
              <ShowMoreText
                onClick={() => {
                  push({
                    pathname: PAGE_PATH.HOSPITAL_NEW,
                    query: { id: encodeId(hospital.hospitalId) },
                  });
                }}
              >
                お知らせ一覧を見る
                <TrendingFlatFill0Wght400Grad0Opsz241Styled />
              </ShowMoreText>
            </>
          )}
        </Section>

        {/* Description block  */}
        <Section>
          <SectionTitleText>クリニック紹介</SectionTitleText>
          <SectionSubTitle>{hospital.descriptionTitle}</SectionSubTitle>
          <TextPrimary>
            <pre>{hospital.description}</pre>
          </TextPrimary>
        </Section>

        {/* Business time block  */}
        <Section>
          <SectionTitleText>診療時間</SectionTitleText>
          {hospital.businessTimes?.length !== 0 && (
            <WeeklyBusinessTimeTable businessTimes={hospital.businessTimes} />
          )}
          <WorkingHours
            businessTimes={hospital.businessTimes}
            holidayDetail={hospital.holidayDetail}
          />
          <TimelineDes>{hospital.timelineDescription}</TimelineDes>
        </Section>

        {/* Submenu block  */}
        <Section>
          <SectionTitleText>診療メニュー</SectionTitleText>
          <SubmenuListComponent />
          {treatmentDepartments.length !== 0 && (
            <ShowMoreText
              onClick={() => {
                dispatchHospitalState({
                  type: HospitalStateActionType.PAGE_TYPE,
                  payload: HospitalInfoPageType.SUBMENU,
                });
                dispatchHospitalState({
                  type: HospitalStateActionType.UPDATE_SUBMENU_SEARCH_PARAMS,
                  payload: { hospitalId: hospital.hospitalId },
                });
                dispatchGlobalState({
                  type: GlobalStateType.UPDATE_ONLINE_TREATMENT_ROUTER,
                  payload: {
                    pageType: HospitalInfoPageType.SUBMENU,
                  },
                });
                scrollToHospitalContentMenu();
              }}
            >
              すべての診療メニューを見る
              <TrendingFlatFill0Wght400Grad0Opsz241Styled />
            </ShowMoreText>
          )}
        </Section>

        {/* Base Info block  */}
        <HospitalInfoTitle>基本情報</HospitalInfoTitle>
        <HospitalInfoSection>
          <BasicInfoItem>
            <InfoTitle>公式HP</InfoTitle>
            <TextLinkPrimary
              onClick={() => {
                const url = hospital.homePage || "";
                url && setHomePageUrl(url);
              }}
            >
              {hospital.homePage ? hospital.homePage : ""}
            </TextLinkPrimary>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>電話</InfoTitle>
            <PhoneNumber href={phoneHref}>
              {formatPhoneNumber(hospital.telephone ?? "")}
            </PhoneNumber>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>メールアドレス</InfoTitle>
            {hospital.mailAddress && (
              <MailAddress href={`mailto:${hospital.mailAddress}`}>
                {hospital.mailAddress}
              </MailAddress>
            )}
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>所在地</InfoTitle>
            <TextLinkPrimary onClick={onGoToMapPage}>
              {hospital.address1} {hospital.address2}
            </TextLinkPrimary>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>院長名</InfoTitle>
            <DetailInfo>{hospital.directorName ?? ""}</DetailInfo>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>駐車場</InfoTitle>
            <DetailInfo>
              {hospital.isCarpark ? "あり" : "なし"}
              <br />
              {hospital.carparkDetail}
            </DetailInfo>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>決済方法</InfoTitle>
            <DetailInfo>{hospital.paymentDetails ?? ""}</DetailInfo>
          </BasicInfoItem>
          <StyledLink
            href={SPECIFIED_COMMERCIAL_TRANSACTION_ACT}
            target="_blank"
          >
            特定商取引法
          </StyledLink>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>診療科目</InfoTitle>
            <DetailInfo>
              {hospital.treatmentCategories
                ? hospital.treatmentCategories
                    .map((item) => item.name)
                    .join("/")
                : ""}
            </DetailInfo>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>健診・ドック</InfoTitle>
            <DetailInfo>
              {hospital.examinations
                ? hospital.examinations
                    .map((item) =>
                      item.type === EXAMINATION_TYPE.MEDICAL_CHECKUP
                        ? item.name
                        : ""
                    )
                    .filter(Boolean)
                    .join("\n")
                : ""}
            </DetailInfo>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>検査・設備</InfoTitle>
            <DetailInfo>
              {hospital.examinations
                ? hospital.examinations
                    .map((item) =>
                      item.type === EXAMINATION_TYPE.FACILITY ? item.name : null
                    )
                    .filter(Boolean)
                    .join("、")
                : ""}
            </DetailInfo>
          </BasicInfoItem>
          <StyledDivider />
          <BasicInfoItem>
            <InfoTitle>専門医</InfoTitle>
            <DetailInfo>
              {hospital.specialists
                ? hospital.specialists
                    .map((item) => item.name)
                    .filter(Boolean)
                    .join("\n")
                : ""}
            </DetailInfo>
          </BasicInfoItem>
        </HospitalInfoSection>
      </Container>

      <FooterSlot>
        <Footer position="sticky">
          <Button
            width={360}
            buttonType={ButtonType.secondary}
            onClick={() => {
              dispatchHospitalState({
                type: HospitalStateActionType.PAGE_TYPE,
                payload: HospitalInfoPageType.SUBMENU,
              });
            }}
          >
            診療メニュー選択・予約
          </Button>
        </Footer>
      </FooterSlot>

      <DirectConfirmModal
        url={homePageUrl}
        onClose={() => setHomePageUrl(null)}
      />
    </HospitalContent>
  );
};

export default TopContentComponent;
